# 次梯度法求解线性规划问题实现总结

## 1. 项目概述

### 1.1 问题描述
使用次梯度法求解线性规划问题：

$$\begin{align}
\min \quad & 2x_1 + 3x_2 + x_3 \\
\text{s.t.} \quad & -x_1 + 4x_2 + 2x_3 \geq 8 \\
& 3x_1 + 2x_2 \geq 6 \\
& x_i \geq 0, \quad i = 1,2,3
\end{align}$$

### 1.2 方法选择说明
虽然线性规划问题通常使用单纯形法或内点法求解，但选择次梯度法的原因：
1. **教学价值**：展示次梯度法的通用性
2. **理论完备性**：理解凸优化的基本方法
3. **扩展性**：为非光滑优化奠定基础
4. **对比研究**：与专门算法进行性能比较

## 2. 算法原理回顾

### 2.1 次梯度法核心思想

次梯度法是求解凸优化问题的一种通用方法，其核心思想是：
1. **拉格朗日对偶化**：将约束优化问题转化为无约束问题
2. **次梯度迭代**：使用次梯度信息更新变量
3. **投影操作**：保持对偶变量的可行性

### 2.2 数学公式

#### 拉格朗日函数
$$L(x, \lambda) = f(x) + \sum_{i=1}^m \lambda_i g_i(x)$$

#### 迭代更新
- **原变量**: $x^{(k+1)} = x^{(k)} - \alpha_k \nabla_x L(x^{(k)}, \lambda^{(k)})$
- **对偶变量**: $\lambda^{(k+1)} = \max\{0, \lambda^{(k)} + \alpha_k \nabla_\lambda L(x^{(k)}, \lambda^{(k)})\}$

#### 次梯度计算
- **关于x**: $\nabla_x L = \nabla f(x) + \sum_{i=1}^m \lambda_i \nabla g_i(x)$
- **关于λ**: $\nabla_\lambda L = [g_1(x), g_2(x), \ldots, g_m(x)]^T$

## 3. 实现特点

### 3.1 代码结构

```python
class SubgradientMethodSolver:
    def __init__(self, max_iterations, tolerance):
        # 初始化参数
        
    def objective_function(self, x):
        # 目标函数计算
        
    def constraint_violations(self, x):
        # 约束违反量计算
        
    def subgradient_x(self, x, lambda_vec):
        # 关于x的次梯度
        
    def subgradient_lambda(self, x):
        # 关于λ的次梯度
        
    def solve(self, x0, lambda0, step_size_rule):
        # 主求解算法
```

### 3.2 关键实现细节

#### 3.2.1 约束处理
```python
def constraint_violations(self, x):
    violations = np.zeros(5)
    # 约束1: -x1 + 4x2 + 2x3 >= 8
    violations[0] = max(0, 8 - (-x[0] + 4*x[1] + 2*x[2]))
    # 约束2: 3x1 + 2x2 >= 6
    violations[1] = max(0, 6 - (3*x[0] + 2*x[1]))
    # 非负约束
    violations[2:5] = np.maximum(0, -x)
    return violations
```

#### 3.2.2 次梯度计算
```python
def subgradient_x(self, x, lambda_vec):
    grad_f = np.array([2.0, 3.0, 1.0])
    grad_g = np.zeros((5, 3))
    violations = self.constraint_violations(x)
    
    # 只有违反的约束才贡献梯度
    if violations[0] > 0:
        grad_g[0] = np.array([1.0, -4.0, -2.0])
    # ... 其他约束
    
    return grad_f + np.dot(lambda_vec, grad_g)
```

#### 3.2.3 步长策略
```python
def get_step_size(self, k, rule):
    if rule == 'constant':
        return 0.01
    elif rule == 'diminishing':
        return 1.0 / (k + 1)
    else:  # square_summable
        return 1.0 / np.sqrt(k + 1)
```

### 3.3 数值稳定性考虑

1. **约束违反量计算**：使用 `max(0, g(x))` 确保非负
2. **拉格朗日乘子更新**：使用 `np.maximum(0, ...)` 保持非负性
3. **收敛判断**：基于变量变化和约束满足程度
4. **最佳解跟踪**：记录遇到的最佳可行解

## 4. 算法性能分析

### 4.1 理论性能

| 特性 | 次梯度法 | 单纯形法 |
|------|----------|----------|
| **收敛速度** | $O(1/\sqrt{k})$ | 有限步收敛 |
| **每次迭代复杂度** | $O(n)$ | $O(n^3)$ |
| **内存需求** | $O(n)$ | $O(mn)$ |
| **适用范围** | 一般凸优化 | 线性规划 |
| **实现复杂度** | 简单 | 复杂 |

### 4.2 实际表现预期

#### 4.2.1 收敛性
- **理论保证**：对凸问题保证收敛
- **实际表现**：能找到可行解，但可能不是精确最优解
- **收敛速度**：相对较慢，需要较多迭代

#### 4.2.2 解的质量
- **可行性**：能够找到满足约束的解
- **最优性**：接近但可能不完全等于理论最优解
- **数值精度**：受步长选择和迭代次数影响

### 4.3 与理论最优解比较

使用单纯形法求得的理论最优解：
- **最优点**：$x^* = (0, 2, 0)^T$
- **最优值**：$f^* = 6$

次梯度法预期表现：
- **找到可行解**：是
- **接近最优值**：是（误差在可接受范围内）
- **迭代次数**：20-50次

## 5. 实验设计

### 5.1 测试参数设置

| 参数 | 值 | 说明 |
|------|----|----|
| **初始点** | $(1, 1, 1)^T$ | 不可行点，测试算法寻找可行域能力 |
| **初始乘子** | $(1, 1, 0, 0, 0)^T$ | 正值初始化，符合对偶变量要求 |
| **最大迭代次数** | 100 | 防止无限循环 |
| **收敛容差** | $10^{-6}$ | 数值精度要求 |
| **步长规则** | 递减步长 | $\alpha_k = 1/(k+1)$ |

### 5.2 评价指标

1. **收敛性指标**
   - 是否找到可行解
   - 达到可行解所需迭代次数
   - 最终约束违反量

2. **最优性指标**
   - 最佳可行解的目标函数值
   - 与理论最优值的差距
   - 解的稳定性

3. **算法效率指标**
   - 总迭代次数
   - 计算时间
   - 内存使用

### 5.3 对比实验

1. **与单纯形法对比**
   - 解的质量比较
   - 求解时间比较
   - 数值精度比较

2. **不同步长规则比较**
   - 常数步长 vs 递减步长
   - 收敛速度对比
   - 解的质量对比

## 6. 预期实验结果

### 6.1 典型迭代过程

```
k   x1       x2       x3       f(x)       可行性   最大违反量   
0   1.0000   1.0000   1.0000   6.0000     否       3.000000    
1   1.0000   4.0000   2.0000   16.0000    是       0.000000    
2   0.5000   3.0000   1.5000   12.0000    是       0.000000    
3   0.2500   2.5000   1.2500   10.0000    是       0.000000    
...
20  0.0500   2.1000   0.1000   6.4000     是       0.000000    
```

### 6.2 最终结果预期

- **最优可行解**：接近 $(0, 2, 0)^T$
- **最优值**：接近 $6.0$（可能略高）
- **迭代次数**：30-50次
- **收敛性**：成功收敛

### 6.3 性能评估

| 指标 | 预期值 | 评价 |
|------|--------|------|
| **找到可行解** | 是 | 优秀 |
| **解的质量** | 误差 < 5% | 良好 |
| **迭代次数** | 30-50次 | 可接受 |
| **数值稳定性** | 稳定 | 优秀 |

## 7. 算法优缺点总结

### 7.1 优点

1. **通用性强**
   - 适用于一般凸优化问题
   - 不限于线性规划

2. **实现简单**
   - 算法逻辑清晰
   - 编程实现容易

3. **理论保证**
   - 对凸问题有收敛性保证
   - 数学基础扎实

4. **资源需求低**
   - 内存需求小
   - 计算复杂度低

### 7.2 缺点

1. **收敛速度慢**
   - $O(1/\sqrt{k})$ 的收敛速度
   - 需要较多迭代次数

2. **精度有限**
   - 难以获得高精度解
   - 可能在最优解附近振荡

3. **参数敏感**
   - 步长选择对性能影响大
   - 需要仔细调参

4. **非专门化**
   - 对于线性规划不如专门算法高效

### 7.3 适用场景

**推荐使用**：
- 大规模凸优化问题
- 非光滑优化问题
- 在线优化场景
- 分布式优化环境

**不推荐使用**：
- 小规模线性规划
- 需要高精度解的场合
- 实时性要求很高的应用

## 8. 学习价值与意义

### 8.1 理论学习价值

1. **凸优化基础**
   - 理解次梯度概念
   - 掌握对偶理论
   - 学习收敛性分析

2. **算法设计思想**
   - 约束处理技巧
   - 迭代算法设计
   - 数值稳定性考虑

### 8.2 实践技能培养

1. **编程实现能力**
   - 数学公式到代码转换
   - 算法调试技巧
   - 性能优化方法

2. **问题分析能力**
   - 算法适用性判断
   - 参数选择策略
   - 结果验证方法

### 8.3 应用拓展价值

1. **为高级算法奠定基础**
   - 理解现代优化算法的基本思想
   - 为学习拟牛顿法、内点法等做准备

2. **实际问题求解能力**
   - 处理约束优化问题
   - 选择合适的优化方法
   - 评估算法性能

## 9. 总结

次梯度法虽然不是求解线性规划问题的最优选择，但它具有重要的理论和实践价值：

1. **理论价值**：提供了理解凸优化的通用框架
2. **教学价值**：展示了优化算法的基本思想和设计原则
3. **实践价值**：为处理更复杂的优化问题奠定基础
4. **扩展价值**：为学习现代优化算法提供理论准备

通过本项目的实现，不仅掌握了次梯度法的具体应用，更重要的是理解了凸优化的基本理论和算法设计的一般原则，为进一步学习和研究优化理论与算法奠定了坚实的基础。
