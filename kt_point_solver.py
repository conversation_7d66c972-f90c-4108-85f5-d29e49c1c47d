#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K-T点求解器
求解约束优化问题的<PERSON><PERSON>-<PERSON><PERSON>-<PERSON>条件

问题：
min f(x) = 2x₁² + 2x₁x₂ + x₂² - 10x₁ - 10x₂
s.t. c₁(x) = x₁² + x₂² ≤ 5
     c₂(x) = 3x₁ + x₂ ≤ 6
"""

import numpy as np
import sympy as sp
from scipy.optimize import minimize
import matplotlib.pyplot as plt
from matplotlib.patches import Circle
import warnings
warnings.filterwarnings('ignore')

def solve_kt_conditions():
    """
    求解K-T条件的完整步骤
    """
    print("=" * 100)
    print("K-T点求解器 - Ka<PERSON>-<PERSON><PERSON>-<PERSON>条件")
    print("=" * 100)
    print()

    print("【问题描述】")
    print("min f(x) = 2x₁² + 2x₁x₂ + x₂² - 10x₁ - 10x₂")
    print("s.t. c₁(x) = x₁² + x₂² ≤ 5")
    print("     c₂(x) = 3x₁ + x₂ ≤ 6")
    print()

    # 定义符号变量
    x1, x2, lam1, lam2 = sp.symbols('x1 x2 lambda1 lambda2', real=True)

    print("=" * 80)
    print("第一步：定义目标函数和约束函数")
    print("=" * 80)
    print()

    # 目标函数
    f = 2*x1**2 + 2*x1*x2 + x2**2 - 10*x1 - 10*x2
    print("目标函数：")
    print("f(x₁, x₂) =", f)
    print()

    # 约束函数（转换为标准形式 g(x) ≤ 0）
    g1 = x1**2 + x2**2 - 5  # c₁(x) - 5 ≤ 0
    g2 = 3*x1 + x2 - 6      # c₂(x) - 6 ≤ 0

    print("约束函数（标准形式 g(x) ≤ 0）：")
    print("g₁(x₁, x₂) = x₁² + x₂² - 5 ≤ 0")
    print("g₂(x₁, x₂) = 3x₁ + x₂ - 6 ≤ 0")
    print()

    print("=" * 80)
    print("第二步：计算梯度")
    print("=" * 80)
    print()

    # 计算梯度
    grad_f = [sp.diff(f, x1), sp.diff(f, x2)]
    grad_g1 = [sp.diff(g1, x1), sp.diff(g1, x2)]
    grad_g2 = [sp.diff(g2, x1), sp.diff(g2, x2)]

    print("目标函数梯度：")
    print("∇f = [∂f/∂x₁, ∂f/∂x₂] = [", grad_f[0], ",", grad_f[1], "]")
    print("   = [", sp.simplify(grad_f[0]), ",", sp.simplify(grad_f[1]), "]")
    print()

    print("约束函数梯度：")
    print("∇g₁ = [", grad_g1[0], ",", grad_g1[1], "]")
    print("∇g₂ = [", grad_g2[0], ",", grad_g2[1], "]")
    print()

    print("=" * 80)
    print("第三步：K-T条件")
    print("=" * 80)
    print()

    print("K-T条件包括：")
    print("1. 梯度条件（拉格朗日条件）：∇f + λ₁∇g₁ + λ₂∇g₂ = 0")
    print("2. 原始可行性：g₁(x) ≤ 0, g₂(x) ≤ 0")
    print("3. 对偶可行性：λ₁ ≥ 0, λ₂ ≥ 0")
    print("4. 互补松弛性：λ₁g₁(x) = 0, λ₂g₂(x) = 0")
    print()

    # K-T条件方程组
    kt_eq1 = grad_f[0] + lam1*grad_g1[0] + lam2*grad_g2[0]  # ∂L/∂x₁ = 0
    kt_eq2 = grad_f[1] + lam1*grad_g1[1] + lam2*grad_g2[1]  # ∂L/∂x₂ = 0

    print("梯度条件展开：")
    print("∂L/∂x₁ = ∂f/∂x₁ + λ₁∂g₁/∂x₁ + λ₂∂g₂/∂x₁ = 0")
    print("       =", kt_eq1, "= 0")
    print()
    print("∂L/∂x₂ = ∂f/∂x₂ + λ₁∂g₁/∂x₂ + λ₂∂g₂/∂x₂ = 0")
    print("       =", kt_eq2, "= 0")
    print()

    print("=" * 80)
    print("第四步：分情况求解K-T点")
    print("=" * 80)
    print()

    solutions = []

    print("根据互补松弛性条件，需要考虑以下情况：")
    print("情况1：λ₁ = 0, λ₂ = 0 （两个约束都不起作用）")
    print("情况2：λ₁ > 0, λ₂ = 0 （只有第一个约束起作用）")
    print("情况3：λ₁ = 0, λ₂ > 0 （只有第二个约束起作用）")
    print("情况4：λ₁ > 0, λ₂ > 0 （两个约束都起作用）")
    print()

    # 情况1：λ₁ = 0, λ₂ = 0
    print("【情况1：λ₁ = 0, λ₂ = 0】")
    eq1_case1 = kt_eq1.subs([(lam1, 0), (lam2, 0)])
    eq2_case1 = kt_eq2.subs([(lam1, 0), (lam2, 0)])

    print("梯度条件变为：")
    print("∂f/∂x₁ =", eq1_case1, "= 0")
    print("∂f/∂x₂ =", eq2_case1, "= 0")

    sol_case1 = sp.solve([eq1_case1, eq2_case1], [x1, x2])
    print("解得：x₁ =", sol_case1[x1], ", x₂ =", sol_case1[x2])

    # 检查可行性
    x1_val, x2_val = float(sol_case1[x1]), float(sol_case1[x2])
    g1_val = x1_val**2 + x2_val**2 - 5
    g2_val = 3*x1_val + x2_val - 6

    print("检查约束：")
    print("g₁ =", g1_val, "≤ 0 ?", "是" if g1_val <= 1e-10 else "否")
    print("g₂ =", g2_val, "≤ 0 ?", "是" if g2_val <= 1e-10 else "否")

    if g1_val <= 1e-10 and g2_val <= 1e-10:
        print("情况1可行，找到K-T点")
        solutions.append(("情况1", x1_val, x2_val, 0, 0))
    else:
        print("情况1不可行")
    print()

    # 情况2：λ₁ > 0, λ₂ = 0
    print("【情况2：λ₁ > 0, λ₂ = 0】")
    print("此时 g₁(x) = 0，即 x₁² + x₂² = 5")

    eq1_case2 = kt_eq1.subs(lam2, 0)
    eq2_case2 = kt_eq2.subs(lam2, 0)
    constraint_case2 = g1  # g₁ = 0

    print("方程组：")
    print("∂L/∂x₁ =", eq1_case2, "= 0")
    print("∂L/∂x₂ =", eq2_case2, "= 0")
    print("约束：", constraint_case2, "= 0")

    try:
        sol_case2 = sp.solve([eq1_case2, eq2_case2, constraint_case2], [x1, x2, lam1])
        if sol_case2:
            for sol in sol_case2:
                if len(sol) == 3:
                    x1_val, x2_val, lam1_val = float(sol[0]), float(sol[1]), float(sol[2])
                    g2_val = 3*x1_val + x2_val - 6

                    print(f"解：x₁ = {x1_val:.6f}, x₂ = {x2_val:.6f}, λ₁ = {lam1_val:.6f}")
                    print(f"检查：λ₁ = {lam1_val:.6f} ≥ 0 ? {'是' if lam1_val >= -1e-10 else '否'}")
                    print(f"检查：g₂ = {g2_val:.6f} ≤ 0 ? {'是' if g2_val <= 1e-10 else '否'}")

                    if lam1_val >= -1e-10 and g2_val <= 1e-10:
                        print("情况2可行，找到K-T点")
                        solutions.append(("情况2", x1_val, x2_val, lam1_val, 0))
                    else:
                        print("此解不满足K-T条件")
        else:
            print("情况2无解")
    except:
        print("情况2求解失败")
    print()

    # 情况3：λ₁ = 0, λ₂ > 0
    print("【情况3：λ₁ = 0, λ₂ > 0】")
    print("此时 g₂(x) = 0，即 3x₁ + x₂ = 6")

    eq1_case3 = kt_eq1.subs(lam1, 0)
    eq2_case3 = kt_eq2.subs(lam1, 0)
    constraint_case3 = g2  # g₂ = 0

    print("方程组：")
    print("∂L/∂x₁ =", eq1_case3, "= 0")
    print("∂L/∂x₂ =", eq2_case3, "= 0")
    print("约束：", constraint_case3, "= 0")

    try:
        sol_case3 = sp.solve([eq1_case3, eq2_case3, constraint_case3], [x1, x2, lam2])
        if sol_case3:
            if isinstance(sol_case3, dict):
                sol_case3 = [sol_case3]

            for sol in sol_case3:
                if isinstance(sol, dict):
                    x1_val = float(sol[x1])
                    x2_val = float(sol[x2])
                    lam2_val = float(sol[lam2])
                else:
                    x1_val, x2_val, lam2_val = float(sol[0]), float(sol[1]), float(sol[2])

                g1_val = x1_val**2 + x2_val**2 - 5

                print(f"解：x₁ = {x1_val:.6f}, x₂ = {x2_val:.6f}, λ₂ = {lam2_val:.6f}")
                print(f"检查：λ₂ = {lam2_val:.6f} ≥ 0 ? {'是' if lam2_val >= -1e-10 else '否'}")
                print(f"检查：g₁ = {g1_val:.6f} ≤ 0 ? {'是' if g1_val <= 1e-10 else '否'}")

                if lam2_val >= -1e-10 and g1_val <= 1e-10:
                    print("情况3可行，找到K-T点")
                    solutions.append(("情况3", x1_val, x2_val, 0, lam2_val))
                else:
                    print("此解不满足K-T条件")
        else:
            print("情况3无解")
    except:
        print("情况3求解失败")
    print()

    # 情况4：λ₁ > 0, λ₂ > 0
    print("【情况4：λ₁ > 0, λ₂ > 0】")
    print("此时 g₁(x) = 0 且 g₂(x) = 0")
    print("即：x₁² + x₂² = 5 且 3x₁ + x₂ = 6")

    try:
        # 从约束条件求解 x₁, x₂
        constraint_eq1 = sp.Eq(x1**2 + x2**2, 5)
        constraint_eq2 = sp.Eq(3*x1 + x2, 6)

        sol_constraints = sp.solve([constraint_eq1, constraint_eq2], [x1, x2])

        if sol_constraints:
            for sol in sol_constraints:
                x1_val, x2_val = float(sol[0]), float(sol[1])

                # 计算对应的拉格朗日乘子
                # 从 K-T 条件求解 λ₁, λ₂
                kt_eq1_val = kt_eq1.subs([(x1, x1_val), (x2, x2_val)])
                kt_eq2_val = kt_eq2.subs([(x1, x1_val), (x2, x2_val)])

                lam_sol = sp.solve([kt_eq1_val, kt_eq2_val], [lam1, lam2])

                if lam_sol:
                    lam1_val = float(lam_sol[lam1])
                    lam2_val = float(lam_sol[lam2])

                    print(f"解：x₁ = {x1_val:.6f}, x₂ = {x2_val:.6f}")
                    print(f"    λ₁ = {lam1_val:.6f}, λ₂ = {lam2_val:.6f}")
                    print(f"检查：λ₁ = {lam1_val:.6f} ≥ 0 ? {'是' if lam1_val >= -1e-10 else '否'}")
                    print(f"检查：λ₂ = {lam2_val:.6f} ≥ 0 ? {'是' if lam2_val >= -1e-10 else '否'}")

                    if lam1_val >= -1e-10 and lam2_val >= -1e-10:
                        print("情况4可行，找到K-T点")
                        solutions.append(("情况4", x1_val, x2_val, lam1_val, lam2_val))
                    else:
                        print("此解不满足K-T条件")
        else:
            print("情况4：约束条件无交点")
    except:
        print("情况4求解失败")
    print()

    return solutions

def verify_and_compare_solutions(solutions):
    """
    验证解并比较目标函数值
    """
    print("=" * 80)
    print("第五步：验证K-T点并比较目标函数值")
    print("=" * 80)
    print()

    if not solutions:
        print("未找到K-T点")
        return

    print("找到的K-T点：")
    print()

    best_solution = None
    best_value = float('inf')

    for i, (case, x1_val, x2_val, lam1_val, lam2_val) in enumerate(solutions):
        # 计算目标函数值
        f_val = 2*x1_val**2 + 2*x1_val*x2_val + x2_val**2 - 10*x1_val - 10*x2_val

        print(f"K-T点 {i+1} ({case}):")
        print(f"  x₁ = {x1_val:.6f}, x₂ = {x2_val:.6f}")
        print(f"  λ₁ = {lam1_val:.6f}, λ₂ = {lam2_val:.6f}")
        print(f"  目标函数值：f(x) = {f_val:.6f}")

        # 验证K-T条件
        print("  K-T条件验证：")

        # 原始可行性
        g1_val = x1_val**2 + x2_val**2 - 5
        g2_val = 3*x1_val + x2_val - 6
        print(f"    原始可行性：g₁ = {g1_val:.8f} ≤ 0 {'✓' if g1_val <= 1e-6 else '✗'}")
        print(f"                g₂ = {g2_val:.8f} ≤ 0 {'✓' if g2_val <= 1e-6 else '✗'}")

        # 对偶可行性
        print(f"    对偶可行性：λ₁ = {lam1_val:.8f} ≥ 0 {'✓' if lam1_val >= -1e-6 else '✗'}")
        print(f"                λ₂ = {lam2_val:.8f} ≥ 0 {'✓' if lam2_val >= -1e-6 else '✗'}")

        # 互补松弛性
        comp1 = lam1_val * g1_val
        comp2 = lam2_val * g2_val
        print(f"    互补松弛性：λ₁g₁ = {comp1:.8f} = 0 {'✓' if abs(comp1) <= 1e-6 else '✗'}")
        print(f"                λ₂g₂ = {comp2:.8f} = 0 {'✓' if abs(comp2) <= 1e-6 else '✗'}")

        # 梯度条件
        grad_L1 = 4*x1_val + 2*x2_val - 10 + lam1_val*2*x1_val + lam2_val*3
        grad_L2 = 2*x1_val + 2*x2_val - 10 + lam1_val*2*x2_val + lam2_val*1
        print(f"    梯度条件：∇L₁ = {grad_L1:.8f} = 0 {'✓' if abs(grad_L1) <= 1e-6 else '✗'}")
        print(f"              ∇L₂ = {grad_L2:.8f} = 0 {'✓' if abs(grad_L2) <= 1e-6 else '✗'}")

        if f_val < best_value:
            best_value = f_val
            best_solution = (case, x1_val, x2_val, lam1_val, lam2_val, f_val)

        print()

    if best_solution:
        case, x1_val, x2_val, lam1_val, lam2_val, f_val = best_solution
        print("【最优K-T点】")
        print(f"情况：{case}")
        print(f"最优解：x₁* = {x1_val:.6f}, x₂* = {x2_val:.6f}")
        print(f"拉格朗日乘子：λ₁* = {lam1_val:.6f}, λ₂* = {lam2_val:.6f}")
        print(f"最优值：f(x*) = {f_val:.6f}")

def numerical_verification():
    """
    使用数值方法验证结果
    """
    print("\n" + "=" * 80)
    print("第六步：数值方法验证")
    print("=" * 80)
    print()

    # 定义目标函数
    def objective(x):
        return 2*x[0]**2 + 2*x[0]*x[1] + x[1]**2 - 10*x[0] - 10*x[1]

    # 定义约束
    def constraint1(x):
        return 5 - (x[0]**2 + x[1]**2)  # g₁ ≤ 0 转换为 ≥ 0

    def constraint2(x):
        return 6 - (3*x[0] + x[1])      # g₂ ≤ 0 转换为 ≥ 0

    constraints = [
        {'type': 'ineq', 'fun': constraint1},
        {'type': 'ineq', 'fun': constraint2}
    ]

    # 多个初始点
    initial_points = [(0, 0), (1, 1), (2, 0), (0, 2), (1.5, 1.5)]

    print("使用scipy.optimize.minimize验证：")
    print()

    best_result = None
    best_value = float('inf')

    for i, x0 in enumerate(initial_points):
        result = minimize(objective, x0, method='SLSQP', constraints=constraints)

        if result.success:
            print(f"初始点 {x0}: x* = [{result.x[0]:.6f}, {result.x[1]:.6f}], f* = {result.fun:.6f}")

            if result.fun < best_value:
                best_value = result.fun
                best_result = result
        else:
            print(f"初始点 {x0}: 优化失败")

    if best_result:
        print()
        print("数值优化最优解：")
        print(f"x* = [{best_result.x[0]:.6f}, {best_result.x[1]:.6f}]")
        print(f"f* = {best_result.fun:.6f}")

def visualize_problem():
    """
    可视化问题和解
    """
    print("\n" + "=" * 80)
    print("第七步：问题可视化")
    print("=" * 80)
    print()

    try:
        import matplotlib.pyplot as plt
        import numpy as np
        from matplotlib.patches import Circle

        # 创建网格
        x1 = np.linspace(-1, 4, 400)
        x2 = np.linspace(-1, 4, 400)
        X1, X2 = np.meshgrid(x1, x2)

        # 目标函数
        F = 2*X1**2 + 2*X1*X2 + X2**2 - 10*X1 - 10*X2

        # 约束函数
        G1 = X1**2 + X2**2 - 5  # ≤ 0
        G2 = 3*X1 + X2 - 6      # ≤ 0

        plt.figure(figsize=(12, 10))

        # 绘制目标函数等高线
        contour = plt.contour(X1, X2, F, levels=20, colors='blue', alpha=0.6)
        plt.clabel(contour, inline=True, fontsize=8)

        # 绘制约束区域
        # 约束1: x₁² + x₂² ≤ 5 (圆形区域)
        circle = Circle((0, 0), np.sqrt(5), fill=False, color='red', linewidth=2, label='x₁² + x₂² = 5')
        plt.gca().add_patch(circle)

        # 约束2: 3x₁ + x₂ ≤ 6 (直线下方)
        x1_line = np.linspace(-1, 4, 100)
        x2_line = 6 - 3*x1_line
        plt.plot(x1_line, x2_line, 'green', linewidth=2, label='3x₁ + x₂ = 6')

        # 填充可行域
        # 创建掩码
        feasible = (X1**2 + X2**2 <= 5) & (3*X1 + X2 <= 6)
        plt.contourf(X1, X2, feasible.astype(int), levels=[0.5, 1.5], colors=['lightblue'], alpha=0.3)

        # 标记K-T点
        plt.plot(1, 2, 'ro', markersize=10, label='K-T点 (1, 2)')
        plt.annotate('K-T点\n(1, 2)\nf* = -20', xy=(1, 2), xytext=(1.5, 2.5),
                    arrowprops=dict(arrowstyle='->', color='red'),
                    fontsize=12, ha='center')

        # 标记无约束最优点
        plt.plot(0, 5, 'ko', markersize=8, label='无约束最优点 (0, 5)')
        plt.annotate('无约束最优点\n(0, 5)\n不可行', xy=(0, 5), xytext=(-0.8, 5.5),
                    arrowprops=dict(arrowstyle='->', color='black'),
                    fontsize=10, ha='center')

        plt.xlim(-1, 4)
        plt.ylim(-1, 4)
        plt.xlabel('x₁')
        plt.ylabel('x₂')
        plt.title('K-T点求解可视化\nmin f(x) = 2x₁² + 2x₁x₂ + x₂² - 10x₁ - 10x₂')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.axis('equal')

        # 保存图片
        plt.savefig('kt_point_visualization.png', dpi=300, bbox_inches='tight')
        print("可视化图片已保存为 'kt_point_visualization.png'")

        # 显示图片
        plt.show()

    except ImportError:
        print("matplotlib未安装，跳过可视化")

if __name__ == "__main__":
    print("K-T点求解器")
    print("求解约束优化问题的Karush-Kuhn-Tucker条件")
    print()

    # 求解K-T条件
    solutions = solve_kt_conditions()

    # 验证和比较解
    verify_and_compare_solutions(solutions)

    # 数值验证
    numerical_verification()

    # 可视化
    visualize_problem()

    print("\n" + "=" * 100)
    print("总结")
    print("=" * 100)
    print("本程序完整展示了K-T点的求解过程：")
    print("1. 建立K-T条件方程组")
    print("2. 根据互补松弛性分情况讨论")
    print("3. 求解每种情况下的候选点")
    print("4. 验证K-T条件的满足情况")
    print("5. 比较目标函数值确定最优解")
    print("6. 使用数值方法验证结果")
    print("7. 可视化问题和解")
