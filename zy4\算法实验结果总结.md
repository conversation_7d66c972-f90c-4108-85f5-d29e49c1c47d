# 三种优化算法实验结果总结

## 1. 实验概述

### 1.1 问题设置
- **目标函数**: $f(x) = 4(x_1+1)^2 + 2(x_2-1)^2 + x_1 + x_2 + 10$
- **初始点**: $x^{(0)} = (0, 0)^T$
- **收敛精度**: $\varepsilon = 10^{-5}$
- **理论最优解**: $x^* = (-1.125, 0.75)^T$, $f(x^*) = 9.8125$

### 1.2 测试算法
1. **最速下降法** (Steepest Descent Method)
2. **牛顿法** (Newton's Method)
3. **共轭梯度法** (Conjugate Gradient Method)

## 2. 实验结果

### 2.1 收敛过程对比

#### 最速下降法（10次迭代）
| k | x₁ | x₂ | f(x) | ‖∇f‖ |
|---|----|----|------|------|
| 0 | 0.000000 | 0.000000 | 16.000000 | 9.487e+00 |
| 1 | -1.184211 | 0.394737 | 10.078947 | 1.498e+00 |
| 2 | -1.076555 | 0.717703 | 9.823974 | 4.085e-01 |
| 3 | -1.127550 | 0.734702 | 9.812994 | 6.450e-02 |
| 4 | -1.122914 | 0.748609 | 9.812521 | 1.759e-02 |
| 5 | -1.125110 | 0.749341 | 9.812501 | 2.778e-03 |
| 6 | -1.124910 | 0.749940 | 9.812500 | 7.575e-04 |
| 7 | -1.125005 | 0.749972 | 9.812500 | 1.196e-04 |
| 8 | -1.124996 | 0.749997 | 9.812500 | 3.262e-05 |
| 9 | -1.125000 | 0.749999 | 9.812500 | 5.151e-06 |

#### 牛顿法（2次迭代）
| k | x₁ | x₂ | f(x) | ‖∇f‖ |
|---|----|----|------|------|
| 0 | 0.000000 | 0.000000 | 16.000000 | 9.487e+00 |
| 1 | -1.125000 | 0.750000 | 9.812500 | 0.000e+00 |

#### 共轭梯度法（3次迭代）
| k | x₁ | x₂ | f(x) | ‖∇f‖ |
|---|----|----|------|------|
| 0 | 0.000000 | 0.000000 | 16.000000 | 9.487e+00 |
| 1 | -1.184211 | 0.394737 | 10.078947 | 1.498e+00 |
| 2 | -1.125000 | 0.750000 | 9.812500 | 0.000e+00 |

### 2.2 性能比较表

| 算法 | 迭代次数 | 最优值 | 误差 | 计算时间(s) | 收敛性 |
|------|----------|--------|------|-------------|--------|
| 最速下降法 | 10 | 9.812500 | 1.238e-06 | 0.013369 | ✓ |
| 牛顿法 | 2 | 9.812500 | 0.000e+00 | 0.006619 | ✓ |
| 共轭梯度法 | 3 | 9.812500 | 0.000e+00 | 0.000000 | ✓ |
| 理论值 | - | 9.812500 | 0.000e+00 | - | - |

## 3. 结果分析

### 3.1 收敛速度分析

1. **牛顿法最快**: 仅需2次迭代（包括初始点），实际上1次牛顿步即收敛
2. **共轭梯度法次之**: 需要3次迭代，符合理论预期（对二次函数n步收敛）
3. **最速下降法最慢**: 需要10次迭代，体现了线性收敛的特点

### 3.2 精度分析

1. **牛顿法和共轭梯度法**: 达到机器精度（误差为0）
2. **最速下降法**: 误差约为1.238e-06，满足收敛精度要求

### 3.3 计算效率分析

1. **共轭梯度法计算最快**: 时间几乎为0（可能由于问题规模小）
2. **牛顿法中等**: 0.006619秒
3. **最速下降法最慢**: 0.013369秒（由于迭代次数多）

## 4. 理论验证

### 4.1 收敛率验证

#### 最速下降法
- **理论收敛率**: $\rho = \frac{\kappa-1}{\kappa+1} = \frac{2-1}{2+1} = \frac{1}{3} \approx 0.333$
- **实际观察**: 梯度范数按约1/3的比率递减，与理论一致

#### 牛顿法
- **理论预期**: 对二次函数一步收敛
- **实际结果**: 确实一步收敛到精确解

#### 共轭梯度法
- **理论预期**: 对二次函数最多n步收敛（n=2）
- **实际结果**: 2步收敛，符合理论预期

### 4.2 算法特性验证

| 特性 | 最速下降法 | 牛顿法 | 共轭梯度法 |
|------|------------|--------|------------|
| **理论收敛次数** | O(κ log(1/ε)) | 1次 | n次 |
| **实际收敛次数** | 10次 | 1次 | 2次 |
| **理论符合度** | ✓ | ✓ | ✓ |

## 5. 算法优缺点实证

### 5.1 最速下降法
**优点验证**:
- ✓ 实现简单，代码最短
- ✓ 内存需求最低
- ✓ 数值稳定，始终收敛

**缺点验证**:
- ✗ 收敛速度最慢（10次迭代）
- ✗ 对病态问题敏感（虽然本问题条件数较小）

### 5.2 牛顿法
**优点验证**:
- ✓ 收敛速度最快（1次迭代）
- ✓ 精度最高（机器精度）
- ✓ 对二次函数表现完美

**缺点验证**:
- ✗ 需要计算和存储Hessian矩阵
- ✗ 每次迭代计算量大（虽然总体最快）

### 5.3 共轭梯度法
**优点验证**:
- ✓ 收敛速度快（2次迭代）
- ✓ 不需要存储Hessian矩阵
- ✓ 内存需求适中
- ✓ 精度高（机器精度）

**缺点验证**:
- ✗ 实现相对复杂（需要共轭方向计算）

## 6. 共轭梯度法详细分析

### 6.1 手工计算验证

**第一次迭代**:
- 初始梯度: $\nabla f(x^{(0)}) = [9, -3]^T$
- 初始方向: $d^{(0)} = [-9, 3]^T$
- 步长: $\alpha^{(0)} = 0.131579$
- 更新点: $x^{(1)} = [-1.184211, 0.394737]^T$

**第二次迭代**:
- 新梯度: $\nabla f(x^{(1)}) = [-0.474, -1.421]^T$
- 共轭系数: $\beta^{(1)} = 0.024931$
- 新方向: $d^{(1)} = [0.249, 1.496]^T$

### 6.2 共轭性验证

验证方向的共轭性：$d^{(0)T} H d^{(1)} \approx 0$

其中 $H = \begin{bmatrix} 8 & 0 \\ 0 & 4 \end{bmatrix}$

## 7. 实际应用建议

### 7.1 算法选择指南

基于实验结果，推荐以下选择策略：

1. **小规模二次或近似二次问题**: 
   - 首选：牛顿法（最快收敛）
   - 次选：共轭梯度法（平衡性能）

2. **中大规模问题**:
   - 首选：共轭梯度法（内存高效）
   - 备选：最速下降法（简单可靠）

3. **内存受限环境**:
   - 首选：最速下降法（最低内存需求）

4. **高精度要求**:
   - 首选：牛顿法或共轭梯度法（机器精度）

### 7.2 工程实践考虑

1. **实现复杂度**: 最速下降法 < 牛顿法 < 共轭梯度法
2. **调试难度**: 最速下降法 < 牛顿法 < 共轭梯度法
3. **数值稳定性**: 三种方法在本问题上都表现良好
4. **可扩展性**: 最速下降法 > 共轭梯度法 > 牛顿法

## 8. 结论

### 8.1 主要发现

1. **理论与实践高度一致**: 所有算法的实际表现都符合理论预期
2. **牛顿法在二次函数上无可匹敌**: 一步收敛的优势明显
3. **共轭梯度法是最佳平衡**: 在收敛速度和计算成本间找到最优平衡
4. **最速下降法简单可靠**: 虽然慢但稳定，适合作为基准

### 8.2 学习价值

1. **算法理解**: 通过实验深入理解三种算法的本质差异
2. **性能分析**: 学会从多个维度评估算法性能
3. **实践技能**: 掌握算法实现和调试技巧
4. **选择策略**: 建立算法选择的决策框架

### 8.3 未来扩展

1. **非二次函数测试**: 验证算法在一般函数上的表现
2. **大规模问题**: 测试算法的可扩展性
3. **病态问题**: 研究算法在高条件数问题上的鲁棒性
4. **混合策略**: 探索结合多种算法优点的混合方法

本实验成功验证了三种经典优化算法的理论性质，为实际应用中的算法选择提供了有价值的参考。
