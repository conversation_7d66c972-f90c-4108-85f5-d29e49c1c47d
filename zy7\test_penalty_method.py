#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外罚函数法测试脚本
"""

import numpy as np
from scipy.optimize import minimize

def objective_function(x):
    """目标函数 f(x) = x1^2 + 4x2^2 - 2x1 - x2"""
    return x[0]**2 + 4*x[1]**2 - 2*x[0] - x[1]

def constraint_function(x):
    """约束函数 g(x) = x1 + x2 - 1"""
    return x[0] + x[1] - 1

def penalty_function(x, r):
    """外罚函数 P(x,r) = f(x) + r * max(0, g(x))^2"""
    f_val = objective_function(x)
    g_val = constraint_function(x)
    penalty_term = r * max(0, g_val)**2
    return f_val + penalty_term

def penalty_gradient(x, r):
    """罚函数梯度"""
    # 原函数梯度: ∇f = [2x1 - 2, 8x2 - 1]
    grad_f = np.array([2*x[0] - 2, 8*x[1] - 1])
    
    # 约束函数梯度: ∇g = [1, 1]
    grad_g = np.array([1, 1])
    
    # 约束函数值
    g_val = constraint_function(x)
    
    # 罚函数梯度
    if g_val > 0:
        grad_penalty = grad_f + 2 * r * g_val * grad_g
    else:
        grad_penalty = grad_f
        
    return grad_penalty

def solve_analytical():
    """解析求解"""
    print("解析解计算:")
    print("=" * 50)
    
    print("1. 无约束最优解:")
    print("   ∇f = [2x1 - 2, 8x2 - 1] = 0")
    print("   解得: x1 = 1, x2 = 1/8 = 0.125")
    
    x_unconstrained = np.array([1.0, 0.125])
    f_unconstrained = objective_function(x_unconstrained)
    g_unconstrained = constraint_function(x_unconstrained)
    
    print(f"   无约束最优解: x = [{x_unconstrained[0]:.6f}, {x_unconstrained[1]:.6f}]")
    print(f"   目标函数值: f = {f_unconstrained:.6f}")
    print(f"   约束函数值: g = {g_unconstrained:.6f}")
    print(f"   约束满足: {'是' if g_unconstrained <= 0 else '否'}")
    
    if g_unconstrained > 0:
        print("\n2. 约束起作用，使用拉格朗日乘数法:")
        print("   L = x1² + 4x2² - 2x1 - x2 + λ(x1 + x2 - 1)")
        print("   KKT条件:")
        print("   ∇L = [2x1 - 2 + λ, 8x2 - 1 + λ] = 0")
        print("   x1 + x2 = 1 (约束起作用)")
        print("   λ ≥ 0")
        
        print("\n   求解:")
        print("   2x1 - 2 + λ = 0  =>  x1 = 1 - λ/2")
        print("   8x2 - 1 + λ = 0  =>  x2 = (1 - λ)/8")
        print("   x1 + x2 = 1  =>  (1 - λ/2) + (1 - λ)/8 = 1")
        print("   解得: λ = 8/5 = 1.6")
        
        lambda_val = 8/5
        x1_constrained = 1 - lambda_val/2
        x2_constrained = (1 - lambda_val)/8
        x_constrained = np.array([x1_constrained, x2_constrained])
        f_constrained = objective_function(x_constrained)
        
        print(f"\n   约束最优解: x* = [{x1_constrained:.6f}, {x2_constrained:.6f}]")
        print(f"   最优值: f* = {f_constrained:.6f}")
        print(f"   拉格朗日乘数: λ* = {lambda_val:.6f}")
        
        return x_constrained, f_constrained
    else:
        return x_unconstrained, f_unconstrained

def simple_penalty_method(x0, r0=1.0, r_factor=10.0, max_iterations=10, tolerance=1e-6):
    """简化的外罚函数法实现"""
    print("\n外罚函数法求解:")
    print("=" * 50)
    print(f"初始点: x0 = {x0}")
    print(f"初始罚参数: r0 = {r0}")
    print(f"罚参数增长因子: {r_factor}")
    print("-" * 50)
    print(f"{'k':<3} {'r':<10} {'x1':<10} {'x2':<10} {'f(x)':<12} {'g(x)':<12} {'P(x,r)':<12} {'可行性':<8}")
    print("-" * 50)
    
    x = np.array(x0, dtype=float)
    r = r0
    
    for k in range(max_iterations):
        # 定义当前罚函数
        def current_penalty(x_var):
            return penalty_function(x_var, r)
        
        def current_gradient(x_var):
            return penalty_gradient(x_var, r)
        
        # 求解无约束子问题
        result = minimize(current_penalty, x, method='BFGS', jac=current_gradient, 
                         options={'gtol': tolerance/10})
        
        if not result.success:
            print(f"第{k}次迭代：子问题求解失败")
            break
        
        x_new = result.x
        f_val = objective_function(x_new)
        g_val = constraint_function(x_new)
        p_val = penalty_function(x_new, r)
        is_feasible = g_val <= tolerance
        
        print(f"{k:<3} {r:<10.2e} {x_new[0]:<10.6f} {x_new[1]:<10.6f} "
              f"{f_val:<12.6f} {g_val:<12.6e} {p_val:<12.6f} {'是' if is_feasible else '否':<8}")
        
        # 检查收敛
        if abs(g_val) < tolerance or (is_feasible and abs(g_val) < tolerance):
            print(f"算法在第{k}次迭代收敛")
            break
        
        # 更新
        x = x_new
        r *= r_factor
    
    print("-" * 50)
    print(f"最终解: x* = [{x[0]:.6f}, {x[1]:.6f}]")
    print(f"最优值: f* = {f_val:.6f}")
    print(f"约束值: g(x*) = {g_val:.6e}")
    print(f"可行性: {'是' if abs(g_val) < tolerance else '否'}")
    
    return x, f_val

def manual_calculation_demo():
    """手工计算演示"""
    print("\n手工计算第一次迭代演示:")
    print("=" * 50)
    
    x0 = np.array([0.0, 0.0])
    r = 1.0
    
    print(f"初始点: x0 = {x0}")
    print(f"罚参数: r = {r}")
    
    # 计算初始罚函数值和梯度
    f_val = objective_function(x0)
    g_val = constraint_function(x0)
    p_val = penalty_function(x0, r)
    grad_p = penalty_gradient(x0, r)
    
    print(f"\n在初始点:")
    print(f"目标函数值: f(x0) = {f_val:.6f}")
    print(f"约束函数值: g(x0) = {g_val:.6f}")
    print(f"罚函数值: P(x0,r) = {p_val:.6f}")
    print(f"罚函数梯度: ∇P(x0,r) = [{grad_p[0]:.6f}, {grad_p[1]:.6f}]")
    
    print(f"\n约束违反: {'是' if g_val > 0 else '否'}")
    if g_val > 0:
        print("需要施加惩罚")
    else:
        print("无需惩罚，等价于无约束问题")

def test_different_starting_points():
    """测试不同初始点"""
    print("\n不同初始点测试:")
    print("=" * 50)
    
    starting_points = [
        np.array([0.0, 0.0]),   # 可行点
        np.array([2.0, 2.0]),   # 不可行点
        np.array([-1.0, 0.5]),  # 可行点
        np.array([1.5, 1.5])    # 不可行点
    ]
    
    for i, x0 in enumerate(starting_points):
        print(f"\n初始点 {i+1}: x0 = [{x0[0]:.1f}, {x0[1]:.1f}]")
        g_val = constraint_function(x0)
        print(f"约束值: g(x0) = {g_val:.6f}")
        print(f"可行性: {'是' if g_val <= 0 else '否'}")
        
        # 简单的一次迭代
        x_result, f_result = simple_penalty_method(x0, max_iterations=3, tolerance=1e-4)

def compare_with_scipy():
    """与scipy的约束优化比较"""
    print("\n与scipy约束优化比较:")
    print("=" * 50)
    
    try:
        from scipy.optimize import minimize
        
        # 定义约束
        constraint = {'type': 'ineq', 'fun': lambda x: 1 - x[0] - x[1]}
        
        # 使用SLSQP方法求解
        x0 = np.array([0.0, 0.0])
        result = minimize(objective_function, x0, method='SLSQP', constraints=constraint)
        
        if result.success:
            print("scipy SLSQP方法:")
            print(f"最优解: x* = [{result.x[0]:.6f}, {result.x[1]:.6f}]")
            print(f"最优值: f* = {result.fun:.6f}")
            print(f"约束值: g(x*) = {constraint_function(result.x):.6e}")
            print(f"迭代次数: {result.nit}")
        else:
            print("scipy求解失败")
    except ImportError:
        print("scipy未安装，无法进行对比")

def algorithm_analysis():
    """算法特性分析"""
    print("\n算法特性分析:")
    print("=" * 50)
    
    print("外罚函数法特点:")
    print("优点:")
    print("  ✓ 概念简单，易于理解和实现")
    print("  ✓ 不需要可行初始点")
    print("  ✓ 适用于各种类型的约束")
    print("  ✓ 有理论收敛保证")
    
    print("\n缺点:")
    print("  ✗ 当罚参数很大时，问题变得病态")
    print("  ✗ 收敛速度相对较慢")
    print("  ✗ 难以获得高精度解")
    print("  ✗ 参数选择影响性能")
    
    print("\n适用场景:")
    print("  • 教学和研究")
    print("  • 中小规模问题")
    print("  • 原型开发")
    print("  • 为其他算法提供初始解")

def main():
    """主函数"""
    print("外罚函数法求解约束优化问题")
    print("问题: min f(x) = x1² + 4x2² - 2x1 - x2")
    print("约束: x1 + x2 ≤ 1")
    
    # 解析解
    x_analytical, f_analytical = solve_analytical()
    
    # 手工计算演示
    manual_calculation_demo()
    
    # 外罚函数法求解
    x0 = np.array([0.0, 0.0])
    x_penalty, f_penalty = simple_penalty_method(x0)
    
    # 结果比较
    print(f"\n结果比较:")
    print(f"解析解:     x* = [{x_analytical[0]:.6f}, {x_analytical[1]:.6f}], f* = {f_analytical:.6f}")
    print(f"外罚函数法: x* = [{x_penalty[0]:.6f}, {x_penalty[1]:.6f}], f* = {f_penalty:.6f}")
    error = np.linalg.norm(x_penalty - x_analytical)
    print(f"解的误差: ||x_penalty - x_analytical|| = {error:.6e}")
    
    # 不同初始点测试
    test_different_starting_points()
    
    # scipy对比
    compare_with_scipy()
    
    # 算法分析
    algorithm_analysis()

if __name__ == "__main__":
    main()
