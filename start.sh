#!/bin/bash

echo "================================================"
echo "医疗智能问答系统 - Flask版本"
echo "================================================"
echo

echo "正在检查Python环境..."
python3 --version
if [ $? -ne 0 ]; then
    echo "错误: 未找到Python，请先安装Python 3.8+"
    exit 1
fi

echo
echo "正在检查依赖包..."
python3 -c "import flask, py2neo, torch, transformers" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "警告: 缺少必要的依赖包"
    echo "正在安装依赖包..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误: 依赖包安装失败"
        exit 1
    fi
fi

echo
echo "正在启动Flask应用..."
python3 run_flask.py
