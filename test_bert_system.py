#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BERT模型系统测试脚本
验证基于BERT的医疗问答系统是否正常工作
"""

import os
import sys
import importlib.util

def test_bert_model():
    """测试BERT模型加载"""
    print("测试BERT模型...")

    try:
        import torch
        print("  ✓ PyTorch可用")

        from transformers import BertTokenizer
        print("  ✓ Transformers可用")

        # 检查模型文件
        model_path = 'model/best_roberta_rnn_model_ent_aug.pt'
        if os.path.exists(model_path):
            model_size = os.path.getsize(model_path) / (1024 * 1024)
            print(f"  ✓ BERT模型文件存在 ({model_size:.1f}MB)")
        else:
            print("  ✗ BERT模型文件不存在")
            return False

        # 检查ner_model模块
        if os.path.exists('ner_model.py'):
            print("  ✓ NER模型模块存在")
        else:
            print("  ✗ NER模型模块不存在")
            return False

        return True

    except ImportError as e:
        print(f"  ✗ 依赖缺失: {e}")
        return False

def test_neo4j_connection():
    """测试Neo4j连接"""
    print("测试Neo4j连接...")

    try:
        import py2neo
        print("  ✓ py2neo可用")

        client = py2neo.Graph(
            'bolt://localhost:7687',
            user='neo4j',
            password='wangqi20040401',
            name='doctor'
        )

        # 测试连接
        result = client.run("RETURN 1 as test").data()
        if result:
            print("  ✓ Neo4j连接成功")

            # 测试数据
            count_query = "MATCH (n) RETURN count(n) as total"
            count_result = client.run(count_query).data()
            if count_result:
                total_nodes = count_result[0]['total']
                print(f"  ✓ 数据库包含 {total_nodes} 个节点")

            return True
        else:
            print("  ✗ Neo4j连接失败")
            return False

    except Exception as e:
        print(f"  ✗ Neo4j连接错误: {e}")
        return False

def test_chat_module():
    """测试聊天模块"""
    print("测试聊天模块...")

    try:
        # 测试导入
        sys.path.append('apps')
        from apps.chat_app import chat_bp, intent_recognition, get_chat_entities
        print("  ✓ 聊天模块导入成功")

        # 测试意图识别
        test_query = "感冒有什么症状"
        intent = intent_recognition(test_query)
        print(f"  ✓ 意图识别: '{test_query}' -> {intent}")

        return True

    except Exception as e:
        print(f"  ✗ 聊天模块测试失败: {e}")
        return False

def test_auth_module():
    """测试认证模块"""
    print("测试认证模块...")

    try:
        from apps.auth_app import auth_bp, User, create_user
        print("  ✓ 认证模块导入成功")

        # 测试用户创建
        success, message = create_user("test_user", "test123", False)
        if success:
            print("  ✓ 用户创建功能正常")
        else:
            print(f"  ✓ 用户创建功能正常 ({message})")

        return True

    except Exception as e:
        print(f"  ✗ 认证模块测试失败: {e}")
        return False

def test_main_app():
    """测试主应用"""
    print("测试主应用...")

    try:
        from app import app
        print("  ✓ 主应用导入成功")

        # 检查蓝图注册
        blueprints = list(app.blueprints.keys())
        print(f"  ✓ 已注册蓝图: {blueprints}")

        # 检查路由
        routes = []
        for rule in app.url_map.iter_rules():
            if rule.endpoint != 'static':
                routes.append(rule.rule)

        print(f"  ✓ 可用路由数量: {len(routes)}")

        return True

    except Exception as e:
        print(f"  ✗ 主应用测试失败: {e}")
        return False

def test_templates():
    """测试模板文件"""
    print("测试模板文件...")

    required_templates = [
        'templates/base.html',
        'templates/index.html',
        'templates/login.html',
        'templates/chat.html',
        'templates/knowledge_graph.html',
        'templates/admin.html'
    ]

    missing_templates = []
    for template in required_templates:
        if os.path.exists(template):
            print(f"  ✓ {template}")
        else:
            print(f"  ✗ {template} (缺失)")
            missing_templates.append(template)

    return len(missing_templates) == 0

def main():
    """主测试函数"""
    print("=" * 60)
    print("医疗智能问答系统 - BERT模型系统测试")
    print("=" * 60)

    tests = [
        ("BERT模型", test_bert_model),
        ("Neo4j连接", test_neo4j_connection),
        ("聊天模块", test_chat_module),
        ("认证模块", test_auth_module),
        ("主应用", test_main_app),
        ("模板文件", test_templates)
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n{test_name}测试:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ✗ 测试异常: {e}")
            results.append((test_name, False))

    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\n总计: {passed}/{total} 项测试通过")

    if passed == total:
        print("\n🎉 所有测试通过！BERT模型系统工作正常")
        print("现在可以运行: python app.py")
        return True
    else:
        print(f"\n⚠️ {total - passed} 项测试失败，请检查上述问题")
        return False

if __name__ == '__main__':
    main()
