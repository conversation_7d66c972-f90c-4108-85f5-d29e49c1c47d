{% extends "base.html" %}

{% block title %}帮助中心 - 医疗智能问答系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <!-- 左侧导航 -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-question-circle me-2"></i>帮助分类</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="#getting-started" class="list-group-item list-group-item-action" onclick="showSection('getting-started')">
                        <i class="fas fa-play me-2"></i>快速开始
                    </a>
                    <a href="#features" class="list-group-item list-group-item-action" onclick="showSection('features')">
                        <i class="fas fa-star me-2"></i>功能介绍
                    </a>
                    <a href="#faq" class="list-group-item list-group-item-action" onclick="showSection('faq')">
                        <i class="fas fa-question me-2"></i>常见问题
                    </a>
                    <a href="#contact" class="list-group-item list-group-item-action" onclick="showSection('contact')">
                        <i class="fas fa-envelope me-2"></i>联系我们
                    </a>
                </div>
            </div>

            <!-- 快速搜索 -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-search me-2"></i>搜索帮助</h6>
                </div>
                <div class="card-body">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="搜索问题..." id="searchInput">
                        <button class="btn btn-outline-primary" onclick="searchHelp()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧内容 -->
        <div class="col-md-9">
            <!-- 快速开始 -->
            <div id="getting-started" class="help-section">
                <div class="card mb-4">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-play me-2"></i>快速开始</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>1. 注册账户</h5>
                                <p>首次使用需要注册账户，填写用户名和密码即可快速注册。</p>
                                
                                <h5>2. 开始提问</h5>
                                <p>登录后点击"智能问答"，输入您的医疗相关问题，AI会为您提供专业解答。</p>
                                
                                <h5>3. 浏览知识图谱</h5>
                                <p>在"知识图谱"页面可以可视化浏览医疗知识网络，了解疾病、症状、治疗方法之间的关系。</p>
                            </div>
                            <div class="col-md-6">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-lightbulb me-1"></i>使用技巧</h6>
                                    <ul class="mb-0">
                                        <li>问题描述越详细，回答越准确</li>
                                        <li>可以询问症状、疾病、治疗方法等</li>
                                        <li>支持中文自然语言提问</li>
                                        <li>建议先查看知识图谱了解相关概念</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 功能介绍 -->
            <div id="features" class="help-section" style="display: none;">
                <div class="card mb-4">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-star me-2"></i>功能介绍</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="feature-item">
                                    <h5><i class="fas fa-comments text-primary me-2"></i>智能问答</h5>
                                    <p>基于BERT模型的医疗问答系统，支持症状咨询、疾病查询、治疗建议等多种问题类型。</p>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="feature-item">
                                    <h5><i class="fas fa-project-diagram text-success me-2"></i>知识图谱</h5>
                                    <p>可视化医疗知识网络，展示疾病、症状、药物、治疗方法之间的复杂关系。</p>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="feature-item">
                                    <h5><i class="fas fa-user-circle text-info me-2"></i>个人中心</h5>
                                    <p>管理个人资料、查看使用统计、修改账户设置、接收系统消息。</p>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="feature-item">
                                    <h5><i class="fas fa-shield-alt text-warning me-2"></i>安全保障</h5>
                                    <p>密码加密存储、会话管理、权限控制，确保用户数据安全。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 常见问题 -->
            <div id="faq" class="help-section" style="display: none;">
                <div class="card mb-4">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-question me-2"></i>常见问题</h4>
                    </div>
                    <div class="card-body">
                        <div class="accordion" id="faqAccordion">
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="faq1">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                        如何提高问答的准确性？
                                    </button>
                                </h2>
                                <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        <ul>
                                            <li>详细描述症状，包括持续时间、严重程度等</li>
                                            <li>提供相关的病史信息</li>
                                            <li>使用准确的医学术语</li>
                                            <li>一次只问一个具体问题</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="accordion-item">
                                <h2 class="accordion-header" id="faq2">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                        系统支持哪些类型的医疗问题？
                                    </button>
                                </h2>
                                <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        <ul>
                                            <li>常见疾病的症状和治疗</li>
                                            <li>药物使用方法和注意事项</li>
                                            <li>健康保健和预防措施</li>
                                            <li>医学检查和诊断解释</li>
                                            <li>急救和应急处理</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="accordion-item">
                                <h2 class="accordion-header" id="faq3">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                        忘记密码怎么办？
                                    </button>
                                </h2>
                                <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        目前系统暂不支持自动密码重置功能。如果忘记密码，请联系系统管理员重置密码。
                                        未来版本将支持邮箱验证重置密码功能。
                                    </div>
                                </div>
                            </div>

                            <div class="accordion-item">
                                <h2 class="accordion-header" id="faq4">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
                                        如何查看我的使用历史？
                                    </button>
                                </h2>
                                <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        在个人资料页面可以查看您的使用统计，包括提问次数、会话时长、常用话题等信息。
                                        详细的问答历史记录功能正在开发中。
                                    </div>
                                </div>
                            </div>

                            <div class="accordion-item">
                                <h2 class="accordion-header" id="faq5">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse5">
                                        系统的回答可以替代医生诊断吗？
                                    </button>
                                </h2>
                                <div id="collapse5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        <div class="alert alert-warning">
                                            <strong>重要提醒：</strong>本系统仅提供医疗信息参考，不能替代专业医生的诊断和治疗建议。
                                            如有严重症状或紧急情况，请立即就医。
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 联系我们 -->
            <div id="contact" class="help-section" style="display: none;">
                <div class="card mb-4">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-envelope me-2"></i>联系我们</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>技术支持</h5>
                                <p><i class="fas fa-envelope me-2"></i><EMAIL></p>
                                <p><i class="fas fa-phone me-2"></i>************</p>
                                <p><i class="fas fa-clock me-2"></i>工作时间：周一至周五 9:00-18:00</p>
                                
                                <h5 class="mt-4">反馈建议</h5>
                                <p>如果您在使用过程中遇到问题或有改进建议，欢迎通过以下方式联系我们：</p>
                                <ul>
                                    <li>邮箱：<EMAIL></li>
                                    <li>QQ群：123456789</li>
                                    <li>微信群：扫描右侧二维码</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <div class="text-center">
                                    <h6>微信交流群</h6>
                                    <img src="https://via.placeholder.com/200x200/007bff/ffffff?text=QR+Code" 
                                         class="img-fluid" alt="微信群二维码" style="max-width: 200px;">
                                    <p class="mt-2 text-muted">扫码加入用户交流群</p>
                                </div>
                                
                                <div class="alert alert-info mt-4">
                                    <h6><i class="fas fa-info-circle me-1"></i>版本信息</h6>
                                    <p class="mb-1">当前版本：v2.1.0</p>
                                    <p class="mb-1">更新时间：2024-01-20</p>
                                    <p class="mb-0">技术栈：Flask + BERT + Neo4j</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 显示指定的帮助部分
function showSection(sectionId) {
    // 隐藏所有部分
    $('.help-section').hide();
    
    // 显示指定部分
    $('#' + sectionId).show();
    
    // 更新导航状态
    $('.list-group-item').removeClass('active');
    $('a[href="#' + sectionId + '"]').addClass('active');
}

// 搜索帮助内容
function searchHelp() {
    const query = $('#searchInput').val().toLowerCase();
    if (!query) {
        showAlert('warning', '请输入搜索关键词');
        return;
    }
    
    // 简单的搜索实现
    let found = false;
    $('.help-section').each(function() {
        const content = $(this).text().toLowerCase();
        if (content.includes(query)) {
            $(this).show();
            found = true;
        } else {
            $(this).hide();
        }
    });
    
    if (!found) {
        showAlert('info', '未找到相关内容，请尝试其他关键词');
    } else {
        $('.list-group-item').removeClass('active');
    }
}

// 页面加载时显示快速开始
$(document).ready(function() {
    showSection('getting-started');
    
    // 搜索框回车事件
    $('#searchInput').keypress(function(e) {
        if (e.which === 13) {
            searchHelp();
        }
    });
});

// 显示提示消息
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container').prepend(alertHtml);
    
    // 3秒后自动消失
    setTimeout(() => {
        $('.alert').alert('close');
    }, 3000);
}
</script>

<style>
.feature-item {
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    height: 100%;
}

.feature-item h5 {
    margin-bottom: 10px;
}

.help-section {
    min-height: 400px;
}

.list-group-item.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}
</style>
{% endblock %}
