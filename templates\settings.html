{% extends "base.html" %}

{% block title %}账户设置 - 医疗智能问答系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <!-- 左侧导航 -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>用户中心</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('auth.profile_page') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-user-circle me-2"></i>个人资料
                    </a>
                    <a href="{{ url_for('auth.settings_page') }}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-cog me-2"></i>账户设置
                    </a>
                    <a href="{{ url_for('auth.messages_page') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-bell me-2"></i>消息中心
                    </a>
                    <a href="{{ url_for('auth.help_page') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-question-circle me-2"></i>帮助中心
                    </a>
                </div>
            </div>
        </div>

        <!-- 右侧内容 -->
        <div class="col-md-9">
            <!-- 密码修改 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-key me-2"></i>修改密码</h5>
                </div>
                <div class="card-body">
                    <form id="passwordForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">当前密码</label>
                                    <input type="password" class="form-control" id="oldPassword" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">新密码</label>
                                    <input type="password" class="form-control" id="newPassword" required>
                                    <div class="form-text">密码长度至少6个字符</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">确认新密码</label>
                                    <input type="password" class="form-control" id="confirmPassword" required>
                                </div>
                                <button type="button" class="btn btn-primary" onclick="changePassword()">
                                    <i class="fas fa-save me-1"></i>修改密码
                                </button>
                            </div>
                            <div class="col-md-6">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-1"></i>密码安全提示</h6>
                                    <ul class="mb-0">
                                        <li>密码长度至少6个字符</li>
                                        <li>建议包含字母、数字和特殊字符</li>
                                        <li>不要使用过于简单的密码</li>
                                        <li>定期更换密码</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 个人偏好设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-sliders-h me-2"></i>个人偏好</h5>
                </div>
                <div class="card-body">
                    <form id="preferencesForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">界面主题</label>
                                    <select class="form-select" id="theme">
                                        <option value="light">浅色主题</option>
                                        <option value="dark">深色主题</option>
                                        <option value="auto">跟随系统</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">语言设置</label>
                                    <select class="form-select" id="language">
                                        <option value="zh-CN">简体中文</option>
                                        <option value="zh-TW">繁体中文</option>
                                        <option value="en-US">English</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">通知设置</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="notifications">
                                        <label class="form-check-label" for="notifications">
                                            启用系统通知
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="emailNotifications">
                                        <label class="form-check-label" for="emailNotifications">
                                            启用邮件通知
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-end">
                            <button type="button" class="btn btn-primary" onclick="updatePreferences()">
                                <i class="fas fa-save me-1"></i>保存设置
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 隐私设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-shield-alt me-2"></i>隐私设置</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="profilePublic">
                                <label class="form-check-label" for="profilePublic">
                                    公开个人资料
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="activityVisible">
                                <label class="form-check-label" for="activityVisible">
                                    显示活动状态
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="dataCollection">
                                <label class="form-check-label" for="dataCollection">
                                    允许数据收集用于改进服务
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="analytics">
                                <label class="form-check-label" for="analytics">
                                    允许使用分析
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="text-end">
                        <button type="button" class="btn btn-primary" onclick="updatePrivacySettings()">
                            <i class="fas fa-save me-1"></i>保存隐私设置
                        </button>
                    </div>
                </div>
            </div>

            <!-- 账户操作 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>危险操作</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-warning me-1"></i>注意</h6>
                        <p class="mb-0">以下操作不可逆，请谨慎操作</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-warning" onclick="clearData()">
                            <i class="fas fa-trash me-1"></i>清空个人数据
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteAccount()">
                            <i class="fas fa-user-times me-1"></i>删除账户
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载时获取用户设置
$(document).ready(function() {
    loadUserPreferences();
});

// 加载用户偏好设置
function loadUserPreferences() {
    fetch('/api/user/profile')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.user.preferences) {
                const prefs = data.user.preferences;
                $('#theme').val(prefs.theme || 'light');
                $('#language').val(prefs.language || 'zh-CN');
                $('#notifications').prop('checked', prefs.notifications !== false);
                $('#emailNotifications').prop('checked', prefs.email_notifications === true);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

// 修改密码
function changePassword() {
    const oldPassword = $('#oldPassword').val();
    const newPassword = $('#newPassword').val();
    const confirmPassword = $('#confirmPassword').val();

    if (!oldPassword || !newPassword || !confirmPassword) {
        showAlert('warning', '请填写所有密码字段');
        return;
    }

    if (newPassword !== confirmPassword) {
        showAlert('warning', '新密码和确认密码不匹配');
        return;
    }

    if (newPassword.length < 6) {
        showAlert('warning', '新密码长度至少6个字符');
        return;
    }

    fetch('/api/user/change-password', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            old_password: oldPassword,
            new_password: newPassword
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            $('#passwordForm')[0].reset();
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', '修改密码失败，请重试');
    });
}

// 更新偏好设置
function updatePreferences() {
    const preferences = {
        theme: $('#theme').val(),
        language: $('#language').val(),
        notifications: $('#notifications').is(':checked'),
        email_notifications: $('#emailNotifications').is(':checked')
    };

    fetch('/api/user/update-preferences', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ preferences })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', '保存设置失败，请重试');
    });
}

// 更新隐私设置
function updatePrivacySettings() {
    showAlert('info', '隐私设置功能开发中...');
}

// 清空个人数据
function clearData() {
    if (confirm('确定要清空所有个人数据吗？此操作不可逆！')) {
        showAlert('info', '清空数据功能开发中...');
    }
}

// 删除账户
function deleteAccount() {
    if (confirm('确定要删除账户吗？此操作不可逆，将永久删除您的所有数据！')) {
        showAlert('info', '删除账户功能开发中...');
    }
}

// 显示提示消息
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container').prepend(alertHtml);
    
    // 3秒后自动消失
    setTimeout(() => {
        $('.alert').alert('close');
    }, 3000);
}
</script>
{% endblock %}
