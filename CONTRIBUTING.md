# 贡献指南

感谢您对DoctorRAG项目的关注！我们欢迎所有形式的贡献，包括但不限于：

- 🐛 报告Bug
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码修复
- ✨ 添加新功能
- 🧪 编写测试用例

## 🚀 快速开始

### 开发环境设置

1. **Fork项目**
```bash
# 在GitHub上Fork项目到您的账户
# 然后克隆到本地
git clone https://github.com/your-username/doctorRAG.git
cd doctorRAG
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
pip install -r requirements-dev.txt  # 开发依赖
```

4. **配置环境**
```bash
cp .env.example .env
# 编辑.env文件，配置您的环境变量
```

5. **运行测试**
```bash
python -m pytest tests/
```

## 📋 贡献流程

### 1. 创建Issue

在开始编码之前，请先创建一个Issue来描述：
- 要修复的Bug
- 要添加的新功能
- 要改进的文档

### 2. 创建分支

```bash
git checkout -b feature/your-feature-name
# 或
git checkout -b bugfix/your-bug-fix
# 或
git checkout -b docs/your-doc-improvement
```

### 3. 编写代码

- 遵循项目的代码风格
- 添加必要的测试
- 更新相关文档
- 确保所有测试通过

### 4. 提交代码

```bash
git add .
git commit -m "feat: add new feature description"
# 或
git commit -m "fix: fix bug description"
# 或
git commit -m "docs: update documentation"
```

### 5. 推送分支

```bash
git push origin feature/your-feature-name
```

### 6. 创建Pull Request

在GitHub上创建Pull Request，并：
- 详细描述您的更改
- 关联相关的Issue
- 添加适当的标签

## 📝 代码规范

### Python代码风格

我们使用以下工具来保持代码质量：

```bash
# 代码格式化
black .

# 代码检查
flake8 .

# 类型检查
mypy .

# 导入排序
isort .
```

### 提交信息规范

使用[约定式提交](https://www.conventionalcommits.org/zh-hans/)格式：

```
<类型>[可选的作用域]: <描述>

[可选的正文]

[可选的脚注]
```

**类型包括：**
- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码格式修改
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例：**
```
feat(chat): add voice input support

Add voice recognition functionality to the chat interface.
Users can now speak their questions instead of typing.

Closes #123
```

## 🧪 测试指南

### 运行测试

```bash
# 运行所有测试
python -m pytest

# 运行特定测试文件
python -m pytest tests/test_chat.py

# 运行带覆盖率的测试
python -m pytest --cov=apps tests/

# 运行性能测试
python -m pytest tests/performance/
```

### 编写测试

- 为新功能编写单元测试
- 为API端点编写集成测试
- 确保测试覆盖率不低于80%

### 测试文件结构

```
tests/
├── unit/           # 单元测试
├── integration/    # 集成测试
├── performance/    # 性能测试
└── fixtures/       # 测试数据
```

## 📚 文档贡献

### 文档类型

- **README.md**: 项目概述和快速开始
- **API文档**: API接口说明
- **用户指南**: 详细使用说明
- **开发者文档**: 开发相关文档

### 文档规范

- 使用Markdown格式
- 添加适当的代码示例
- 包含必要的截图
- 保持语言简洁明了

## 🐛 Bug报告

### Bug报告模板

```markdown
## Bug描述
简要描述遇到的问题

## 复现步骤
1. 进入...
2. 点击...
3. 看到错误...

## 期望行为
描述您期望发生的情况

## 实际行为
描述实际发生的情况

## 环境信息
- 操作系统: [例如 Windows 10]
- Python版本: [例如 3.8.5]
- 浏览器: [例如 Chrome 91]

## 附加信息
添加任何其他有助于解决问题的信息
```

## 💡 功能建议

### 功能建议模板

```markdown
## 功能描述
简要描述建议的功能

## 问题背景
描述这个功能要解决的问题

## 解决方案
描述您建议的解决方案

## 替代方案
描述您考虑过的其他解决方案

## 附加信息
添加任何其他相关信息
```

## 🔍 代码审查

### 审查清单

- [ ] 代码符合项目规范
- [ ] 包含适当的测试
- [ ] 文档已更新
- [ ] 没有引入新的安全漏洞
- [ ] 性能没有明显下降
- [ ] 向后兼容性

### 审查流程

1. 自动化检查通过
2. 至少一位维护者审查
3. 所有讨论得到解决
4. 合并到主分支

## 🏷️ 发布流程

### 版本发布

1. 更新版本号
2. 更新CHANGELOG.md
3. 创建发布标签
4. 发布到PyPI（如适用）

### 版本号规则

遵循[语义化版本](https://semver.org/lang/zh-CN/)：
- 主版本号：不兼容的API修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

## 📞 联系我们

如果您有任何问题，可以通过以下方式联系我们：

- 📧 邮箱: <EMAIL>
- 💬 讨论: [GitHub Discussions](https://github.com/your-username/doctorRAG/discussions)
- 🐛 问题: [GitHub Issues](https://github.com/your-username/doctorRAG/issues)

## 📄 许可证

通过贡献代码，您同意您的贡献将在[MIT许可证](LICENSE)下发布。

---

再次感谢您的贡献！🎉
