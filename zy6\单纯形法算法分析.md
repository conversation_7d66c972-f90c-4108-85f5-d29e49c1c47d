# 单纯形法求解线性规划问题详细分析

## 1. 问题描述

### 1.1 线性规划问题
求解线性规划问题：

$$\begin{align}
\min \quad & 2x_1 + 3x_2 + x_3 \\
\text{s.t.} \quad & -x_1 + 4x_2 + 2x_3 \geq 8 \\
& 3x_1 + 2x_2 \geq 6 \\
& x_i \geq 0, \quad i = 1,2,3
\end{align}$$

### 1.2 问题特点
- **问题类型**：线性规划问题
- **变量维数**：3维
- **约束数量**：5个（2个不等式约束 + 3个非负约束）
- **目标函数**：线性函数
- **约束函数**：线性不等式约束

## 2. 单纯形法原理

### 2.1 基本思想

单纯形法是求解线性规划问题的经典算法，其核心思想是：

1. **几何解释**：线性规划的最优解位于可行域的顶点（极点）
2. **代数解释**：从一个基可行解移动到相邻的更优基可行解
3. **迭代过程**：通过主元操作在基可行解之间移动
4. **最优性判断**：当所有非基变量的检验数非负时达到最优

### 2.2 标准形式转换

#### 2.2.1 原问题转换
将原问题转换为标准形式：

$$\begin{align}
\min \quad & 2x_1 + 3x_2 + x_3 \\
\text{s.t.} \quad & x_1 - 4x_2 - 2x_3 + s_1 = -8 \\
& -3x_1 - 2x_2 + s_2 = -6 \\
& x_i \geq 0, \quad s_j \geq 0
\end{align}$$

**注意**：为了处理 $\geq$ 约束，我们：
1. 将不等式乘以 $-1$ 转换为 $\leq$ 形式
2. 添加松弛变量 $s_1, s_2$
3. 得到的右端向量为负值，实际需要两阶段法处理

#### 2.2.2 矩阵形式
标准形式的矩阵表示：

$$\min \mathbf{c}^T \mathbf{x} \quad \text{s.t.} \quad \mathbf{A}\mathbf{x} = \mathbf{b}, \quad \mathbf{x} \geq 0$$

其中：
$$\mathbf{c} = \begin{bmatrix} 2 \\ 3 \\ 1 \\ 0 \\ 0 \end{bmatrix}, \quad 
\mathbf{A} = \begin{bmatrix} 1 & -4 & -2 & 1 & 0 \\ -3 & -2 & 0 & 0 & 1 \end{bmatrix}, \quad 
\mathbf{b} = \begin{bmatrix} -8 \\ -6 \end{bmatrix}$$

### 2.3 单纯形表

#### 2.3.1 单纯形表结构
单纯形表是求解过程中的核心数据结构：

| 基变量 | $x_1$ | $x_2$ | $x_3$ | $s_1$ | $s_2$ | RHS |
|--------|-------|-------|-------|-------|-------|-----|
| $s_1$  | $a_{11}$ | $a_{12}$ | $a_{13}$ | $a_{14}$ | $a_{15}$ | $b_1$ |
| $s_2$  | $a_{21}$ | $a_{22}$ | $a_{23}$ | $a_{24}$ | $a_{25}$ | $b_2$ |
| $z$    | $c_1$ | $c_2$ | $c_3$ | $c_4$ | $c_5$ | $z_0$ |

#### 2.3.2 初始单纯形表
对于本问题的初始表：

| 基变量 | $x_1$ | $x_2$ | $x_3$ | $s_1$ | $s_2$ | RHS |
|--------|-------|-------|-------|-------|-------|-----|
| $s_1$  | 1     | -4    | -2    | 1     | 0     | -8  |
| $s_2$  | -3    | -2    | 0     | 0     | 1     | -6  |
| $z$    | -2    | -3    | -1    | 0     | 0     | 0   |

## 3. 算法步骤

### 3.1 单纯形法迭代步骤

#### 步骤1：最优性检验
检查目标函数行的系数：
- 如果所有系数 $\geq 0$，则当前解为最优解
- 否则，选择最负的系数对应的变量作为进入变量

#### 步骤2：选择进入变量
选择目标函数行中最负系数对应的变量：
$$\text{进入变量} = \arg\min_j \{c_j : c_j < 0\}$$

#### 步骤3：比值测试（选择离开变量）
对于进入变量对应的列，计算比值：
$$\theta_i = \frac{b_i}{a_{ik}} \quad \text{for } a_{ik} > 0$$

选择最小比值对应的行：
$$\text{离开变量} = \arg\min_i \{\theta_i : \theta_i \geq 0\}$$

#### 步骤4：主元操作
以选定的主元素进行行变换：
1. 主元行归一化
2. 其他行消元，使主元列成为单位列

#### 步骤5：更新基变量
更新基变量列表，重复步骤1-4直到最优

### 3.2 主元操作详解

设主元素为 $a_{rs}$，主元操作包括：

#### 3.2.1 主元行归一化
$$\text{新主元行} = \frac{\text{原主元行}}{a_{rs}}$$

#### 3.2.2 其他行消元
对于第 $i$ 行（$i \neq r$）：
$$\text{新第i行} = \text{原第i行} - a_{is} \times \text{新主元行}$$

### 3.3 数学公式

#### 3.3.1 基可行解
设基变量索引集合为 $B$，非基变量索引集合为 $N$，则：
- 基变量：$\mathbf{x}_B = \mathbf{B}^{-1}\mathbf{b}$
- 非基变量：$\mathbf{x}_N = \mathbf{0}$

#### 3.3.2 检验数计算
非基变量 $x_j$ 的检验数：
$$\sigma_j = c_j - \mathbf{c}_B^T \mathbf{B}^{-1} \mathbf{A}_j$$

#### 3.3.3 目标函数值
当前基可行解的目标函数值：
$$z = \mathbf{c}_B^T \mathbf{x}_B = \mathbf{c}_B^T \mathbf{B}^{-1} \mathbf{b}$$

## 4. 伪代码

```
算法：单纯形法
输入：系数矩阵A, 目标函数系数c, 右端向量b
输出：最优解x*, 最优值z*

1. 初始化
   构造初始单纯形表
   设置初始基变量B = {s1, s2, ...}
   
2. 主循环
   while True do
       // 最优性检验
       if 目标函数行所有系数 >= 0 then
           return 当前解为最优解
       end if
       
       // 选择进入变量
       进入变量k = argmin{cj : cj < 0}
       
       // 比值测试
       计算比值 θi = bi/aik for aik > 0
       if 所有aik <= 0 then
           return 问题无界
       end if
       
       离开变量r = argmin{θi : θi >= 0}
       
       // 主元操作
       主元素 = ark
       主元行归一化: 第r行 = 第r行 / ark
       
       for i = 1 to m do
           if i ≠ r then
               第i行 = 第i行 - aik * 第r行
           end if
       end for
       
       // 更新基变量
       B[r] = k
   end while
   
3. 提取解
   从最终单纯形表提取最优解和最优值
```

## 5. 复杂度分析

### 5.1 时间复杂度

#### 5.1.1 每次迭代复杂度
- **主元选择**：$O(n)$
- **比值测试**：$O(m)$
- **主元操作**：$O(mn)$
- **总计**：$O(mn)$ 每次迭代

#### 5.1.2 迭代次数
- **平均情况**：$O(m)$ 到 $O(n)$ 次迭代
- **最坏情况**：指数级（极少发生）
- **实际表现**：通常在 $2m$ 到 $3m$ 次迭代内收敛

#### 5.1.3 总时间复杂度
- **平均**：$O(m^2 n)$ 到 $O(mn^2)$
- **最坏**：指数级

### 5.2 空间复杂度

- **单纯形表**：$O(mn)$
- **辅助数组**：$O(m + n)$
- **总计**：$O(mn)$

## 6. 算法特点

### 6.1 优点

1. **理论完备**：有严格的数学理论支撑
2. **精确解**：能得到精确的最优解
3. **有限收敛**：理论上有限步收敛
4. **广泛适用**：适用于所有线性规划问题
5. **敏感性分析**：容易进行敏感性分析

### 6.2 缺点

1. **最坏情况复杂度高**：指数级时间复杂度
2. **数值稳定性**：可能出现数值误差累积
3. **退化问题**：可能出现循环（通过反循环规则解决）
4. **内存需求**：需要存储完整的单纯形表

### 6.3 适用场景

**最适合**：
- 中小规模线性规划问题
- 需要精确解的场合
- 需要敏感性分析的情况
- 理论研究和教学

**不适合**：
- 超大规模问题（使用内点法）
- 非线性优化问题
- 实时性要求极高的应用

## 7. 两阶段法

### 7.1 问题分析

本问题的初始基解不可行（RHS有负值），实际求解需要两阶段法：

#### 第一阶段：寻找初始可行解
构造辅助问题：
$$\begin{align}
\min \quad & w_1 + w_2 \\
\text{s.t.} \quad & x_1 - 4x_2 - 2x_3 + s_1 + w_1 = -8 \\
& -3x_1 - 2x_2 + s_2 + w_2 = -6 \\
& x_i, s_j, w_k \geq 0
\end{align}$$

#### 第二阶段：求解原问题
如果第一阶段的最优值为0，则找到了原问题的初始可行解。

### 7.2 大M法

另一种处理方法是大M法：
$$\begin{align}
\min \quad & 2x_1 + 3x_2 + x_3 + M w_1 + M w_2 \\
\text{s.t.} \quad & x_1 - 4x_2 - 2x_3 + s_1 + w_1 = -8 \\
& -3x_1 - 2x_2 + s_2 + w_2 = -6 \\
& x_i, s_j, w_k \geq 0
\end{align}$$

其中 $M$ 是一个很大的正数。

## 8. 手工计算示例

### 8.1 初始单纯形表

| 基变量 | $x_1$ | $x_2$ | $x_3$ | $s_1$ | $s_2$ | RHS |
|--------|-------|-------|-------|-------|-------|-----|
| $s_1$  | 1     | -4    | -2    | 1     | 0     | -8  |
| $s_2$  | -3    | -2    | 0     | 0     | 1     | -6  |
| $z$    | -2    | -3    | -1    | 0     | 0     | 0   |

### 8.2 第一次迭代

**步骤1**：选择进入变量
目标函数行最负系数是 $-3$，对应 $x_2$，所以 $x_2$ 进入基。

**步骤2**：比值测试
- 第1行：$\frac{-8}{-4} = 2$（负比值，不考虑）
- 第2行：$\frac{-6}{-2} = 3$（负比值，不考虑）

**问题**：所有比值都是负的，这表明当前基解不可行。

### 8.3 实际求解

由于初始解不可行，我们需要使用两阶段法或大M法。为了演示，我们假设已经通过两阶段法找到了初始可行解。

## 9. 与次梯度法比较

### 9.1 算法特性对比

| 特性 | 单纯形法 | 次梯度法 |
|------|----------|----------|
| **适用问题** | 线性规划 | 一般凸优化 |
| **收敛速度** | 有限步收敛 | $O(1/\sqrt{k})$ |
| **每次迭代复杂度** | $O(mn)$ | $O(n)$ |
| **总时间复杂度** | $O(m^2n)$ 平均 | $O(1/\varepsilon^2)$ |
| **空间复杂度** | $O(mn)$ | $O(n)$ |
| **解的精度** | 精确解 | 近似解 |
| **实现复杂度** | 复杂 | 简单 |
| **数值稳定性** | 可能有问题 | 较好 |

### 9.2 收敛性比较

#### 9.2.1 单纯形法
- **收敛类型**：有限步收敛
- **理论保证**：对非退化问题保证有限步收敛
- **实际表现**：通常 $2m$ 到 $3m$ 次迭代
- **精度**：机器精度

#### 9.2.2 次梯度法
- **收敛类型**：次线性收敛
- **收敛速度**：$O(1/\sqrt{k})$
- **实际表现**：需要大量迭代
- **精度**：依赖于迭代次数和步长选择

### 9.3 性能预期

对于本问题的性能预期：

| 指标 | 单纯形法 | 次梯度法 |
|------|----------|----------|
| **迭代次数** | 2-5次 | 50-100次 |
| **计算时间** | 快 | 慢 |
| **最优值精度** | 精确 | 近似 |
| **内存使用** | 中等 | 低 |
| **实现难度** | 高 | 低 |

### 9.4 适用场景对比

#### 单纯形法适合：
- **线性规划问题**
- **需要精确解**
- **中小规模问题**
- **敏感性分析**
- **理论研究**

#### 次梯度法适合：
- **大规模问题**
- **非光滑优化**
- **在线优化**
- **分布式计算**
- **快速原型**

## 10. Python实现要点

### 10.1 核心数据结构

```python
class SimplexMethodSolver:
    def __init__(self):
        self.c = None          # 目标函数系数
        self.A = None          # 约束矩阵
        self.b = None          # 右端向量
        self.basic_vars = []   # 基变量索引
        self.tableau = None    # 单纯形表
```

### 10.2 关键算法实现

#### 10.2.1 主元选择
```python
def find_pivot_column(self, tableau):
    obj_row = tableau[-1, :-1]  # 目标函数行
    min_val = np.min(obj_row)
    if min_val >= -self.tolerance:
        return None  # 已最优
    return np.argmin(obj_row)
```

#### 10.2.2 比值测试
```python
def find_pivot_row(self, tableau, pivot_col):
    m, n = tableau.shape
    rhs = tableau[:-1, -1]
    pivot_column = tableau[:-1, pivot_col]

    ratios = []
    for i in range(m-1):
        if pivot_column[i] > self.tolerance:
            ratios.append(rhs[i] / pivot_column[i])
        else:
            ratios.append(float('inf'))

    return np.argmin(ratios) if min(ratios) >= 0 else None
```

#### 10.2.3 主元操作
```python
def pivot_operation(self, tableau, pivot_row, pivot_col):
    pivot_element = tableau[pivot_row, pivot_col]

    # 主元行归一化
    tableau[pivot_row, :] /= pivot_element

    # 其他行消元
    for i in range(tableau.shape[0]):
        if i != pivot_row:
            multiplier = tableau[i, pivot_col]
            tableau[i, :] -= multiplier * tableau[pivot_row, :]

    return tableau
```

### 10.3 数值稳定性考虑

1. **主元选择**：选择绝对值较大的主元
2. **容差设置**：使用适当的数值容差
3. **退化处理**：使用Bland规则防止循环
4. **精度控制**：监控数值误差累积

## 11. 实验设计

### 11.1 测试用例

#### 11.1.1 基本测试
- **问题规模**：3变量，2约束
- **初始解**：不可行（需要两阶段法）
- **预期结果**：最优解 $(0, 2, 0)$，最优值 $6$

#### 11.1.2 性能测试
- **迭代次数统计**
- **计算时间测量**
- **内存使用监控**
- **数值精度验证**

### 11.2 对比实验

#### 11.2.1 与scipy.optimize.linprog对比
- **解的质量**：验证解的正确性
- **性能比较**：时间和迭代次数
- **数值稳定性**：精度比较

#### 11.2.2 与次梯度法对比
- **收敛速度**：迭代次数对比
- **解的精度**：最优值误差
- **计算效率**：总时间比较
- **实现复杂度**：代码复杂度

### 11.3 评价指标

1. **正确性指标**
   - 解的可行性验证
   - 最优性条件检查
   - 与理论解的误差

2. **效率指标**
   - 迭代次数
   - 计算时间
   - 内存使用

3. **稳定性指标**
   - 数值精度
   - 对参数扰动的敏感性
   - 退化情况处理

## 12. 预期实验结果

### 12.1 单纯形法表现

**预期迭代过程**：
1. **第0次**：初始不可行解
2. **第1-2次**：通过两阶段法找到可行解
3. **第3-4次**：移动到最优解
4. **总迭代次数**：4-6次

**预期最终结果**：
- **最优解**：$(0, 2, 0)^T$
- **最优值**：$6.0$
- **计算时间**：< 0.01秒
- **数值精度**：机器精度级别

### 12.2 与次梯度法对比

| 指标 | 单纯形法 | 次梯度法 | 优势方 |
|------|----------|----------|--------|
| **迭代次数** | 4-6次 | 50-100次 | 单纯形法 |
| **计算时间** | < 0.01s | 0.1-0.5s | 单纯形法 |
| **最优值精度** | 机器精度 | 1e-3到1e-6 | 单纯形法 |
| **内存使用** | 中等 | 低 | 次梯度法 |
| **实现复杂度** | 高 | 低 | 次梯度法 |

### 12.3 算法选择建议

**选择单纯形法当**：
- 问题是线性规划
- 需要精确解
- 问题规模中等
- 需要敏感性分析

**选择次梯度法当**：
- 问题规模很大
- 可以接受近似解
- 实现简单性重要
- 问题是非光滑凸优化

## 13. 总结

### 13.1 单纯形法优势

1. **理论完备性**：有严格的数学理论支撑
2. **精确性**：能得到精确的最优解
3. **效率**：对线性规划问题高效
4. **成熟性**：算法成熟，有丰富的变种和改进

### 13.2 实际应用价值

1. **工业应用**：广泛应用于生产计划、资源分配等
2. **学术研究**：线性规划理论的基础
3. **算法基础**：许多高级算法的基础
4. **教学工具**：理解优化理论的重要工具

### 13.3 发展趋势

1. **内点法**：对大规模问题更高效
2. **并行算法**：利用现代计算架构
3. **混合方法**：结合不同算法的优点
4. **特殊结构**：针对特殊结构的专门算法

单纯形法作为线性规划的经典算法，虽然在最坏情况下有指数复杂度，但在实际应用中表现优秀，是理解和解决线性规划问题的重要工具。
