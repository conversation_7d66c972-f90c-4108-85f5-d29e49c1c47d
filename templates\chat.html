{% extends "base.html" %}

{% block title %}智能问答 - 医疗智能问答系统{% endblock %}

{% block extra_css %}
<style>
.chat-container {
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
}

.chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    border-radius: 10px 10px 0 0;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%);
    max-height: 500px;
    min-height: 400px;
}

.message {
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    clear: both;
}

.message.user {
    justify-content: flex-end;
    margin-left: 20%;
}

.message.assistant {
    justify-content: flex-start;
    margin-right: 20%;
}

.message-content {
    max-width: 100%;
    padding: 12px 16px;
    border-radius: 20px;
    position: relative;
    word-wrap: break-word;
    line-height: 1.4;
}

.message.user .message-content {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    margin-right: 12px;
    border-bottom-right-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.message.assistant .message-content {
    background: white;
    color: #333;
    border: 1px solid #e9ecef;
    margin-left: 12px;
    border-bottom-left-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
    flex-shrink: 0;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.message.user .message-avatar {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    order: 2;
}

.message.assistant .message-avatar {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    order: 0;
    margin-right: 8px;
}

.chat-input {
    padding: 20px;
    background: white;
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 10px 10px;
}

.input-group {
    position: relative;
    max-width: 100%;
    display: flex;
    align-items: center;
    height: 50px;
}

.form-control {
    border-radius: 25px;
    padding: 12px 60px 12px 20px;
    border: 2px solid #e9ecef;
    font-size: 14px;
    background: #f8f9fa;
    transition: all 0.3s ease;
    height: 50px;
    flex: 1;
    line-height: 1.5;
    box-sizing: border-box;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

.form-control::placeholder {
    color: #6c757d;
    font-style: italic;
}

.btn-send {
    border-radius: 50%;
    width: 44px;
    height: 44px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    position: absolute;
    right: 3px;
    top: 50%;
    transform: translateY(-50%);
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-send:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-send:active {
    transform: translateY(-50%) scale(0.95);
}

.typing-indicator {
    display: none;
    padding: 12px 16px;
    background: white;
    border-radius: 20px;
    border: 1px solid #e9ecef;
    margin-left: 12px;
    border-bottom-left-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #999;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

.model-selector {
    margin-bottom: 1rem;
}

.debug-info {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: #6c757d;
}

.debug-toggle {
    cursor: pointer;
    color: #007bff;
    text-decoration: underline;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- 侧边栏 -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cog me-2"></i>设置</h5>
                </div>
                <div class="card-body">
                    <div class="model-selector">
                        <label for="modelSelect" class="form-label">AI模型</label>
                        <select class="form-select" id="modelSelect">
                            <option value="bert-medical-qa" selected>BERT医疗问答模型</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showDebug">
                            <label class="form-check-label" for="showDebug">
                                显示调试信息
                            </label>
                        </div>
                    </div>

                    <button class="btn btn-outline-primary w-100" onclick="clearChat()">
                        <i class="fas fa-trash me-2"></i>清空对话
                    </button>
                </div>
            </div>

            <!-- 快速问题 -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-question-circle me-2"></i>快速问题</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-secondary btn-sm" onclick="sendQuickQuestion('感冒了怎么办？')">
                            感冒了怎么办？
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="sendQuickQuestion('高血压的症状有哪些？')">
                            高血压的症状有哪些？
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="sendQuickQuestion('糖尿病需要注意什么？')">
                            糖尿病需要注意什么？
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="sendQuickQuestion('头痛的原因是什么？')">
                            头痛的原因是什么？
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 聊天区域 -->
        <div class="col-md-9">
            <div class="card chat-container">
                <div class="chat-header">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-robot me-3 fs-4"></i>
                        <h5 class="mb-0">医疗智能助手</h5>
                    </div>
                </div>

                <div class="chat-messages" id="chatMessages">
                    <div class="message assistant">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <p class="mb-0">您好！我是基于BERT模型的医疗智能助手，可以为您提供医疗相关的咨询服务。我使用您训练的BERT模型权重和医疗知识图谱来回答问题。请注意，我的回答仅供参考，不能替代专业医生的诊断。有什么可以帮助您的吗？</p>
                        </div>
                    </div>
                </div>

                <div class="chat-input">
                    <form id="chatForm">
                        <div class="input-group">
                            <input type="text" class="form-control" id="messageInput"
                                   placeholder="请输入您的医疗问题..." autocomplete="off">
                            <button type="submit" class="btn btn-primary btn-send">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let chatHistory = [];

$(document).ready(function() {
    $('#chatForm').on('submit', function(e) {
        e.preventDefault();
        sendMessage();
    });

    $('#messageInput').on('keypress', function(e) {
        if (e.which === 13 && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
});

function sendMessage() {
    const message = $('#messageInput').val().trim();
    if (!message) return;

    // 添加用户消息
    addMessage('user', message);
    $('#messageInput').val('');

    // 显示输入指示器
    showTypingIndicator();

    // 发送到后端
    const model = $('#modelSelect').val();

    fetch('/api/chat', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            query: message,
            model: model
        })
    })
    .then(response => response.json())
    .then(data => {
        hideTypingIndicator();

        if (data.success) {
            addMessage('assistant', data.response, {
                entities: data.entities,
                intent: data.intent,
                debug_info: data.debug_info
            });
        } else {
            addMessage('assistant', '抱歉，处理您的问题时出现了错误：' + data.message);
        }
    })
    .catch(error => {
        hideTypingIndicator();
        console.error('Error:', error);
        addMessage('assistant', '抱歉，网络连接出现问题，请稍后重试。');
    });
}

function addMessage(role, content, metadata = null) {
    const messagesContainer = $('#chatMessages');
    const isUser = role === 'user';

    const messageHtml = `
        <div class="message ${role}">
            <div class="message-avatar">
                <i class="fas fa-${isUser ? 'user' : 'robot'}"></i>
            </div>
            <div class="message-content">
                <p class="mb-0">${content}</p>
                ${metadata && $('#showDebug').is(':checked') ? createDebugInfo(metadata) : ''}
            </div>
        </div>
    `;

    messagesContainer.append(messageHtml);
    messagesContainer.scrollTop(messagesContainer[0].scrollHeight);

    // 保存到历史记录
    chatHistory.push({
        role: role,
        content: content,
        metadata: metadata,
        timestamp: new Date()
    });
}

function createDebugInfo(metadata) {
    let debugHtml = '<div class="debug-info mt-2">';

    if (metadata.entities) {
        debugHtml += `<div><strong>识别实体:</strong> ${JSON.stringify(metadata.entities)}</div>`;
    }

    if (metadata.intent) {
        debugHtml += `<div><strong>意图识别:</strong> ${metadata.intent}</div>`;
    }

    if (metadata.debug_info && metadata.debug_info.prompt) {
        debugHtml += `<div class="mt-1">
            <span class="debug-toggle" onclick="togglePrompt(this)">显示提示词</span>
            <div class="prompt-content" style="display: none; margin-top: 0.5rem; padding: 0.5rem; background: #f8f9fa; border-radius: 5px; font-family: monospace; font-size: 0.7rem; white-space: pre-wrap;">${metadata.debug_info.prompt}</div>
        </div>`;
    }

    debugHtml += '</div>';
    return debugHtml;
}

function togglePrompt(element) {
    const promptContent = $(element).siblings('.prompt-content');
    if (promptContent.is(':visible')) {
        promptContent.hide();
        $(element).text('显示提示词');
    } else {
        promptContent.show();
        $(element).text('隐藏提示词');
    }
}

function showTypingIndicator() {
    const typingHtml = `
        <div class="message assistant typing-message">
            <div class="message-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="typing-indicator">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    `;

    $('#chatMessages').append(typingHtml);
    $('#chatMessages').scrollTop($('#chatMessages')[0].scrollHeight);
}

function hideTypingIndicator() {
    $('.typing-message').remove();
}

function sendQuickQuestion(question) {
    $('#messageInput').val(question);
    sendMessage();
}

function clearChat() {
    if (confirm('确定要清空所有对话记录吗？')) {
        $('#chatMessages').empty();
        chatHistory = [];

        // 添加欢迎消息
        addMessage('assistant', '您好！我是基于BERT模型的医疗智能助手，可以为您提供医疗相关的咨询服务。我使用您训练的BERT模型权重和医疗知识图谱来回答问题。请注意，我的回答仅供参考，不能替代专业医生的诊断。有什么可以帮助您的吗？');
    }
}

// 监听调试信息开关
$('#showDebug').on('change', function() {
    if ($(this).is(':checked')) {
        $('.debug-info').show();
    } else {
        $('.debug-info').hide();
    }
});
</script>
{% endblock %}
