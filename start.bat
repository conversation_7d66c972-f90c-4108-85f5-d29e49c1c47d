@echo off
chcp 65001 >nul
echo ================================================
echo 医疗智能问答系统 - Flask版本
echo ================================================
echo.

echo 正在检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo.
echo 正在检查依赖包...
python -c "import flask, py2neo, torch, transformers" 2>nul
if %errorlevel% neq 0 (
    echo 警告: 缺少必要的依赖包
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

echo.
echo 正在启动Flask应用...
python run_flask.py

pause
