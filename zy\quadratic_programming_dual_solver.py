#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
二次规划对偶问题求解器
专门针对图片中的二次规划问题：
min (1/2)x^T G x + c^T x
s.t. Ax ≤ b
其中 G 为半正定对称矩阵，X = R^n

完整推导对偶问题的步骤
"""

import numpy as np
import warnings
warnings.filterwarnings('ignore')

def derive_quadratic_programming_dual():
    """
    完整推导二次规划问题的对偶问题
    """
    print("=" * 100)
    print("二次规划问题对偶问题完整推导")
    print("=" * 100)
    print()

    print("【原问题 (Primal Problem)】")
    print("min  (1/2)x^T G x + c^T x")
    print("s.t. Ax ≤ b")
    print("     x ∈ R^n")
    print("其中：")
    print("- G 为 n×n 半正定对称矩阵")
    print("- A 为 m×n 矩阵")
    print("- c ∈ R^n, b ∈ R^m")
    print()

    print("=" * 80)
    print("第一步：构造拉格朗日函数")
    print("=" * 80)
    print()

    print("引入拉格朗日乘子 λ = [λ₁, λ₂, ..., λₘ]^T ≥ 0 对应约束 Ax ≤ b")
    print()
    print("拉格朗日函数：")
    print("L(x, λ) = (1/2)x^T G x + c^T x + λ^T(Ax - b)")
    print()
    print("展开得到：")
    print("L(x, λ) = (1/2)x^T G x + c^T x + λ^T Ax - λ^T b")
    print("        = (1/2)x^T G x + (c + A^T λ)^T x - λ^T b")
    print()

    print("=" * 80)
    print("第二步：求解对偶函数")
    print("=" * 80)
    print()

    print("对偶函数定义为：")
    print("g(λ) = inf_{x∈R^n} L(x, λ)")
    print("     = inf_{x∈R^n} [(1/2)x^T G x + (c + A^T λ)^T x - λ^T b]")
    print()

    print("为了求解这个下确界，对 x 求偏导数：")
    print("∇_x L(x, λ) = G x + c + A^T λ")
    print()

    print("令梯度为零：")
    print("G x + c + A^T λ = 0  ... (1)")
    print()

    print("【情况分析】")
    print()
    print("情况1：如果 G 正定")
    print("   则 G 可逆，从方程 (1) 得到唯一解：")
    print("   x* = -G^(-1)(c + A^T λ)  ... (2)")
    print()
    print("情况2：如果 G 半正定但不正定")
    print("   则需要检查 (c + A^T λ) 是否在 G 的列空间中")
    print("   - 如果不在列空间中，则 g(λ) = -∞")
    print("   - 如果在列空间中，则存在解")
    print()

    print("=" * 80)
    print("第三步：G 正定情况下的对偶函数计算")
    print("=" * 80)
    print()

    print("当 G 正定时，将 x* = -G^(-1)(c + A^T λ) 代入拉格朗日函数：")
    print()
    print("g(λ) = L(x*, λ)")
    print("     = (1/2)(x*)^T G x* + (c + A^T λ)^T x* - λ^T b")
    print()

    print("逐项计算：")
    print()
    print("第一项：(1/2)(x*)^T G x*")
    print("= (1/2)(-G^(-1)(c + A^T λ))^T G (-G^(-1)(c + A^T λ))")
    print("= (1/2)(c + A^T λ)^T (G^(-1))^T G G^(-1) (c + A^T λ)")
    print("= (1/2)(c + A^T λ)^T G^(-1) (c + A^T λ)  [因为 G 对称，G^(-1) 也对称]")
    print()

    print("第二项：(c + A^T λ)^T x*")
    print("= (c + A^T λ)^T (-G^(-1)(c + A^T λ))")
    print("= -(c + A^T λ)^T G^(-1) (c + A^T λ)")
    print()

    print("第三项：-λ^T b")
    print()

    print("因此：")
    print("g(λ) = (1/2)(c + A^T λ)^T G^(-1) (c + A^T λ)")
    print("     - (c + A^T λ)^T G^(-1) (c + A^T λ) - λ^T b")
    print("     = -(1/2)(c + A^T λ)^T G^(-1) (c + A^T λ) - λ^T b")
    print()

    print("=" * 80)
    print("第四步：对偶问题")
    print("=" * 80)
    print()

    print("对偶问题为：")
    print("max g(λ)")
    print("s.t. λ ≥ 0")
    print()
    print("即：")
    print("max [-(1/2)(c + A^T λ)^T G^(-1) (c + A^T λ) - λ^T b]")
    print("s.t. λ ≥ 0")
    print()
    print("等价于最小化问题：")
    print("min [(1/2)(c + A^T λ)^T G^(-1) (c + A^T λ) + λ^T b]")
    print("s.t. λ ≥ 0")
    print()

    print("=" * 80)
    print("第五步：对偶问题的标准二次规划形式")
    print("=" * 80)
    print()

    print("展开 (c + A^T λ)^T G^(-1) (c + A^T λ)：")
    print("= (c^T + λ^T A) G^(-1) (c + A^T λ)")
    print("= c^T G^(-1) c + c^T G^(-1) A^T λ + λ^T A G^(-1) c + λ^T A G^(-1) A^T λ")
    print("= c^T G^(-1) c + 2λ^T A G^(-1) c + λ^T A G^(-1) A^T λ")
    print("  [因为 c^T G^(-1) A^T λ = λ^T A G^(-1) c，标量的转置等于自身]")
    print()

    print("因此对偶问题变为：")
    print("min [(1/2)(c^T G^(-1) c + 2λ^T A G^(-1) c + λ^T A G^(-1) A^T λ) + λ^T b]")
    print("s.t. λ ≥ 0")
    print()
    print("= min [(1/2)c^T G^(-1) c + λ^T A G^(-1) c + (1/2)λ^T A G^(-1) A^T λ + λ^T b]")
    print("s.t. λ ≥ 0")
    print()

    print("忽略常数项 (1/2)c^T G^(-1) c，得到：")
    print()
    print("【对偶问题的最终形式】")
    print("min [(1/2)λ^T H λ + d^T λ]")
    print("s.t. λ ≥ 0")
    print()
    print("其中：")
    print("H = A G^(-1) A^T  (m×m 对称半正定矩阵)")
    print("d = A G^(-1) c + b  (m×1 向量)")
    print()

    print("=" * 80)
    print("第六步：KKT 最优性条件")
    print("=" * 80)
    print()

    print("原问题和对偶问题的 KKT 条件：")
    print()
    print("1. 原始可行性 (Primal Feasibility)：")
    print("   Ax ≤ b")
    print()
    print("2. 对偶可行性 (Dual Feasibility)：")
    print("   λ ≥ 0")
    print()
    print("3. 互补松弛性 (Complementary Slackness)：")
    print("   λᵢ(aᵢᵀx - bᵢ) = 0, ∀i = 1, 2, ..., m")
    print("   其中 aᵢᵀ 是矩阵 A 的第 i 行")
    print()
    print("4. 梯度条件 (Stationarity)：")
    print("   ∇_x L(x, λ) = G x + c + A^T λ = 0")
    print()

    print("=" * 80)
    print("第七步：强对偶性")
    print("=" * 80)
    print()

    print("当满足以下条件时，强对偶性成立：")
    print("1. G 正定，或")
    print("2. 存在 Slater 条件：存在 x 使得 Ax < b")
    print()
    print("强对偶性意味着：")
    print("原问题最优值 = 对偶问题最优值")
    print()
    print("此时，KKT 条件是最优性的必要充分条件。")
    print()

    return None

def numerical_example():
    """
    具体数值例子演示
    """
    print("=" * 100)
    print("数值例子：具体的二次规划问题及其对偶")
    print("=" * 100)
    print()

    print("【例子】考虑以下二次规划问题：")
    print()
    print("min (1/2)x^T [[4, 1], [1, 2]] x + [1, 2]^T x")
    print("s.t. [1, 1] x ≤ 4")
    print("     [2, 1] x ≤ 6")
    print("     x ∈ R²")
    print()

    # 定义问题参数
    G = np.array([[4, 1], [1, 2]], dtype=float)
    c = np.array([1, 2], dtype=float)
    A = np.array([[1, 1], [2, 1]], dtype=float)
    b = np.array([4, 6], dtype=float)

    print("问题参数：")
    print("G =", G)
    print("c =", c)
    print("A =", A)
    print("b =", b)
    print()

    # 检查 G 是否正定
    eigenvals = np.linalg.eigvals(G)
    print("G 的特征值：", eigenvals)
    is_positive_definite = np.all(eigenvals > 0)
    print("G 是否正定：", "是" if is_positive_definite else "否")
    print()

    if is_positive_definite:
        print("=" * 60)
        print("构造对偶问题")
        print("=" * 60)
        print()

        # 计算对偶问题参数
        G_inv = np.linalg.inv(G)
        H = A @ G_inv @ A.T
        d = A @ G_inv @ c + b

        print("计算对偶问题参数：")
        print("G^(-1) =")
        print(G_inv)
        print()
        print("H = A G^(-1) A^T =")
        print(H)
        print()
        print("d = A G^(-1) c + b =")
        print(d)
        print()

        print("【对偶问题】")
        print("min (1/2)λ^T H λ + d^T λ")
        print("s.t. λ ≥ 0")
        print()
        print("具体形式：")
        print(f"min (1/2)λ^T {H} λ + {d}^T λ")
        print("s.t. λ ≥ 0")
        print()

        # 使用 scipy 求解原问题和对偶问题
        from scipy.optimize import minimize

        print("=" * 60)
        print("数值求解")
        print("=" * 60)
        print()

        # 求解原问题
        def primal_objective(x):
            return 0.5 * x.T @ G @ x + c.T @ x

        def constraint1(x):
            return b[0] - A[0] @ x

        def constraint2(x):
            return b[1] - A[1] @ x

        constraints = [
            {'type': 'ineq', 'fun': constraint1},
            {'type': 'ineq', 'fun': constraint2}
        ]

        x0 = np.array([0, 0])
        result_primal = minimize(primal_objective, x0, method='SLSQP', constraints=constraints)

        # 求解对偶问题
        def dual_objective(lam):
            return 0.5 * lam.T @ H @ lam + d.T @ lam

        bounds = [(0, None), (0, None)]
        lam0 = np.array([0, 0])
        result_dual = minimize(dual_objective, lam0, method='L-BFGS-B', bounds=bounds)

        print("【原问题求解结果】")
        if result_primal.success:
            x_opt = result_primal.x
            print(f"最优解: x* = [{x_opt[0]:.6f}, {x_opt[1]:.6f}]")
            print(f"最优值: {result_primal.fun:.6f}")
        else:
            print("求解失败")
            return

        print()
        print("【对偶问题求解结果】")
        if result_dual.success:
            lam_opt = result_dual.x
            print(f"最优解: λ* = [{lam_opt[0]:.6f}, {lam_opt[1]:.6f}]")
            print(f"最优值: {result_dual.fun:.6f}")
        else:
            print("求解失败")
            return

        print()
        print("=" * 60)
        print("验证结果")
        print("=" * 60)
        print()

        # 验证强对偶性
        print("【强对偶性验证】")
        print(f"原问题最优值: {result_primal.fun:.8f}")
        print(f"对偶问题最优值: {result_dual.fun:.8f}")
        duality_gap = abs(result_primal.fun - result_dual.fun)
        print(f"对偶间隙: {duality_gap:.10f}")
        print(f"强对偶性: {'成立' if duality_gap < 1e-6 else '不成立'}")
        print()

        # 验证 KKT 条件
        print("【KKT 条件验证】")
        print()

        print("1. 原始可行性:")
        for i in range(len(b)):
            constraint_val = A[i] @ x_opt
            feasible = constraint_val <= b[i] + 1e-8
            print(f"   约束{i+1}: {A[i]} x = {constraint_val:.6f} ≤ {b[i]} {'✓' if feasible else '✗'}")
        print()

        print("2. 对偶可行性:")
        for i in range(len(lam_opt)):
            feasible = lam_opt[i] >= -1e-8
            print(f"   λ_{i+1} = {lam_opt[i]:.6f} ≥ 0 {'✓' if feasible else '✗'}")
        print()

        print("3. 梯度条件 (G x + c + A^T λ = 0):")
        gradient = G @ x_opt + c + A.T @ lam_opt
        gradient_satisfied = np.allclose(gradient, 0, atol=1e-6)
        print(f"   G x + c + A^T λ = {gradient}")
        print(f"   梯度条件: {'满足' if gradient_satisfied else '不满足'}")
        print()

        print("4. 互补松弛性 (λᵢ(aᵢᵀx - bᵢ) = 0):")
        for i in range(len(lam_opt)):
            slack = b[i] - A[i] @ x_opt
            complementarity = lam_opt[i] * slack
            comp_satisfied = abs(complementarity) < 1e-6
            print(f"   λ_{i+1} × slack_{i+1} = {lam_opt[i]:.6f} × {slack:.6f} = {complementarity:.8f}")
            print(f"   互补松弛性{i+1}: {'满足' if comp_satisfied else '不满足'}")
        print()

    else:
        print("G 不是正定矩阵，需要更复杂的分析。")

if __name__ == "__main__":
    print("二次规划对偶问题完整求解器")
    print("解决图片中的问题：推导二次规划问题的对偶问题")
    print()

    # 理论推导
    derive_quadratic_programming_dual()

    # 数值例子
    numerical_example()

    print("=" * 100)
    print("总结")
    print("=" * 100)
    print()
    print("本程序完整展示了二次规划问题对偶问题的推导过程：")
    print("1. 构造拉格朗日函数")
    print("2. 求解对偶函数")
    print("3. 推导对偶问题的标准形式")
    print("4. 分析 KKT 最优性条件")
    print("5. 验证强对偶性")
    print("6. 通过数值例子验证理论结果")
    print()
    print("【关键结论】")
    print("对于二次规划问题：min (1/2)x^T G x + c^T x, s.t. Ax ≤ b")
    print("当 G 正定时，其对偶问题为：")
    print("min (1/2)λ^T H λ + d^T λ, s.t. λ ≥ 0")
    print("其中 H = A G^(-1) A^T, d = A G^(-1) c + b")
