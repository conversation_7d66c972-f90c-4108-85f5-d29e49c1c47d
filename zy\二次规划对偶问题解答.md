# 二次规划问题对偶问题推导

## 问题描述

考虑如下二次规划问题：

```
min  (1/2)x^T G x + c^T x
s.t. Ax ≤ b
```

其中，G为半正定对称矩阵，X = R^n，写出该问题的对偶问题。

## 完整解答

### 第一步：构造拉格朗日函数

引入拉格朗日乘子 λ = [λ₁, λ₂, ..., λₘ]^T ≥ 0 对应约束 Ax ≤ b

拉格朗日函数：
```
L(x, λ) = (1/2)x^T G x + c^T x + λ^T(Ax - b)
        = (1/2)x^T G x + (c + A^T λ)^T x - λ^T b
```

### 第二步：求解对偶函数

对偶函数定义为：
```
g(λ) = inf_{x∈R^n} L(x, λ)
     = inf_{x∈R^n} [(1/2)x^T G x + (c + A^T λ)^T x - λ^T b]
```

对 x 求偏导数并令其为零：
```
∇_x L(x, λ) = G x + c + A^T λ = 0
```

**情况分析：**

- **情况1：如果 G 正定**
  则 G 可逆，得到唯一解：x* = -G^(-1)(c + A^T λ)

- **情况2：如果 G 半正定但不正定**
  需要检查 (c + A^T λ) 是否在 G 的列空间中
  - 如果不在列空间中，则 g(λ) = -∞
  - 如果在列空间中，则存在解

### 第三步：G 正定情况下的对偶函数计算

当 G 正定时，将 x* = -G^(-1)(c + A^T λ) 代入拉格朗日函数：

```
g(λ) = (1/2)(x*)^T G x* + (c + A^T λ)^T x* - λ^T b
```

逐项计算：

1. **第一项：** (1/2)(x*)^T G x* = (1/2)(c + A^T λ)^T G^(-1) (c + A^T λ)
2. **第二项：** (c + A^T λ)^T x* = -(c + A^T λ)^T G^(-1) (c + A^T λ)
3. **第三项：** -λ^T b

因此：
```
g(λ) = -(1/2)(c + A^T λ)^T G^(-1) (c + A^T λ) - λ^T b
```

### 第四步：对偶问题

对偶问题为：
```
max g(λ)
s.t. λ ≥ 0
```

即：
```
max [-(1/2)(c + A^T λ)^T G^(-1) (c + A^T λ) - λ^T b]
s.t. λ ≥ 0
```

等价于最小化问题：
```
min [(1/2)(c + A^T λ)^T G^(-1) (c + A^T λ) + λ^T b]
s.t. λ ≥ 0
```

### 第五步：对偶问题的标准形式

展开 (c + A^T λ)^T G^(-1) (c + A^T λ)：

```
= c^T G^(-1) c + 2λ^T A G^(-1) c + λ^T A G^(-1) A^T λ
```

忽略常数项，对偶问题的最终形式为：

## **对偶问题**

```
min (1/2)λ^T H λ + d^T λ
s.t. λ ≥ 0
```

**其中：**
- **H = A G^(-1) A^T** (m×m 对称半正定矩阵)
- **d = A G^(-1) c + b** (m×1 向量)

### KKT 最优性条件

原问题和对偶问题的 KKT 条件：

1. **原始可行性：** Ax ≤ b
2. **对偶可行性：** λ ≥ 0
3. **互补松弛性：** λᵢ(aᵢᵀx - bᵢ) = 0, ∀i = 1, 2, ..., m
4. **梯度条件：** G x + c + A^T λ = 0

### 强对偶性

当满足以下条件时，强对偶性成立：
1. G 正定，或
2. 存在 Slater 条件：存在 x 使得 Ax < b

强对偶性意味着：**原问题最优值 = 对偶问题最优值**

## 数值验证示例

程序 `quadratic_programming_dual_solver.py` 提供了完整的数值验证，包括：
- 具体二次规划问题的求解
- 对偶问题的构造和求解
- KKT 条件的验证
- 强对偶性的检验

## 关键结论

对于二次规划问题：
```
min (1/2)x^T G x + c^T x
s.t. Ax ≤ b
```

当 G 正定时，其**对偶问题**为：
```
min (1/2)λ^T H λ + d^T λ
s.t. λ ≥ 0
```

其中：
- **H = A G^(-1) A^T**
- **d = A G^(-1) c + b**

这就是图片中二次规划问题的完整对偶问题推导结果。
