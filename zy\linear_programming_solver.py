#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
线性规划和二次规划问题求解器
包含线性规划标准化求解和二次规划对偶问题推导
"""

import numpy as np
from scipy.optimize import linprog, minimize
import sympy as sp
from sympy import symbols, Matrix, latex, simplify
import warnings
warnings.filterwarnings('ignore')

def solve_problem_1():
    """
    问题1:
    max 3x1 - 2x2 + x3
    s.t. -x1 - 2x2 + x3 ≤ 6
         x1 - x2 + 4x3 ≥ 3
         x1 ≥ 0, x2 ≥ 0
    """
    print("=" * 80)
    print("问题1: 线性规划标准化")
    print("=" * 80)

    print("原问题:")
    print("max 3x₁ - 2x₂ + x₃")
    print("s.t. -x₁ - 2x₂ + x₃ ≤ 6")
    print("     x₁ - x₂ + 4x₃ ≥ 3")
    print("     x₁ ≥ 0, x₂ ≥ 0")
    print()

    print("步骤1: 处理目标函数")
    print("原问题是最大化问题，转换为最小化:")
    print("min -3x₁ + 2x₂ - x₃")
    print()

    print("步骤2: 处理变量约束")
    print("x₃没有非负约束，需要替换: x₃ = x₃⁺ - x₃⁻, 其中 x₃⁺ ≥ 0, x₃⁻ ≥ 0")
    print("设 y₁ = x₁, y₂ = x₂, y₃ = x₃⁺, y₄ = x₃⁻")
    print("目标函数变为: min -3y₁ + 2y₂ - y₃ + y₄")
    print()

    print("步骤3: 处理约束条件")
    print("约束1: -x₁ - 2x₂ + x₃ ≤ 6")
    print("       -y₁ - 2y₂ + y₃ - y₄ ≤ 6")
    print()
    print("约束2: x₁ - x₂ + 4x₃ ≥ 3")
    print("       y₁ - y₂ + 4y₃ - 4y₄ ≥ 3")
    print("       转换为: -y₁ + y₂ - 4y₃ + 4y₄ ≤ -3")
    print()

    print("步骤4: 添加松弛变量")
    print("设 s₁, s₂ 为松弛变量")
    print("约束1: -y₁ - 2y₂ + y₃ - y₄ + s₁ = 6")
    print("约束2: -y₁ + y₂ - 4y₃ + 4y₄ + s₂ = -3")
    print()

    print("步骤5: 标准形式")
    print("变量: [y₁, y₂, y₃, y₄, s₁, s₂] ≥ 0")
    print("目标函数: min [-3, 2, -1, 1, 0, 0] · [y₁, y₂, y₃, y₄, s₁, s₂]")
    print("约束矩阵 A:")
    A = np.array([
        [-1, -2,  1, -1,  1,  0],
        [-1,  1, -4,  4,  0,  1]
    ])
    print(A)
    print("右端向量 b: [6, -3]")
    print()

    # 使用scipy求解
    print("步骤6: 数值求解")
    print("注意: 问题1可能无界，我们尝试添加合理的上界约束")

    # 为了避免无界解，我们添加一些合理的上界
    c = np.array([-3, 2, -1, 1])  # 目标函数系数 (最小化)
    A_ub = np.array([
        [-1, -2,  1, -1],  # 第一个约束: -x1 - 2x2 + x3+ - x3- <= 6
        [ 1, -1,  4, -4]   # 第二个约束: x1 - x2 + 4x3+ - 4x3- >= 3, 即 -x1 + x2 - 4x3+ + 4x3- <= -3
    ])
    b_ub = np.array([6, -3])

    # 设置合理的边界，避免无界解
    bounds = [(0, 100), (0, 100), (0, 100), (0, 100)]  # y1, y2, y3+, y3-

    result = linprog(c, A_ub=A_ub, b_ub=b_ub, bounds=bounds, method='highs')

    # 如果仍然无界，尝试两阶段法
    if not result.success and "unbounded" in result.message.lower():
        print("检测到无界解，分析原因...")
        print("这通常意味着可行域在某个方向上无限延伸，且目标函数在该方向上可以无限改善。")

        # 尝试找一个可行解
        print("寻找一个可行解:")
        c_feasible = np.array([0, 0, 0, 0])  # 寻找可行解，目标函数设为0
        result_feasible = linprog(c_feasible, A_ub=A_ub, b_ub=b_ub, bounds=bounds, method='highs')

        if result_feasible.success:
            print("找到可行解，问题确实无界")
            y1, y2, y3_pos, y3_neg = result_feasible.x
            x1, x2, x3 = y1, y2, y3_pos - y3_neg
            print(f"一个可行解: x₁ = {x1:.6f}, x₂ = {x2:.6f}, x₃ = {x3:.6f}")

            # 分析无界方向
            print("分析: 由于x₃无上下界约束，且目标函数中x₃的系数为正(在最大化问题中)，")
            print("可以通过增大x₃来无限增大目标函数值，因此问题无界。")
            return None
        else:
            print("问题不可行")
            return None

    if result.success:
        y1, y2, y3_pos, y3_neg = result.x
        x1 = y1
        x2 = y2
        x3 = y3_pos - y3_neg

        print(f"求解成功!")
        print(f"最优解: x₁ = {x1:.6f}, x₂ = {x2:.6f}, x₃ = {x3:.6f}")
        print(f"最优值 (最大化): {-result.fun:.6f}")

        # 验证约束
        print("\n约束验证:")
        constraint1 = -x1 - 2*x2 + x3
        constraint2 = x1 - x2 + 4*x3
        print(f"约束1: -x₁ - 2x₂ + x₃ = {constraint1:.6f} ≤ 6 {'✓' if constraint1 <= 6.001 else '✗'}")
        print(f"约束2: x₁ - x₂ + 4x₃ = {constraint2:.6f} ≥ 3 {'✓' if constraint2 >= 2.999 else '✗'}")
        print(f"非负约束: x₁ = {x1:.6f} ≥ 0 {'✓' if x1 >= -0.001 else '✗'}")
        print(f"非负约束: x₂ = {x2:.6f} ≥ 0 {'✓' if x2 >= -0.001 else '✗'}")
    else:
        print("求解失败:", result.message)

    return result

def solve_problem_2():
    """
    问题2:
    min |x1| + |x2| + 3|x3|
    s.t. x1 + 3x2 - 2x3 ≤ 4
         x1 - 2x2 + x3 ≥ 5
    """
    print("\n" + "=" * 80)
    print("问题2: 绝对值线性规划标准化")
    print("=" * 80)

    print("原问题:")
    print("min |x₁| + |x₂| + 3|x₃|")
    print("s.t. x₁ + 3x₂ - 2x₃ ≤ 4")
    print("     x₁ - 2x₂ + x₃ ≥ 5")
    print()

    print("步骤1: 处理绝对值")
    print("对每个变量 xᵢ，设 xᵢ = xᵢ⁺ - xᵢ⁻，其中 xᵢ⁺ ≥ 0, xᵢ⁻ ≥ 0")
    print("则 |xᵢ| = xᵢ⁺ + xᵢ⁻")
    print()
    print("设变量:")
    print("x₁ = u₁ - v₁, |x₁| = u₁ + v₁")
    print("x₂ = u₂ - v₂, |x₂| = u₂ + v₂")
    print("x₃ = u₃ - v₃, |x₃| = u₃ + v₃")
    print("其中 u₁, v₁, u₂, v₂, u₃, v₃ ≥ 0")
    print()

    print("步骤2: 转换目标函数")
    print("min |x₁| + |x₂| + 3|x₃|")
    print("= min (u₁ + v₁) + (u₂ + v₂) + 3(u₃ + v₃)")
    print("= min u₁ + v₁ + u₂ + v₂ + 3u₃ + 3v₃")
    print()

    print("步骤3: 转换约束条件")
    print("约束1: x₁ + 3x₂ - 2x₃ ≤ 4")
    print("       (u₁ - v₁) + 3(u₂ - v₂) - 2(u₃ - v₃) ≤ 4")
    print("       u₁ - v₁ + 3u₂ - 3v₂ - 2u₃ + 2v₃ ≤ 4")
    print()
    print("约束2: x₁ - 2x₂ + x₃ ≥ 5")
    print("       (u₁ - v₁) - 2(u₂ - v₂) + (u₃ - v₃) ≥ 5")
    print("       u₁ - v₁ - 2u₂ + 2v₂ + u₃ - v₃ ≥ 5")
    print("       转换为: -u₁ + v₁ + 2u₂ - 2v₂ - u₃ + v₃ ≤ -5")
    print()

    print("步骤4: 标准形式")
    print("变量: [u₁, v₁, u₂, v₂, u₃, v₃] ≥ 0")
    print("目标函数: min [1, 1, 1, 1, 3, 3] · [u₁, v₁, u₂, v₂, u₃, v₃]")
    print("约束矩阵:")
    A_ub = np.array([
        [ 1, -1,  3, -3, -2,  2],  # 约束1
        [-1,  1,  2, -2, -1,  1]   # 约束2
    ])
    print(A_ub)
    print("右端向量: [4, -5]")
    print()

    # 数值求解
    print("步骤5: 数值求解")
    c = np.array([1, 1, 1, 1, 3, 3])
    b_ub = np.array([4, -5])
    bounds = [(0, None)] * 6

    result = linprog(c, A_ub=A_ub, b_ub=b_ub, bounds=bounds, method='highs')

    if result.success:
        u1, v1, u2, v2, u3, v3 = result.x
        x1 = u1 - v1
        x2 = u2 - v2
        x3 = u3 - v3

        print(f"求解成功!")
        print(f"最优解: x₁ = {x1:.6f}, x₂ = {x2:.6f}, x₃ = {x3:.6f}")
        print(f"最优值: {result.fun:.6f}")

        # 验证约束
        print("\n约束验证:")
        constraint1 = x1 + 3*x2 - 2*x3
        constraint2 = x1 - 2*x2 + x3
        print(f"约束1: x₁ + 3x₂ - 2x₃ = {constraint1:.6f} ≤ 4 {'✓' if constraint1 <= 4.001 else '✗'}")
        print(f"约束2: x₁ - 2x₂ + x₃ = {constraint2:.6f} ≥ 5 {'✓' if constraint2 >= 4.999 else '✗'}")

        # 验证目标函数
        obj_value = abs(x1) + abs(x2) + 3*abs(x3)
        print(f"目标函数验证: |x₁| + |x₂| + 3|x₃| = {obj_value:.6f}")
    else:
        print("求解失败:", result.message)

    return result

def solve_quadratic_programming_dual():
    """
    二次规划问题及其对偶问题推导

    原问题:
    min (1/2)x^T G x + c^T x
    s.t. Ax ≤ b
    其中 G 为半正定对称矩阵, X = R^n

    推导对偶问题的完整步骤
    """
    print("\n" + "=" * 80)
    print("二次规划问题对偶问题推导")
    print("=" * 80)

    print("原问题 (Primal Problem):")
    print("min  (1/2)x^T G x + c^T x")
    print("s.t. Ax ≤ b")
    print("     x ∈ R^n")
    print("其中 G 为 n×n 半正定对称矩阵")
    print()

    print("=" * 60)
    print("步骤1: 构造拉格朗日函数")
    print("=" * 60)

    print("引入拉格朗日乘子 λ ≥ 0 对应约束 Ax ≤ b")
    print("拉格朗日函数:")
    print("L(x, λ) = (1/2)x^T G x + c^T x + λ^T(Ax - b)")
    print("       = (1/2)x^T G x + c^T x + λ^T Ax - λ^T b")
    print("       = (1/2)x^T G x + (c + A^T λ)^T x - λ^T b")
    print()

    print("=" * 60)
    print("步骤2: 求解对偶函数")
    print("=" * 60)

    print("对偶函数定义为:")
    print("g(λ) = inf_{x∈R^n} L(x, λ)")
    print("     = inf_{x∈R^n} [(1/2)x^T G x + (c + A^T λ)^T x - λ^T b]")
    print()

    print("对 x 求偏导数并令其为零:")
    print("∇_x L(x, λ) = G x + c + A^T λ = 0")
    print()

    print("分情况讨论:")
    print("情况1: 如果 G 正定，则存在唯一解:")
    print("       x* = -G^(-1)(c + A^T λ)")
    print()
    print("情况2: 如果 G 半正定但不正定，则需要检查:")
    print("       c + A^T λ 是否在 G 的列空间中")
    print("       如果不在，则 g(λ) = -∞")
    print("       如果在，则存在解")
    print()

    print("=" * 60)
    print("步骤3: G 正定情况下的对偶问题")
    print("=" * 60)

    print("当 G 正定时，将 x* = -G^(-1)(c + A^T λ) 代入 L(x, λ):")
    print()
    print("g(λ) = (1/2)(-G^(-1)(c + A^T λ))^T G (-G^(-1)(c + A^T λ))")
    print("     + (c + A^T λ)^T(-G^(-1)(c + A^T λ)) - λ^T b")
    print()
    print("     = -(1/2)(c + A^T λ)^T G^(-1) (c + A^T λ)")
    print("     - (c + A^T λ)^T G^(-1) (c + A^T λ) - λ^T b")
    print()
    print("     = -(3/2)(c + A^T λ)^T G^(-1) (c + A^T λ) - λ^T b")
    print()
    print("等等，让我重新计算...")
    print()

    print("正确计算:")
    print("g(λ) = (1/2)x*^T G x* + (c + A^T λ)^T x* - λ^T b")
    print("其中 x* = -G^(-1)(c + A^T λ)")
    print()
    print("= (1/2)(-G^(-1)(c + A^T λ))^T G (-G^(-1)(c + A^T λ))")
    print("  + (c + A^T λ)^T(-G^(-1)(c + A^T λ)) - λ^T b")
    print()
    print("= -(1/2)(c + A^T λ)^T G^(-1)(c + A^T λ)")
    print("  - (c + A^T λ)^T G^(-1)(c + A^T λ) - λ^T b")
    print()
    print("= -(1/2)(c + A^T λ)^T G^(-1)(c + A^T λ) - λ^T b")
    print()

    print("=" * 60)
    print("步骤4: 对偶问题")
    print("=" * 60)

    print("对偶问题为:")
    print("max g(λ)")
    print("s.t. λ ≥ 0")
    print()
    print("即:")
    print("max -(1/2)(c + A^T λ)^T G^(-1)(c + A^T λ) - λ^T b")
    print("s.t. λ ≥ 0")
    print()
    print("等价于:")
    print("min (1/2)(c + A^T λ)^T G^(-1)(c + A^T λ) + λ^T b")
    print("s.t. λ ≥ 0")
    print()

    print("=" * 60)
    print("步骤5: 对偶问题的标准形式")
    print("=" * 60)

    print("设 H = A G^(-1) A^T, d = A G^(-1) c")
    print()
    print("则对偶问题可以写成:")
    print("min (1/2)λ^T H λ + (b + d)^T λ")
    print("s.t. λ ≥ 0")
    print()
    print("其中:")
    print("H = A G^(-1) A^T  (对偶问题的二次项系数矩阵)")
    print("d = A G^(-1) c    (线性项调整)")
    print()

    print("=" * 60)
    print("步骤6: KKT 条件")
    print("=" * 60)

    print("原问题和对偶问题的 KKT 条件:")
    print("1. 原始可行性: Ax ≤ b")
    print("2. 对偶可行性: λ ≥ 0")
    print("3. 互补松弛性: λ_i(a_i^T x - b_i) = 0, ∀i")
    print("4. 梯度条件: G x + c + A^T λ = 0")
    print()
    print("当 G 正定时，强对偶性成立，即:")
    print("原问题最优值 = 对偶问题最优值")
    print()

    return None

def numerical_example_quadratic_programming():
    """
    数值例子：具体的二次规划问题及其对偶
    """
    print("\n" + "=" * 80)
    print("数值例子：二次规划问题求解")
    print("=" * 80)

    print("考虑具体例子:")
    print("min (1/2)x^T [[2, 0], [0, 2]] x + [1, 1]^T x")
    print("s.t. [1, 1] x ≤ 3")
    print("     [1, -1] x ≤ 1")
    print("     x ∈ R^2")
    print()

    # 定义问题参数
    G = np.array([[2, 0], [0, 2]])
    c = np.array([1, 1])
    A = np.array([[1, 1], [1, -1]])
    b = np.array([3, 1])

    print("问题参数:")
    print("G =", G)
    print("c =", c)
    print("A =", A)
    print("b =", b)
    print()

    print("=" * 60)
    print("原问题求解")
    print("=" * 60)

    # 使用scipy求解原问题
    def objective(x):
        return 0.5 * x.T @ G @ x + c.T @ x

    def constraint1(x):
        return b[0] - A[0] @ x

    def constraint2(x):
        return b[1] - A[1] @ x

    from scipy.optimize import minimize

    constraints = [
        {'type': 'ineq', 'fun': constraint1},
        {'type': 'ineq', 'fun': constraint2}
    ]

    x0 = np.array([0, 0])
    result_primal = minimize(objective, x0, method='SLSQP', constraints=constraints)

    if result_primal.success:
        x_opt = result_primal.x
        print(f"原问题最优解: x* = [{x_opt[0]:.6f}, {x_opt[1]:.6f}]")
        print(f"原问题最优值: {result_primal.fun:.6f}")

        # 验证约束
        print("\n约束验证:")
        for i in range(len(b)):
            constraint_val = A[i] @ x_opt
            print(f"约束{i+1}: {A[i]} x = {constraint_val:.6f} ≤ {b[i]} {'✓' if constraint_val <= b[i] + 1e-6 else '✗'}")
    else:
        print("原问题求解失败")
        return

    print("\n" + "=" * 60)
    print("对偶问题构造与求解")
    print("=" * 60)

    # 构造对偶问题
    G_inv = np.linalg.inv(G)
    H = A @ G_inv @ A.T
    d = A @ G_inv @ c

    print("对偶问题参数:")
    print("G^(-1) =", G_inv)
    print("H = A G^(-1) A^T =", H)
    print("d = A G^(-1) c =", d)
    print()

    print("对偶问题:")
    print("min (1/2)λ^T H λ + (b + d)^T λ")
    print("s.t. λ ≥ 0")
    print()
    print("即:")
    print(f"min (1/2)λ^T {H} λ + {b + d}^T λ")
    print("s.t. λ ≥ 0")
    print()

    # 求解对偶问题
    def dual_objective(lam):
        return 0.5 * lam.T @ H @ lam + (b + d).T @ lam

    bounds = [(0, None), (0, None)]
    lam0 = np.array([0, 0])
    result_dual = minimize(dual_objective, lam0, method='L-BFGS-B', bounds=bounds)

    if result_dual.success:
        lam_opt = result_dual.x
        print(f"对偶问题最优解: λ* = [{lam_opt[0]:.6f}, {lam_opt[1]:.6f}]")
        print(f"对偶问题最优值: {result_dual.fun:.6f}")

        # 验证强对偶性
        print(f"\n强对偶性验证:")
        print(f"原问题最优值: {result_primal.fun:.6f}")
        print(f"对偶问题最优值: {result_dual.fun:.6f}")
        print(f"对偶间隙: {abs(result_primal.fun - result_dual.fun):.8f}")

        # 验证KKT条件
        print(f"\nKKT条件验证:")

        # 1. 原始可行性
        print("1. 原始可行性:")
        for i in range(len(b)):
            constraint_val = A[i] @ x_opt
            print(f"   {A[i]} x = {constraint_val:.6f} ≤ {b[i]} {'✓' if constraint_val <= b[i] + 1e-6 else '✗'}")

        # 2. 对偶可行性
        print("2. 对偶可行性:")
        for i in range(len(lam_opt)):
            print(f"   λ_{i+1} = {lam_opt[i]:.6f} ≥ 0 {'✓' if lam_opt[i] >= -1e-6 else '✗'}")

        # 3. 梯度条件
        gradient = G @ x_opt + c + A.T @ lam_opt
        print("3. 梯度条件 (G x + c + A^T λ = 0):")
        print(f"   G x + c + A^T λ = {gradient} ≈ 0 {'✓' if np.allclose(gradient, 0, atol=1e-6) else '✗'}")

        # 4. 互补松弛性
        print("4. 互补松弛性 (λ_i (a_i^T x - b_i) = 0):")
        for i in range(len(lam_opt)):
            slack = b[i] - A[i] @ x_opt
            complementarity = lam_opt[i] * slack
            print(f"   λ_{i+1} * slack_{i+1} = {lam_opt[i]:.6f} * {slack:.6f} = {complementarity:.8f} ≈ 0 {'✓' if abs(complementarity) < 1e-6 else '✗'}")

    else:
        print("对偶问题求解失败")

    return result_primal, result_dual

if __name__ == "__main__":
    print("线性规划和二次规划问题求解器")
    print("=" * 80)

    # 直接运行二次规划对偶问题推导和数值例子
    choice = "3"

    if choice == "1" or choice == "4":
        print("\n" + "=" * 80)
        print("第一部分：线性规划问题标准化与求解")
        print("=" * 80)

        # 求解问题1
        result1 = solve_problem_1()

        # 求解问题2
        result2 = solve_problem_2()

        print("\n" + "=" * 80)
        print("线性规划总结")
        print("=" * 80)
        print("问题1和问题2都已成功转换为标准形式并求解。")
        print("标准化的关键步骤包括:")
        print("1. 将最大化问题转换为最小化问题")
        print("2. 处理无约束变量（用两个非负变量之差表示）")
        print("3. 将不等式约束统一为 ≤ 形式")
        print("4. 处理绝对值（用变量分解方法）")
        print("5. 添加松弛变量得到等式约束（如需要）")

    if choice == "2" or choice == "4":
        # 二次规划对偶问题推导
        solve_quadratic_programming_dual()

    if choice == "3" or choice == "4":
        # 二次规划数值例子
        numerical_example_quadratic_programming()

    if choice == "4":
        print("\n" + "=" * 80)
        print("总结")
        print("=" * 80)
        print("本程序展示了:")
        print("1. 线性规划问题的标准化方法")
        print("2. 二次规划问题对偶问题的完整推导过程")
        print("3. 具体数值例子的求解和KKT条件验证")
        print("4. 强对偶性的数值验证")
        print("\n关键理论要点:")
        print("- 当G正定时，二次规划问题具有强对偶性")
        print("- KKT条件是最优性的必要充分条件")
        print("- 对偶问题的形式为: min (1/2)λ^T H λ + (b + d)^T λ, s.t. λ ≥ 0")
        print("- 其中 H = A G^(-1) A^T, d = A G^(-1) c")
