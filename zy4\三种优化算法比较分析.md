# 三种优化算法比较分析：最速下降法、牛顿法、共轭梯度法

## 1. 问题描述

### 1.1 优化问题
求解无约束优化问题：

$$\min f(x) = 4(x_1+1)^2 + 2(x_2-1)^2 + x_1 + x_2 + 10$$

其中：
- 初始点：$x^{(0)} = (0, 0)^T$
- 收敛精度：$\varepsilon = 10^{-5}$

### 1.2 问题特点
- **目标函数类型**：二次函数（强凸函数）
- **变量维数**：2维
- **约束条件**：无约束
- **全局最优解**：存在唯一全局最优解

### 1.3 理论最优解
通过令 $\nabla f(x^*) = 0$ 求得：
- $x_1^* = -\frac{9}{8} = -1.125$
- $x_2^* = \frac{3}{4} = 0.75$
- $f(x^*) = 9.8125$

## 2. 三种算法原理

### 2.1 最速下降法（Steepest Descent Method）

#### 2.1.1 基本思想
- 在当前点处，**负梯度方向**是函数值下降最快的方向
- 沿着负梯度方向进行线搜索，寻找最优步长
- 重复此过程直到收敛

#### 2.1.2 算法步骤
1. 计算梯度：$g^{(k)} = \nabla f(x^{(k)})$
2. 搜索方向：$d^{(k)} = -g^{(k)}$
3. 线搜索：$\alpha^{(k)} = \arg\min_{\alpha} f(x^{(k)} + \alpha d^{(k)})$
4. 更新点：$x^{(k+1)} = x^{(k)} + \alpha^{(k)} d^{(k)}$

#### 2.1.3 收敛性质
- **收敛速度**：线性收敛
- **收敛率**：$\rho = \frac{\kappa - 1}{\kappa + 1}$，其中 $\kappa$ 是条件数
- **对本问题**：$\rho = \frac{2-1}{2+1} = \frac{1}{3}$

### 2.2 牛顿法（Newton's Method）

#### 2.2.1 基本思想
- 利用目标函数的**二阶信息**（Hessian矩阵）
- 在当前点用二次函数近似目标函数
- 求解二次近似函数的最优解作为下一个迭代点

#### 2.2.2 算法步骤
1. 计算梯度：$g^{(k)} = \nabla f(x^{(k)})$
2. 计算Hessian：$H^{(k)} = \nabla^2 f(x^{(k)})$
3. 牛顿方向：$d^{(k)} = -[H^{(k)}]^{-1} g^{(k)}$
4. 更新点：$x^{(k+1)} = x^{(k)} + d^{(k)}$

#### 2.2.3 收敛性质
- **收敛速度**：二次收敛
- **对二次函数**：理论上1次迭代收敛
- **收敛条件**：Hessian矩阵正定

### 2.3 共轭梯度法（Conjugate Gradient Method）

#### 2.3.1 基本思想
- 构造一组**共轭方向**进行搜索
- 每次迭代选择与之前所有方向共轭的新方向
- 对于二次函数，理论上n步收敛（n为变量维数）

#### 2.3.2 算法步骤
1. 初始化：$d^{(0)} = -g^{(0)}$
2. 线搜索：$\alpha^{(k)} = \arg\min_{\alpha} f(x^{(k)} + \alpha d^{(k)})$
3. 更新点：$x^{(k+1)} = x^{(k)} + \alpha^{(k)} d^{(k)}$
4. 计算新梯度：$g^{(k+1)} = \nabla f(x^{(k+1)})$
5. 共轭系数：$\beta^{(k+1)} = \frac{||g^{(k+1)}||^2}{||g^{(k)}||^2}$ (Fletcher-Reeves)
6. 新方向：$d^{(k+1)} = -g^{(k+1)} + \beta^{(k+1)} d^{(k)}$

#### 2.3.3 收敛性质
- **收敛速度**：超线性收敛
- **对二次函数**：有限步收敛（最多n步）
- **对本问题**：理论上2步收敛

## 3. 数学公式详解

### 3.1 目标函数分析

$$f(x) = 4(x_1+1)^2 + 2(x_2-1)^2 + x_1 + x_2 + 10$$

#### 3.1.1 梯度
$$\nabla f(x) = \begin{bmatrix} 8x_1 + 9 \\ 4x_2 - 3 \end{bmatrix}$$

#### 3.1.2 Hessian矩阵
$$H = \nabla^2 f(x) = \begin{bmatrix} 8 & 0 \\ 0 & 4 \end{bmatrix}$$

**性质**：
- 对角矩阵，特征值 $\lambda_1 = 8, \lambda_2 = 4$
- 正定矩阵（所有特征值为正）
- 条件数 $\kappa = \frac{8}{4} = 2$

### 3.2 精确线搜索公式

对于二次函数，三种方法都可以使用精确线搜索：

#### 3.2.1 最速下降法
$$\alpha^{(k)} = \frac{||g^{(k)}||^2}{g^{(k)T} H g^{(k)}}$$

#### 3.2.2 共轭梯度法
$$\alpha^{(k)} = \frac{g^{(k)T} d^{(k)}}{d^{(k)T} H d^{(k)}}$$

#### 3.2.3 牛顿法
对于二次函数，牛顿法不需要线搜索，直接使用单位步长。

## 4. 伪代码实现

### 4.1 最速下降法伪代码

```
算法：最速下降法
输入：初始点 x0, 精度 ε
输出：最优点 x*

1. 初始化 x ← x0, k ← 0
2. while k < max_iter do
3.     g ← ∇f(x)
4.     if ||g|| < ε then break
5.     d ← -g
6.     α ← ||g||² / (g^T H g)  // 精确线搜索
7.     x ← x + α * d
8.     k ← k + 1
9. return x
```

### 4.2 牛顿法伪代码

```
算法：牛顿法
输入：初始点 x0, 精度 ε
输出：最优点 x*

1. 初始化 x ← x0, k ← 0
2. while k < max_iter do
3.     g ← ∇f(x)
4.     if ||g|| < ε then break
5.     H ← ∇²f(x)
6.     solve H * d = -g for d  // 牛顿方向
7.     x ← x + d
8.     k ← k + 1
9. return x
```

### 4.3 共轭梯度法伪代码

```
算法：共轭梯度法
输入：初始点 x0, 精度 ε
输出：最优点 x*

1. 初始化 x ← x0, g ← ∇f(x0), d ← -g, k ← 0
2. while k < max_iter do
3.     if ||g|| < ε then break
4.     α ← (g^T d) / (d^T H d)  // 精确线搜索
5.     x ← x + α * d
6.     g_new ← ∇f(x)
7.     β ← ||g_new||² / ||g||²  // Fletcher-Reeves公式
8.     d ← -g_new + β * d
9.     g ← g_new
10.    k ← k + 1
11. return x
```

## 5. 算法复杂度分析

### 5.1 时间复杂度

| 算法 | 每次迭代复杂度 | 预期迭代次数 | 总复杂度 |
|------|----------------|--------------|----------|
| 最速下降法 | O(n) | O(κ log(1/ε)) | O(nκ log(1/ε)) |
| 牛顿法 | O(n³) | O(log log(1/ε)) | O(n³ log log(1/ε)) |
| 共轭梯度法 | O(n²) | O(n) | O(n³) |

### 5.2 空间复杂度

| 算法 | 空间复杂度 | 主要存储 |
|------|------------|----------|
| 最速下降法 | O(n) | 当前点、梯度 |
| 牛顿法 | O(n²) | Hessian矩阵 |
| 共轭梯度法 | O(n) | 当前点、梯度、方向 |

### 5.3 对本问题的理论预测

| 算法 | 预期迭代次数 | 理论依据 |
|------|--------------|----------|
| 最速下降法 | 5-6次 | 线性收敛，收敛率1/3 |
| 牛顿法 | 1次 | 对二次函数一步收敛 |
| 共轭梯度法 | 2次 | 对二次函数n步收敛 |

## 6. 收敛性比较

### 6.1 收敛速度定义

设 $\{x^{(k)}\}$ 是算法产生的序列，$x^*$ 是最优解，定义：

- **线性收敛**：$\lim_{k \to \infty} \frac{||x^{(k+1)} - x^*||}{||x^{(k)} - x^*||} = \rho < 1$
- **二次收敛**：$\lim_{k \to \infty} \frac{||x^{(k+1)} - x^*||}{||x^{(k)} - x^*||^2} = C < \infty$
- **超线性收敛**：$\lim_{k \to \infty} \frac{||x^{(k+1)} - x^*||}{||x^{(k)} - x^*||} = 0$

### 6.2 收敛性质总结

| 算法 | 收敛类型 | 收敛率 | 对二次函数 |
|------|----------|--------|------------|
| 最速下降法 | 线性收敛 | ρ = (κ-1)/(κ+1) | 多步收敛 |
| 牛顿法 | 二次收敛 | C = O(1) | 一步收敛 |
| 共轭梯度法 | 超线性收敛 | ρ → 0 | 有限步收敛 |

## 7. 算法优缺点比较

### 7.1 最速下降法

**优点**：
- 实现简单，易于理解
- 内存需求低
- 全局收敛（对凸函数）
- 数值稳定

**缺点**：
- 收敛速度慢
- 对病态问题表现差
- 可能出现锯齿现象

### 7.2 牛顿法

**优点**：
- 收敛速度快（二次收敛）
- 对二次函数最优
- 不需要线搜索
- 理论基础扎实

**缺点**：
- 计算量大（需要Hessian）
- 内存需求高
- 要求Hessian正定
- 局部收敛

### 7.3 共轭梯度法

**优点**：
- 收敛速度快（超线性）
- 内存需求适中
- 对二次函数有限步收敛
- 不需要存储Hessian

**缺点**：
- 实现相对复杂
- 对非二次函数可能退化
- 需要精确线搜索
- 数值误差累积

## 8. 适用场景分析

### 8.1 问题规模

| 算法 | 小规模(n<100) | 中规模(100≤n<1000) | 大规模(n≥1000) |
|------|---------------|---------------------|----------------|
| 最速下降法 | 可用 | 可用 | 推荐 |
| 牛顿法 | 推荐 | 可用 | 不推荐 |
| 共轭梯度法 | 推荐 | 推荐 | 可用 |

### 8.2 函数性质

| 算法 | 二次函数 | 强凸函数 | 一般凸函数 | 非凸函数 |
|------|----------|----------|------------|----------|
| 最速下降法 | 良好 | 良好 | 良好 | 局部收敛 |
| 牛顿法 | 最优 | 优秀 | 良好 | 可能发散 |
| 共轭梯度法 | 最优 | 优秀 | 良好 | 局部收敛 |

### 8.3 计算资源

| 算法 | 计算时间 | 内存使用 | 实现难度 |
|------|----------|----------|----------|
| 最速下降法 | 中等 | 低 | 简单 |
| 牛顿法 | 高 | 高 | 中等 |
| 共轭梯度法 | 中等 | 中等 | 中等 |
