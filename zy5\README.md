# 次梯度法求解线性规划问题

## 问题描述

使用次梯度法求解线性规划问题：

$$\begin{align}
\min \quad & 2x_1 + 3x_2 + x_3 \\
\text{s.t.} \quad & -x_1 + 4x_2 + 2x_3 \geq 8 \\
& 3x_1 + 2x_2 \geq 6 \\
& x_i \geq 0, \quad i = 1,2,3
\end{align}$$

## 文件说明

### 核心文件

1. **`次梯度法算法分析.md`** - 详细的算法理论分析文档
   - 次梯度法原理和数学推导
   - 拉格朗日对偶方法
   - 收敛性分析和算法特点
   - 手工计算示例

2. **`subgradient_method_solver.py`** - 完整的Python实现
   - 包含详细的次梯度法实现
   - 支持多种步长规则
   - 包含可视化和结果导出功能

3. **`test_subgradient_method.py`** - 简化测试脚本
   - 快速验证算法正确性
   - 包含手工计算演示
   - 与单纯形法对比

## 运行方法

### 方法1：运行完整版本
```bash
python subgradient_method_solver.py
```

### 方法2：运行简化测试
```bash
python test_subgradient_method.py
```

## 算法原理

### 次梯度法基本思想

次梯度法是求解凸优化问题的一种通用方法，特别适用于非光滑优化问题。

#### 核心概念
1. **次梯度定义**: 对于凸函数 $f$，在点 $x$ 处的次梯度 $g$ 满足：
   $$f(y) \geq f(x) + g^T(y - x), \quad \forall y$$

2. **拉格朗日函数**: 
   $$L(x, \lambda) = f(x) + \sum_{i=1}^m \lambda_i g_i(x)$$

3. **迭代更新**:
   - $x^{(k+1)} = x^{(k)} - \alpha_k \nabla_x L(x^{(k)}, \lambda^{(k)})$
   - $\lambda^{(k+1)} = \max\{0, \lambda^{(k)} + \alpha_k \nabla_\lambda L(x^{(k)}, \lambda^{(k)})\}$

### 数学公式

#### 约束转换
原问题转换为标准形式：
- 约束1: $g_1(x) = 8 - (-x_1 + 4x_2 + 2x_3) \leq 0$
- 约束2: $g_2(x) = 6 - (3x_1 + 2x_2) \leq 0$
- 约束3-5: $g_{i+2}(x) = -x_i \leq 0, \quad i = 1,2,3$

#### 次梯度计算
关于 $x$ 的次梯度：
$$\nabla_x L = \begin{bmatrix} 2 \\ 3 \\ 1 \end{bmatrix} + \sum_{i=1}^5 \lambda_i \nabla g_i(x)$$

关于 $\lambda$ 的次梯度：
$$\nabla_\lambda L = [g_1(x), g_2(x), g_3(x), g_4(x), g_5(x)]^T$$

## 预期输出

### 次梯度法求解过程
```
次梯度法求解线性规划问题
============================================================
目标函数: min 2x1 + 3x2 + x3
约束条件:
  -x1 + 4x2 + 2x3 >= 8
  3x1 + 2x2 >= 6
  xi >= 0, i = 1,2,3
初始点: [1. 1. 1.]
------------------------------------------------------------
k   x1       x2       x3       f(x)       可行性   最大违反量   
------------------------------------------------------------
0   1.0000   1.0000   1.0000   6.0000     否       3.000000    
1   1.0000   4.0000   2.0000   16.0000    是       0.000000    
2   0.5000   3.0000   1.5000   12.0000    是       0.000000    
...
```

### 单纯形法对比结果
```
单纯形法求解结果（对比）:
============================================================
最优解: x* = [0.000000, 2.000000, 0.000000]
最优值: f* = 6.000000
求解成功
```

## 算法特点

### 优点
1. **通用性强**: 适用于非光滑凸优化问题
2. **实现简单**: 不需要计算Hessian矩阵
3. **内存需求低**: 只需存储当前迭代点
4. **理论保证**: 对凸问题有收敛性保证

### 缺点
1. **收敛速度慢**: $O(1/\sqrt{k})$ 的收敛速度
2. **步长选择敏感**: 步长选择对性能影响很大
3. **可能振荡**: 在最优解附近可能出现振荡
4. **精度有限**: 难以获得高精度解

### 步长规则

| 规则 | 公式 | 特点 |
|------|------|------|
| 常数步长 | $\alpha_k = \alpha$ | 简单但可能不收敛 |
| 递减步长 | $\alpha_k = \frac{1}{k+1}$ | 保证收敛但可能较慢 |
| 平方可和 | $\alpha_k = \frac{1}{\sqrt{k+1}}$ | 平衡收敛性和速度 |

## 与其他方法比较

### 次梯度法 vs 单纯形法

| 特性 | 次梯度法 | 单纯形法 |
|------|----------|----------|
| **适用问题** | 一般凸优化 | 线性规划 |
| **收敛速度** | $O(1/\sqrt{k})$ | 有限步收敛 |
| **内存需求** | $O(n)$ | $O(mn)$ |
| **实现复杂度** | 简单 | 复杂 |
| **数值稳定性** | 好 | 可能有数值问题 |
| **精度** | 中等 | 高 |

### 适用场景

**次梯度法适合**:
- 大规模问题
- 非光滑问题
- 在线优化
- 分布式优化

**单纯形法适合**:
- 中小规模线性规划
- 需要高精度解
- 理论分析

## 手工计算示例

### 第一次迭代
**初始点**: $x^{(0)} = (1, 1, 1)^T$, $\lambda^{(0)} = (1, 1, 0, 0, 0)^T$

1. **约束违反量**:
   - $g_1(x^{(0)}) = 8 + 1 - 4 - 2 = 3$
   - $g_2(x^{(0)}) = 6 - 3 - 2 = 1$
   - $g_3(x^{(0)}) = g_4(x^{(0)}) = g_5(x^{(0)}) = -1 < 0$

2. **次梯度计算**:
   - $\nabla_x L = [2, 3, 1]^T + 1 \cdot [1, -4, -2]^T + 1 \cdot [-3, -2, 0]^T = [0, -3, -1]^T$
   - $\nabla_\lambda L = [3, 1, 0, 0, 0]^T$

3. **变量更新** (步长 $\alpha = 1$):
   - $x^{(1)} = [1, 1, 1]^T - 1 \cdot [0, -3, -1]^T = [1, 4, 2]^T$
   - $\lambda^{(1)} = \max\{0, [1, 1, 0, 0, 0]^T + 1 \cdot [3, 1, 0, 0, 0]^T\} = [4, 2, 0, 0, 0]^T$

4. **结果验证**:
   - 新点 $(1, 4, 2)$ 是可行的
   - 目标函数值: $f(x^{(1)}) = 2 + 12 + 2 = 16$

## 实验设计

### 测试参数
- **初始点**: $(1, 1, 1)^T$ (不可行点)
- **初始乘子**: $(1, 1, 0, 0, 0)^T$
- **最大迭代次数**: 100
- **收敛容差**: $10^{-6}$
- **步长规则**: 递减步长 $\alpha_k = 1/(k+1)$

### 评价指标
1. **收敛性**: 是否找到可行解
2. **最优性**: 目标函数值与理论最优值的差距
3. **约束满足**: 约束违反量
4. **迭代效率**: 达到可行解所需的迭代次数

## 输出文件

运行完整版本后会生成：
- **`subgradient_convergence.png`** - 收敛过程图
- **`subgradient_results.csv`** - 详细的迭代历史数据

## 学习价值

### 理论意义
1. **凸优化基础**: 理解次梯度和凸优化的基本概念
2. **对偶理论**: 学习拉格朗日对偶方法
3. **收敛分析**: 理解非光滑优化的收敛性质

### 实践技能
1. **算法实现**: 从数学公式到代码实现
2. **约束处理**: 学习处理不等式约束的技巧
3. **数值优化**: 掌握步长选择和收敛判断

### 应用价值
1. **机器学习**: 处理非光滑损失函数
2. **信号处理**: 稀疏优化问题
3. **网络优化**: 分布式优化算法
4. **经济学**: 均衡问题求解

## 总结

次梯度法虽然不是求解线性规划问题的最优选择，但它提供了一个通用的框架来理解和处理更广泛的凸优化问题。通过本项目的实现，可以：

1. **深入理解凸优化理论**: 次梯度、对偶性、收敛性
2. **掌握实用的优化技能**: 约束处理、算法实现、性能分析
3. **为高级算法奠定基础**: 理解现代优化算法的基本思想
4. **培养工程实践能力**: 算法调试、参数选择、结果验证

次梯度法的学习价值在于其通用性和理论完备性，为理解更复杂的优化算法提供了重要的理论基础。
