#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗智能问答系统 - 主应用文件
集成所有功能模块的主Flask应用
"""

from flask import Flask, render_template, redirect, url_for, session
import os
import sys

# 尝试导入CORS，如果失败则跳过
try:
    from flask_cors import CORS
    CORS_AVAILABLE = True
except ImportError:
    CORS_AVAILABLE = False
    print("警告: flask-cors未安装，跨域功能将不可用")

# 添加apps目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'apps'))

# 导入各个功能模块
from apps.auth_app import auth_bp
from apps.chat_app import chat_bp
from apps.knowledge_graph_app import kg_bp
from apps.admin_app import admin_bp

# 创建Flask应用
app = Flask(__name__)
app.secret_key = 'medical-qa-system-secret-key-change-in-production'

# 如果CORS可用则启用
if CORS_AVAILABLE:
    CORS(app)

# 注册蓝图
app.register_blueprint(auth_bp)
app.register_blueprint(chat_bp)
app.register_blueprint(kg_bp)
app.register_blueprint(admin_bp)

# 主页路由
@app.route('/')
def index():
    """主页"""
    if 'user_id' not in session:
        return redirect(url_for('auth.login_page'))
    return render_template('index.html')

# 错误处理
@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    print(f"404错误: {error}")
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    print(f"500错误: {error}")
    return render_template('500.html'), 500

if __name__ == '__main__':
    print("=" * 50)
    print("医疗智能问答系统启动中...")
    print("=" * 50)
    print("🏠 主页: http://localhost:5000")
    print("💬 智能问答: http://localhost:5000/chat")
    print("📊 知识图谱: http://localhost:5000/knowledge-graph")
    print("🔧 管理面板: http://localhost:5000/admin")
    print("=" * 50)
    app.run(debug=True, host='0.0.0.0', port=5000)
