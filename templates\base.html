<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}医疗智能问答系统{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-heartbeat me-2"></i>
                医疗智能问答系统
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('chat.chat_page') }}">
                            <i class="fas fa-comments me-1"></i>智能问答
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('knowledge_graph.knowledge_graph_page') }}">
                            <i class="fas fa-project-diagram me-1"></i>知识图谱
                        </a>
                    </li>
                    {% if session.get('is_admin') %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.admin_page') }}">
                            <i class="fas fa-cog me-1"></i>管理面板
                        </a>
                    </li>
                    {% endif %}
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>用户中心
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile_page') }}">
                                <i class="fas fa-user-circle me-1"></i>个人资料
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.settings_page') }}">
                                <i class="fas fa-cog me-1"></i>账户设置
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.messages_page') }}">
                                <i class="fas fa-bell me-1"></i>消息中心
                                <span class="badge bg-danger ms-1" id="messageCount" style="display: none;">0</span>
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.help_page') }}">
                                <i class="fas fa-question-circle me-1"></i>帮助中心
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt me-1"></i>退出登录
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        {% block content %}{% endblock %}
    </main>

    <!-- 返回顶部按钮 -->
    <button id="backToTop" class="btn btn-primary" style="display: none; position: fixed; bottom: 20px; right: 20px; z-index: 1000; border-radius: 50%; width: 50px; height: 50px;">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>医疗智能问答系统</h5>
                    <p class="mb-0">基于RAG与大模型技术的医疗问答系统</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">© 2024 医疗智能问答系统. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    {% block extra_js %}{% endblock %}

    <script>
        // 全局退出登录函数
        function logout() {
            fetch('/api/logout', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = '{{ url_for("auth.login_page") }}';
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }
    </script>
</body>
</html>
