#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载BERT模型脚本
下载chinese-roberta-wwm-ext模型到本地
"""

import os
import sys
from transformers import BertTokenizer, BertModel

def download_bert_model():
    """下载BERT模型到本地"""
    model_name = 'hfl/chinese-roberta-wwm-ext'
    local_path = 'model/chinese-roberta-wwm-ext'

    print(f"开始下载BERT模型: {model_name}")
    print(f"保存路径: {local_path}")

    try:
        # 创建目录
        os.makedirs(local_path, exist_ok=True)

        # 下载tokenizer
        print("下载tokenizer...")
        tokenizer = BertTokenizer.from_pretrained(model_name)
        tokenizer.save_pretrained(local_path)
        print("✓ Tokenizer下载完成")

        # 下载模型
        print("下载BERT模型...")
        model = BertModel.from_pretrained(model_name)
        model.save_pretrained(local_path)
        print("✓ BERT模型下载完成")

        # 验证文件
        required_files = [
            'config.json',
            'pytorch_model.bin',
            'tokenizer.json',
            'tokenizer_config.json',
            'vocab.txt'
        ]

        print("\n验证下载的文件:")
        for file_name in required_files:
            file_path = os.path.join(local_path, file_name)
            if os.path.exists(file_path):
                size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                print(f"  ✓ {file_name} ({size:.1f}MB)")
            else:
                print(f"  ✗ {file_name} (缺失)")

        print(f"\n✅ BERT模型下载完成！")
        print(f"模型保存在: {os.path.abspath(local_path)}")

        return True

    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False

def check_internet_connection():
    """检查网络连接"""
    try:
        import requests
        # 尝试多个URL
        urls = [
            'https://huggingface.co',
            'https://www.baidu.com',
            'https://www.google.com'
        ]

        for url in urls:
            try:
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    return True
            except:
                continue
        return False
    except ImportError:
        print("❌ 缺少requests库，请安装: pip install requests")
        return False
    except:
        return False

def manual_download_guide():
    """手动下载指导"""
    print("\n" + "="*60)
    print("🔧 手动下载BERT模型指导")
    print("="*60)
    print("\n由于网络问题，您可以手动下载模型：")
    print("\n方法1: 使用镜像站点")
    print("1. 访问: https://hf-mirror.com/hfl/chinese-roberta-wwm-ext")
    print("2. 下载所有文件到: model/chinese-roberta-wwm-ext/")
    print("   需要的文件:")
    print("   - config.json")
    print("   - pytorch_model.bin")
    print("   - tokenizer.json")
    print("   - tokenizer_config.json")
    print("   - vocab.txt")

    print("\n方法2: 使用Git LFS")
    print("1. 安装git-lfs: https://git-lfs.github.io/")
    print("2. 运行命令:")
    print("   cd model")
    print("   git clone https://huggingface.co/hfl/chinese-roberta-wwm-ext")

    print("\n方法3: 修改环境变量使用镜像")
    print("设置环境变量: HF_ENDPOINT=https://hf-mirror.com")
    print("然后重新运行此脚本")

    print("\n完成后，重新启动系统即可使用本地模型！")
    print("="*60)

if __name__ == "__main__":
    print("BERT模型下载工具")
    print("=" * 50)

    # 检查是否已存在
    if os.path.exists('model/chinese-roberta-wwm-ext/config.json'):
        print("✓ BERT模型已存在，无需重新下载")
        sys.exit(0)

    # 检查网络连接
    if not check_internet_connection():
        print("❌ 无法连接到网络，无法自动下载模型")
        manual_download_guide()
        sys.exit(1)

    # 尝试使用镜像站点
    print("尝试使用HuggingFace镜像站点...")
    os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

    # 下载模型
    success = download_bert_model()

    if success:
        print("\n🎉 下载完成！现在可以重新启动系统了")
    else:
        print("\n❌ 自动下载失败")
        manual_download_guide()
        sys.exit(1)
