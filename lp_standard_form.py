#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
线性规划标准形式转换
"""

import numpy as np
from scipy.optimize import linprog

def problem_1_analysis():
    """
    问题1: max 3x₁ - 2x₂ + x₃
    s.t. -x₁ - 2x₂ + x₃ ≤ 6
         x₁ - x₂ + 4x₃ ≥ 3
         x₁ ≥ 0, x₂ ≥ 0
    """
    print("=" * 70)
    print("问题1: 线性规划标准形式转换")
    print("=" * 70)
    
    print("原问题:")
    print("max  3x₁ - 2x₂ + x₃")
    print("s.t. -x₁ - 2x₂ + x₃ ≤ 6")
    print("      x₁ - x₂ + 4x₃ ≥ 3")
    print("      x₁ ≥ 0, x₂ ≥ 0")
    print()
    
    print("标准形式转换步骤:")
    print("1. 目标函数: max → min")
    print("   min -3x₁ + 2x₂ - x₃")
    print()
    
    print("2. 处理无约束变量x₃:")
    print("   设 x₃ = x₃⁺ - x₃⁻, 其中 x₃⁺ ≥ 0, x₃⁻ ≥ 0")
    print("   目标函数: min -3x₁ + 2x₂ - x₃⁺ + x₃⁻")
    print()
    
    print("3. 约束条件转换:")
    print("   约束1: -x₁ - 2x₂ + x₃⁺ - x₃⁻ ≤ 6")
    print("   约束2: x₁ - x₂ + 4x₃⁺ - 4x₃⁻ ≥ 3")
    print("          → -x₁ + x₂ - 4x₃⁺ + 4x₃⁻ ≤ -3")
    print()
    
    print("4. 添加松弛变量:")
    print("   约束1: -x₁ - 2x₂ + x₃⁺ - x₃⁻ + s₁ = 6")
    print("   约束2: -x₁ + x₂ - 4x₃⁺ + 4x₃⁻ + s₂ = -3")
    print()
    
    print("5. 标准形式:")
    print("   变量: [x₁, x₂, x₃⁺, x₃⁻, s₁, s₂] ≥ 0")
    print("   目标: min [-3, 2, -1, 1, 0, 0] · [x₁, x₂, x₃⁺, x₃⁻, s₁, s₂]")
    print("   约束矩阵 A:")
    A = np.array([
        [-1, -2,  1, -1,  1,  0],
        [-1,  1, -4,  4,  0,  1]
    ])
    print(A)
    print("   右端向量 b: [6, -3]")
    print()
    
    # 分析可行性
    print("6. 可行性分析:")
    print("   由于约束2的右端为负数(-3)，且所有变量非负，")
    print("   约束2: -x₁ + x₂ - 4x₃⁺ + 4x₃⁻ = -3 要求左端为负数")
    print("   这要求 x₁ > x₂ + 4x₃⁺ - 4x₃⁻")
    print()
    
    # 尝试求解
    print("7. 数值求解:")
    c = np.array([-3, 2, -1, 1])
    A_ub = np.array([
        [-1, -2,  1, -1],
        [-1,  1, -4,  4]
    ])
    b_ub = np.array([6, -3])
    bounds = [(0, None), (0, None), (0, None), (0, None)]
    
    result = linprog(c, A_ub=A_ub, b_ub=b_ub, bounds=bounds, method='highs')
    
    if result.success:
        x1, x2, x3_pos, x3_neg = result.x
        x3 = x3_pos - x3_neg
        print(f"   最优解: x₁ = {x1:.6f}, x₂ = {x2:.6f}, x₃ = {x3:.6f}")
        print(f"   最优值(最大化): {-result.fun:.6f}")
    else:
        print(f"   求解状态: {result.message}")
        if "unbounded" in result.message.lower():
            print("   分析: 问题无界，可能是因为x₃可以无限增大")
            print("   在原最大化问题中，x₃系数为正，且x₃无上界约束")
    
    return result

def problem_2_analysis():
    """
    问题2: min |x₁| + |x₂| + 3|x₃|
    s.t. x₁ + 3x₂ - 2x₃ ≤ 4
         x₁ - 2x₂ + x₃ ≥ 5
    """
    print("\n" + "=" * 70)
    print("问题2: 绝对值线性规划标准形式转换")
    print("=" * 70)
    
    print("原问题:")
    print("min |x₁| + |x₂| + 3|x₃|")
    print("s.t. x₁ + 3x₂ - 2x₃ ≤ 4")
    print("     x₁ - 2x₂ + x₃ ≥ 5")
    print()
    
    print("标准形式转换步骤:")
    print("1. 处理绝对值:")
    print("   对每个变量xᵢ，设 xᵢ = uᵢ - vᵢ，其中 uᵢ ≥ 0, vᵢ ≥ 0")
    print("   则 |xᵢ| = uᵢ + vᵢ")
    print()
    print("   x₁ = u₁ - v₁, |x₁| = u₁ + v₁")
    print("   x₂ = u₂ - v₂, |x₂| = u₂ + v₂")
    print("   x₃ = u₃ - v₃, |x₃| = u₃ + v₃")
    print()
    
    print("2. 目标函数转换:")
    print("   min |x₁| + |x₂| + 3|x₃|")
    print("   = min (u₁ + v₁) + (u₂ + v₂) + 3(u₃ + v₃)")
    print("   = min u₁ + v₁ + u₂ + v₂ + 3u₃ + 3v₃")
    print()
    
    print("3. 约束条件转换:")
    print("   约束1: x₁ + 3x₂ - 2x₃ ≤ 4")
    print("          (u₁-v₁) + 3(u₂-v₂) - 2(u₃-v₃) ≤ 4")
    print("          u₁ - v₁ + 3u₂ - 3v₂ - 2u₃ + 2v₃ ≤ 4")
    print()
    print("   约束2: x₁ - 2x₂ + x₃ ≥ 5")
    print("          (u₁-v₁) - 2(u₂-v₂) + (u₃-v₃) ≥ 5")
    print("          u₁ - v₁ - 2u₂ + 2v₂ + u₃ - v₃ ≥ 5")
    print("          → -u₁ + v₁ + 2u₂ - 2v₂ - u₃ + v₃ ≤ -5")
    print()
    
    print("4. 标准形式:")
    print("   变量: [u₁, v₁, u₂, v₂, u₃, v₃] ≥ 0")
    print("   目标: min [1, 1, 1, 1, 3, 3] · [u₁, v₁, u₂, v₂, u₃, v₃]")
    print("   约束矩阵:")
    A_ub = np.array([
        [ 1, -1,  3, -3, -2,  2],
        [-1,  1,  2, -2, -1,  1]
    ])
    print(A_ub)
    print("   右端向量: [4, -5]")
    print()
    
    print("5. 数值求解:")
    c = np.array([1, 1, 1, 1, 3, 3])
    b_ub = np.array([4, -5])
    bounds = [(0, None)] * 6
    
    result = linprog(c, A_ub=A_ub, b_ub=b_ub, bounds=bounds, method='highs')
    
    if result.success:
        u1, v1, u2, v2, u3, v3 = result.x
        x1 = u1 - v1
        x2 = u2 - v2
        x3 = u3 - v3
        
        print(f"   最优解: x₁ = {x1:.6f}, x₂ = {x2:.6f}, x₃ = {x3:.6f}")
        print(f"   最优值: {result.fun:.6f}")
        
        # 验证
        print("\n   验证:")
        obj_val = abs(x1) + abs(x2) + 3*abs(x3)
        constraint1 = x1 + 3*x2 - 2*x3
        constraint2 = x1 - 2*x2 + x3
        
        print(f"   目标函数: |{x1:.3f}| + |{x2:.3f}| + 3|{x3:.3f}| = {obj_val:.6f}")
        print(f"   约束1: {x1:.3f} + 3×{x2:.3f} - 2×{x3:.3f} = {constraint1:.3f} ≤ 4 {'✓' if constraint1 <= 4.001 else '✗'}")
        print(f"   约束2: {x1:.3f} - 2×{x2:.3f} + {x3:.3f} = {constraint2:.3f} ≥ 5 {'✓' if constraint2 >= 4.999 else '✗'}")
    else:
        print(f"   求解失败: {result.message}")
    
    return result

def main():
    print("线性规划标准形式转换与求解")
    
    # 问题1
    result1 = problem_1_analysis()
    
    # 问题2
    result2 = problem_2_analysis()
    
    print("\n" + "=" * 70)
    print("总结")
    print("=" * 70)
    print("标准形式转换的关键技术:")
    print("1. 最大化 → 最小化: 目标函数系数取负")
    print("2. 无约束变量: xᵢ = xᵢ⁺ - xᵢ⁻ (xᵢ⁺, xᵢ⁻ ≥ 0)")
    print("3. ≥ 约束 → ≤ 约束: 两边同乘-1")
    print("4. 绝对值: |xᵢ| = xᵢ⁺ + xᵢ⁻, xᵢ = xᵢ⁺ - xᵢ⁻")
    print("5. 等式约束: 添加松弛变量")

if __name__ == "__main__":
    main()
