{% extends "base.html" %}

{% block title %}消息中心 - 医疗智能问答系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <!-- 左侧导航 -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>用户中心</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('auth.profile_page') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-user-circle me-2"></i>个人资料
                    </a>
                    <a href="{{ url_for('auth.settings_page') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-cog me-2"></i>账户设置
                    </a>
                    <a href="{{ url_for('auth.messages_page') }}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-bell me-2"></i>消息中心
                    </a>
                    <a href="{{ url_for('auth.help_page') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-question-circle me-2"></i>帮助中心
                    </a>
                </div>
            </div>
        </div>

        <!-- 右侧内容 -->
        <div class="col-md-9">
            <!-- 消息统计 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h4 class="text-primary">5</h4>
                            <p class="mb-0">未读消息</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h4 class="text-success">23</h4>
                            <p class="mb-0">系统通知</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h4 class="text-info">8</h4>
                            <p class="mb-0">更新提醒</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h4 class="text-warning">2</h4>
                            <p class="mb-0">安全提醒</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 消息过滤 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="btn-group" role="group">
                                <input type="radio" class="btn-check" name="messageFilter" id="all" checked>
                                <label class="btn btn-outline-primary" for="all">全部</label>

                                <input type="radio" class="btn-check" name="messageFilter" id="unread">
                                <label class="btn btn-outline-primary" for="unread">未读</label>

                                <input type="radio" class="btn-check" name="messageFilter" id="system">
                                <label class="btn btn-outline-primary" for="system">系统</label>

                                <input type="radio" class="btn-check" name="messageFilter" id="security">
                                <label class="btn btn-outline-primary" for="security">安全</label>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <button class="btn btn-outline-secondary" onclick="markAllRead()">
                                <i class="fas fa-check-double me-1"></i>全部标记为已读
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 消息列表 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-inbox me-2"></i>消息列表</h5>
                </div>
                <div class="card-body p-0">
                    <div id="messagesList">
                        <!-- 消息项 -->
                        <div class="message-item border-bottom p-3" data-type="system" data-read="false">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <div class="message-icon bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="fas fa-info"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">系统更新通知</h6>
                                            <p class="mb-1 text-muted">医疗智能问答系统已更新到v2.1版本，新增了知识图谱可视化功能。</p>
                                            <small class="text-muted">2024-01-20 14:30</small>
                                        </div>
                                        <div class="message-actions">
                                            <span class="badge bg-danger">未读</span>
                                            <button class="btn btn-sm btn-outline-primary ms-2" onclick="markAsRead(this)">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="message-item border-bottom p-3" data-type="security" data-read="false">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <div class="message-icon bg-warning text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">安全提醒</h6>
                                            <p class="mb-1 text-muted">检测到您的账户在新设备上登录，如非本人操作请及时修改密码。</p>
                                            <small class="text-muted">2024-01-19 09:15</small>
                                        </div>
                                        <div class="message-actions">
                                            <span class="badge bg-danger">未读</span>
                                            <button class="btn btn-sm btn-outline-primary ms-2" onclick="markAsRead(this)">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="message-item border-bottom p-3" data-type="system" data-read="true">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <div class="message-icon bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="fas fa-gift"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">欢迎使用</h6>
                                            <p class="mb-1 text-muted">欢迎使用医疗智能问答系统！您可以开始提问医疗相关问题了。</p>
                                            <small class="text-muted">2024-01-18 16:45</small>
                                        </div>
                                        <div class="message-actions">
                                            <span class="badge bg-secondary">已读</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="message-item border-bottom p-3" data-type="system" data-read="true">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <div class="message-icon bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">账户创建成功</h6>
                                            <p class="mb-1 text-muted">您的账户已成功创建，请完善个人资料以获得更好的服务体验。</p>
                                            <small class="text-muted">2024-01-18 10:20</small>
                                        </div>
                                        <div class="message-actions">
                                            <span class="badge bg-secondary">已读</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 空状态 -->
                    <div id="emptyState" class="text-center py-5" style="display: none;">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无消息</h5>
                        <p class="text-muted">当有新消息时，会在这里显示</p>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <nav class="mt-4">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1">上一页</a>
                    </li>
                    <li class="page-item active">
                        <a class="page-link" href="#">1</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#">2</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#">3</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#">下一页</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载时初始化
$(document).ready(function() {
    loadMessages();

    // 消息过滤
    $('input[name="messageFilter"]').change(function() {
        loadMessages($(this).attr('id'));
    });
});

// 加载消息
function loadMessages(filter = 'all') {
    let params = {};

    switch(filter) {
        case 'unread':
            params.read = 'false';
            break;
        case 'system':
            params.type = 'system';
            break;
        case 'security':
            params.type = 'security';
            break;
    }

    const queryString = new URLSearchParams(params).toString();
    const url = `/api/user/messages${queryString ? '?' + queryString : ''}`;

    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayMessages(data.messages);
                updateMessageCount(data.unread_count);
                updateStatistics(data.messages);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', '加载消息失败');
        });
}

// 显示消息
function displayMessages(messages) {
    const messagesList = $('#messagesList');

    if (messages.length === 0) {
        messagesList.html('<div class="text-center py-5"><i class="fas fa-inbox fa-3x text-muted mb-3"></i><h5 class="text-muted">暂无消息</h5></div>');
        return;
    }

    const messagesHtml = messages.map(message => {
        const iconClass = getMessageIcon(message.type);
        const iconColor = getMessageIconColor(message.type);
        const badgeClass = message.read ? 'bg-secondary' : 'bg-danger';
        const badgeText = message.read ? '已读' : '未读';

        return `
            <div class="message-item border-bottom p-3" data-type="${message.type}" data-read="${message.read}" data-id="${message.id}">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <div class="message-icon ${iconColor} text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                            <i class="fas ${iconClass}"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">${message.title}</h6>
                                <p class="mb-1 text-muted">${message.content}</p>
                                <small class="text-muted">${formatDateTime(message.timestamp)}</small>
                            </div>
                            <div class="message-actions">
                                <span class="badge ${badgeClass}">${badgeText}</span>
                                ${!message.read ? `<button class="btn btn-sm btn-outline-primary ms-2" onclick="markAsRead('${message.id}', this)"><i class="fas fa-check"></i></button>` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    messagesList.html(messagesHtml);
}

// 获取消息图标
function getMessageIcon(type) {
    switch(type) {
        case 'system': return 'fa-info';
        case 'security': return 'fa-shield-alt';
        case 'update': return 'fa-download';
        default: return 'fa-bell';
    }
}

// 获取消息图标颜色
function getMessageIconColor(type) {
    switch(type) {
        case 'system': return 'bg-primary';
        case 'security': return 'bg-warning';
        case 'update': return 'bg-info';
        default: return 'bg-success';
    }
}

// 更新统计信息
function updateStatistics(messages) {
    const unreadCount = messages.filter(m => !m.read).length;
    const systemCount = messages.filter(m => m.type === 'system').length;
    const updateCount = messages.filter(m => m.type === 'update').length;
    const securityCount = messages.filter(m => m.type === 'security').length;

    $('.row .card .card-body h4').eq(0).text(unreadCount);
    $('.row .card .card-body h4').eq(1).text(systemCount);
    $('.row .card .card-body h4').eq(2).text(updateCount);
    $('.row .card .card-body h4').eq(3).text(securityCount);
}

// 过滤消息
function filterMessages(filter) {
    const messages = $('.message-item');

    messages.each(function() {
        const $message = $(this);
        const type = $message.data('type');
        const isRead = $message.data('read');

        let show = true;

        switch(filter) {
            case 'unread':
                show = !isRead;
                break;
            case 'system':
                show = type === 'system';
                break;
            case 'security':
                show = type === 'security';
                break;
            case 'all':
            default:
                show = true;
                break;
        }

        $message.toggle(show);
    });

    // 检查是否有可见消息
    const visibleMessages = $('.message-item:visible').length;
    $('#emptyState').toggle(visibleMessages === 0);
}

// 标记单条消息为已读
function markAsRead(messageId, button) {
    fetch(`/api/user/messages/${messageId}/read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const $messageItem = $(button).closest('.message-item');
            const $badge = $messageItem.find('.badge');

            $messageItem.attr('data-read', 'true');
            $badge.removeClass('bg-danger').addClass('bg-secondary').text('已读');
            $(button).remove();

            updateMessageCount(data.unread_count);
            showAlert('success', data.message);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', '操作失败，请重试');
    });
}

// 标记所有消息为已读
function markAllRead() {
    fetch('/api/user/messages/mark-all-read', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('.message-item[data-read="false"]').each(function() {
                const $messageItem = $(this);
                const $badge = $messageItem.find('.badge');
                const $button = $messageItem.find('.message-actions button');

                $messageItem.attr('data-read', 'true');
                $badge.removeClass('bg-danger').addClass('bg-secondary').text('已读');
                $button.remove();
            });

            updateMessageCount(data.unread_count);
            showAlert('success', data.message);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', '操作失败，请重试');
    });
}

// 更新消息计数
function updateMessageCount(count) {
    const $messageCount = $('#messageCount');

    if (count > 0) {
        $messageCount.text(count).show();
    } else {
        $messageCount.hide();
    }
}

// 格式化日期时间
function formatDateTime(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;

    // 如果是今天
    if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
        return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    }

    // 如果是昨天
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    if (date.getDate() === yesterday.getDate()) {
        return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    }

    // 其他日期
    return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
}

// 显示提示消息
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container').prepend(alertHtml);

    // 3秒后自动消失
    setTimeout(() => {
        $('.alert').alert('close');
    }, 3000);
}
</script>

<style>
.message-item:hover {
    background-color: #f8f9fa;
}

.message-item[data-read="false"] {
    background-color: #fff3cd;
}

.message-icon {
    font-size: 16px;
}

.message-actions {
    display: flex;
    align-items: center;
}
</style>
{% endblock %}
