{% extends "base.html" %}

{% block title %}知识图谱 - 医疗智能问答系统{% endblock %}

{% block extra_css %}
<style>
.graph-container {
    height: 600px;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    background: #f8f9fa;
    position: relative;
}

.graph-controls {
    background: white;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.node-info {
    background: white;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    min-height: 200px;
}

.legend {
    background: white;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.search-results {
    max-height: 300px;
    overflow-y: auto;
}

.result-item {
    padding: 0.5rem;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    transition: background-color 0.2s;
}

.result-item:hover {
    background-color: #f8f9fa;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    z-index: 1000;
}

.node-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.95);
    color: white;
    padding: 12px;
    border-radius: 8px;
    font-size: 12px;
    pointer-events: none;
    z-index: 2000;
    max-width: 300px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.node-tooltip.show {
    opacity: 1;
}

.tooltip-title {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 8px;
    color: #fff;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 5px;
}

.tooltip-type {
    color: #ffd700;
    font-size: 12px;
    margin-bottom: 5px;
    font-weight: 500;
}

.tooltip-connections {
    color: #87ceeb;
    font-size: 11px;
    margin-bottom: 5px;
}

.tooltip-related {
    color: #98fb98;
    font-size: 10px;
    margin-top: 8px;
    padding-top: 5px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.tooltip-related-title {
    font-weight: bold;
    margin-bottom: 3px;
}

.tooltip-related-item {
    margin-bottom: 2px;
    padding-left: 5px;
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    

    <!-- 统计信息 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number" id="nodeCount">-</div>
                <div>节点数量</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number" id="relationshipCount">-</div>
                <div>关系数量</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number" id="nodeTypeCount">-</div>
                <div>实体类型</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number" id="relationshipTypeCount">-</div>
                <div>关系类型</div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 控制面板 -->
        <div class="col-md-2">
            <div class="graph-controls mb-3">
                <h5><i class="fas fa-search me-2"></i>搜索</h5>
                <div class="mb-3">
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索医疗实体...">
                    <button class="btn btn-primary w-100 mt-2" onclick="searchGraph()">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                </div>

                <h6>实体类型过滤</h6>
                <div class="mb-3">
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        <label class="form-check-label fw-bold" for="selectAll">
                            全选/全不选
                        </label>
                    </div>
                    <hr class="my-2">
                    <div class="form-check mb-2">
                        <input class="form-check-input node-type-filter" type="checkbox" value="疾病" id="filter_疾病" onchange="applyFilter()">
                        <label class="form-check-label d-flex align-items-center" for="filter_疾病">
                            <div class="legend-color me-2" style="background: #ff6b6b; width: 16px; height: 16px; border-radius: 50%;"></div>
                            疾病
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input node-type-filter" type="checkbox" value="药品" id="filter_药品" onchange="applyFilter()">
                        <label class="form-check-label d-flex align-items-center" for="filter_药品">
                            <div class="legend-color me-2" style="background: #4ecdc4; width: 16px; height: 16px; border-radius: 50%;"></div>
                            药品
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input node-type-filter" type="checkbox" value="食物" id="filter_食物" onchange="applyFilter()">
                        <label class="form-check-label d-flex align-items-center" for="filter_食物">
                            <div class="legend-color me-2" style="background: #45b7d1; width: 16px; height: 16px; border-radius: 50%;"></div>
                            食物
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input node-type-filter" type="checkbox" value="检查项目" id="filter_检查项目" onchange="applyFilter()">
                        <label class="form-check-label d-flex align-items-center" for="filter_检查项目">
                            <div class="legend-color me-2" style="background: #f9ca24; width: 16px; height: 16px; border-radius: 50%;"></div>
                            检查项目
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input node-type-filter" type="checkbox" value="科目" id="filter_科目" onchange="applyFilter()">
                        <label class="form-check-label d-flex align-items-center" for="filter_科目">
                            <div class="legend-color me-2" style="background: #6c5ce7; width: 16px; height: 16px; border-radius: 50%;"></div>
                            科目
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input node-type-filter" type="checkbox" value="疾病症状" id="filter_疾病症状" onchange="applyFilter()">
                        <label class="form-check-label d-flex align-items-center" for="filter_疾病症状">
                            <div class="legend-color me-2" style="background: #a29bfe; width: 16px; height: 16px; border-radius: 50%;"></div>
                            疾病症状
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input node-type-filter" type="checkbox" value="治疗方法" id="filter_治疗方法" onchange="applyFilter()">
                        <label class="form-check-label d-flex align-items-center" for="filter_治疗方法">
                            <div class="legend-color me-2" style="background: #fd79a8; width: 16px; height: 16px; border-radius: 50%;"></div>
                            治疗方法
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input node-type-filter" type="checkbox" value="药品商" id="filter_药品商" onchange="applyFilter()">
                        <label class="form-check-label d-flex align-items-center" for="filter_药品商">
                            <div class="legend-color me-2" style="background: #00b894; width: 16px; height: 16px; border-radius: 50%;"></div>
                            药品商
                        </label>
                    </div>
                </div>

                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="loadRandomNodes()">
                        <i class="fas fa-random me-1"></i>随机加载
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearGraph()">
                        <i class="fas fa-trash me-1"></i>清空图谱
                    </button>
                    <button class="btn btn-outline-info" onclick="resetView()">
                        <i class="fas fa-expand me-1"></i>重置视图
                    </button>
                </div>
            </div>




        </div>

        <!-- 图谱可视化区域 -->
        <div class="col-md-10">
            <div class="graph-container" id="graphContainer">
                <div class="loading-overlay" id="loadingOverlay">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2">正在加载知识图谱...</div>
                    </div>
                </div>
                <div id="graph"></div>
                <!-- 节点悬浮提示框 -->
                <div id="nodeTooltip" class="node-tooltip">
                    <div class="tooltip-title"></div>
                    <div class="tooltip-type"></div>
                    <div class="tooltip-connections"></div>
                    <div class="tooltip-related">
                        <div class="tooltip-related-title">相关节点:</div>
                        <div class="tooltip-related-content"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索结果模态框 -->
    <div class="modal fade" id="searchModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">搜索结果</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="search-results" id="searchResults">
                        <!-- 搜索结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- D3.js -->
<script src="https://d3js.org/d3.v7.min.js"></script>

<script>
let svg, simulation, nodes = [], links = [];
let originalNodes = [], originalLinks = []; // 保存原始数据用于过滤
let nodeTypeColors = {
    '疾病': '#ff6b6b',
    '药品': '#4ecdc4',
    '食物': '#45b7d1',
    '检查项目': '#f9ca24',
    '科目': '#6c5ce7',
    '疾病症状': '#a29bfe',
    '治疗方法': '#fd79a8',
    '药品商': '#00b894'
};

$(document).ready(function() {
    initGraph();

    // 默认全选所有类型
    $('.node-type-filter').prop('checked', true);
    $('#selectAll').prop('checked', true);

    loadRandomNodes();
});

function initGraph() {
    const container = d3.select("#graph");
    const width = $('#graphContainer').width();
    const height = $('#graphContainer').height();

    svg = container.append("svg")
        .attr("width", width)
        .attr("height", height);

    // 添加缩放功能
    const zoom = d3.zoom()
        .scaleExtent([0.1, 4])
        .on("zoom", function(event) {
            svg.selectAll("g").attr("transform", event.transform);
        });

    svg.call(zoom);

    // 创建力导向图
    simulation = d3.forceSimulation()
        .force("link", d3.forceLink().id(d => d.id).distance(100))
        .force("charge", d3.forceManyBody().strength(-300))
        .force("center", d3.forceCenter(width / 2, height / 2));
}

function loadRandomNodes() {
    showLoading();

    // 使用新的连接数据API，一次获取节点和关系
    fetch('/api/knowledge-graph/connected-data?limit=50')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 保存原始数据
                originalNodes = data.nodes;
                originalLinks = data.relationships;

                // 设置当前显示的数据
                nodes = [...originalNodes];
                links = [...originalLinks];

                updateStatistics();

                console.log('加载的节点:', nodes.length);
                console.log('加载的关系:', links.length);
                console.log('节点示例:', nodes.slice(0, 3));
                console.log('关系示例:', links.slice(0, 3));

                updateGraph();
            } else {
                throw new Error(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('加载数据失败: ' + error.message);
        })
        .finally(() => {
            hideLoading();
        });
}

function searchGraph() {
    const keyword = $('#searchInput').val().trim();
    if (!keyword) {
        alert('请输入搜索关键词');
        return;
    }

    showLoading();

    fetch(`/api/knowledge-graph/search?keyword=${encodeURIComponent(keyword)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 保存搜索结果为原始数据
                originalNodes = data.nodes;
                originalLinks = data.relationships;

                // 设置当前显示的数据
                nodes = [...originalNodes];
                links = [...originalLinks];

                updateStatistics();
                updateGraph();

                // 重置过滤器
                $('.node-type-filter').prop('checked', false);
                $('#selectAll').prop('checked', false).prop('indeterminate', false);

                if (nodes.length === 0) {
                    alert('未找到相关结果');
                }
            } else {
                throw new Error(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('搜索失败: ' + error.message);
        })
        .finally(() => {
            hideLoading();
        });
}

function updateGraph() {
    // 清除现有图形
    svg.selectAll("*").remove();

    // 确保节点数据格式正确
    if (!nodes || nodes.length === 0) {
        console.log('没有节点数据');
        return;
    }

    // 确保链接数据格式正确，并过滤掉无效的链接
    const validLinks = links.filter(link => {
        const sourceExists = nodes.some(node => node.id === link.source);
        const targetExists = nodes.some(node => node.id === link.target);
        return sourceExists && targetExists;
    });

    console.log('节点数量:', nodes.length);
    console.log('有效链接数量:', validLinks.length);

    // 创建链接
    const link = svg.append("g")
        .selectAll("line")
        .data(validLinks)
        .enter().append("line")
        .attr("stroke", "#999")
        .attr("stroke-opacity", 0.6)
        .attr("stroke-width", 2);

    // 创建节点
    const node = svg.append("g")
        .selectAll("circle")
        .data(nodes)
        .enter().append("circle")
        .attr("r", 8)
        .attr("fill", d => nodeTypeColors[d.type] || "#999")
        .attr("stroke", "#fff")
        .attr("stroke-width", 2)
        .call(d3.drag()
            .on("start", dragstarted)
            .on("drag", dragged)
            .on("end", dragended))

        .on("mouseover", function(event, d) {
            d3.select(this).attr("r", 12);
            showTooltip(event, d);
        })
        .on("mouseout", function(event, d) {
            d3.select(this).attr("r", 8);
            hideTooltip();
        })
        .on("mousemove", function(event, d) {
            updateTooltipPosition(event);
        });

    // 添加节点标签
    const label = svg.append("g")
        .selectAll("text")
        .data(nodes)
        .enter().append("text")
        .text(d => d.name)
        .attr("font-size", "12px")
        .attr("dx", 12)
        .attr("dy", 4)
        .attr("fill", "#333");

    // 更新力导向图
    simulation
        .nodes(nodes)
        .on("tick", ticked);

    simulation.force("link")
        .links(validLinks);

    simulation.alpha(1).restart();

    function ticked() {
        link
            .attr("x1", d => d.source.x)
            .attr("y1", d => d.source.y)
            .attr("x2", d => d.target.x)
            .attr("y2", d => d.target.y);

        node
            .attr("cx", d => d.x)
            .attr("cy", d => d.y);

        label
            .attr("x", d => d.x)
            .attr("y", d => d.y);
    }
}



// 显示节点悬浮提示框
function showTooltip(event, node) {
    const tooltip = $('#nodeTooltip');

    // 计算该节点的连接信息
    const connectedLinks = links.filter(link =>
        link.source === node.id || link.target === node.id ||
        (typeof link.source === 'object' && link.source.id === node.id) ||
        (typeof link.target === 'object' && link.target.id === node.id)
    );

    // 获取相关节点
    const relatedNodes = new Set();
    const relationshipTypes = new Set();

    connectedLinks.forEach(link => {
        let sourceId, targetId;

        if (typeof link.source === 'object') {
            sourceId = link.source.id;
        } else {
            sourceId = link.source;
        }

        if (typeof link.target === 'object') {
            targetId = link.target.id;
        } else {
            targetId = link.target;
        }

        if (sourceId === node.id) {
            relatedNodes.add(targetId);
        } else if (targetId === node.id) {
            relatedNodes.add(sourceId);
        }

        if (link.relationship) {
            relationshipTypes.add(link.relationship);
        }
    });

    // 设置提示框内容
    tooltip.find('.tooltip-title').text(node.name);
    tooltip.find('.tooltip-type').text(`类型: ${node.type}`);
    tooltip.find('.tooltip-connections').text(`连接数: ${connectedLinks.length} | 关系类型: ${relationshipTypes.size}`);

    // 显示相关节点（最多显示5个）
    const relatedNodesArray = Array.from(relatedNodes).slice(0, 5);
    let relatedContent = '';
    if (relatedNodesArray.length > 0) {
        relatedContent = relatedNodesArray.map(nodeId => {
            const relatedNode = nodes.find(n => n.id === nodeId);
            return `<div class="tooltip-related-item">• ${relatedNode ? relatedNode.name : nodeId}</div>`;
        }).join('');

        if (relatedNodes.size > 5) {
            relatedContent += `<div class="tooltip-related-item">... 还有 ${relatedNodes.size - 5} 个节点</div>`;
        }
    } else {
        relatedContent = '<div class="tooltip-related-item">无相关节点</div>';
    }

    tooltip.find('.tooltip-related-content').html(relatedContent);

    // 显示提示框
    tooltip.addClass('show');

    // 设置初始位置
    updateTooltipPosition(event);
}

// 隐藏节点悬浮提示框
function hideTooltip() {
    $('#nodeTooltip').removeClass('show');
}

// 更新提示框位置
function updateTooltipPosition(event) {
    const tooltip = $('#nodeTooltip');
    const container = $('#graphContainer');
    const containerOffset = container.offset();

    // 计算鼠标相对于容器的位置
    const mouseX = event.pageX - containerOffset.left;
    const mouseY = event.pageY - containerOffset.top;

    // 获取提示框尺寸
    const tooltipWidth = tooltip.outerWidth();
    const tooltipHeight = tooltip.outerHeight();
    const containerWidth = container.width();
    const containerHeight = container.height();

    // 计算提示框位置，避免超出容器边界
    let left = mouseX + 15;
    let top = mouseY - tooltipHeight / 2;

    // 如果右边超出边界，显示在鼠标左侧
    if (left + tooltipWidth > containerWidth) {
        left = mouseX - tooltipWidth - 15;
    }

    // 如果上边超出边界，调整到容器内
    if (top < 0) {
        top = 10;
    }

    // 如果下边超出边界，调整到容器内
    if (top + tooltipHeight > containerHeight) {
        top = containerHeight - tooltipHeight - 10;
    }

    // 设置位置
    tooltip.css({
        left: left + 'px',
        top: top + 'px'
    });
}

function expandNode(nodeId) {
    // 这里可以实现展开节点的功能
    alert('展开节点功能开发中...');
}

function clearGraph() {
    nodes = [];
    links = [];
    originalNodes = [];
    originalLinks = [];
    svg.selectAll("*").remove();

    // 重置统计信息
    $('#nodeCount').text('0');
    $('#relationshipCount').text('0');
    $('#nodeTypeCount').text('0');
    $('#relationshipTypeCount').text('0');



    // 重置过滤器
    $('.node-type-filter').prop('checked', false);
    $('#selectAll').prop('checked', false).prop('indeterminate', false);
}

function resetView() {
    const width = $('#graphContainer').width();
    const height = $('#graphContainer').height();

    svg.transition().duration(750).call(
        d3.zoom().transform,
        d3.zoomIdentity.translate(0, 0).scale(1)
    );
}

function showLoading() {
    $('#loadingOverlay').show();
}

function hideLoading() {
    $('#loadingOverlay').hide();
}

// 拖拽功能
function dragstarted(event, d) {
    if (!event.active) simulation.alphaTarget(0.3).restart();
    d.fx = d.x;
    d.fy = d.y;
}

function dragged(event, d) {
    d.fx = event.x;
    d.fy = event.y;
}

function dragended(event, d) {
    if (!event.active) simulation.alphaTarget(0);
    d.fx = null;
    d.fy = null;
}

// 搜索框回车事件
$('#searchInput').on('keypress', function(e) {
    if (e.which === 13) {
        searchGraph();
    }
});

// 过滤功能
function applyFilter() {
    // 获取所有选中的类型
    const selectedTypes = [];
    $('.node-type-filter:checked').each(function() {
        selectedTypes.push($(this).val());
    });

    if (selectedTypes.length === 0) {
        // 如果没有选中任何类型，显示所有节点
        nodes = [...originalNodes];
        links = [...originalLinks];
    } else {
        // 过滤选中类型的节点
        nodes = originalNodes.filter(node => selectedTypes.includes(node.type));

        // 获取过滤后节点的ID集合
        const nodeIds = new Set(nodes.map(node => node.id));

        // 过滤关系，只保留两端都在过滤后节点中的关系
        links = originalLinks.filter(link =>
            nodeIds.has(link.source) && nodeIds.has(link.target)
        );
    }

    // 更新统计信息
    updateStatistics();

    // 重新渲染图谱
    updateGraph();



    // 更新全选按钮状态
    updateSelectAllState();
}

// 全选/全不选功能
function toggleSelectAll() {
    const selectAllChecked = $('#selectAll').is(':checked');
    $('.node-type-filter').prop('checked', selectAllChecked);
    applyFilter();
}

// 更新全选按钮状态
function updateSelectAllState() {
    const totalFilters = $('.node-type-filter').length;
    const checkedFilters = $('.node-type-filter:checked').length;

    if (checkedFilters === 0) {
        $('#selectAll').prop('checked', false).prop('indeterminate', false);
    } else if (checkedFilters === totalFilters) {
        $('#selectAll').prop('checked', true).prop('indeterminate', false);
    } else {
        $('#selectAll').prop('checked', false).prop('indeterminate', true);
    }
}

// 更新统计信息
function updateStatistics() {
    // 更新节点和关系数量
    $('#nodeCount').text(nodes.length);
    $('#relationshipCount').text(links.length);

    // 计算实体类型数量
    const nodeTypes = new Set();
    nodes.forEach(node => {
        if (node.type) {
            nodeTypes.add(node.type);
        }
    });
    $('#nodeTypeCount').text(nodeTypes.size);

    // 计算关系类型数量
    const relationshipTypes = new Set();
    links.forEach(link => {
        if (link.relationship) {
            relationshipTypes.add(link.relationship);
        } else if (link.type) {
            relationshipTypes.add(link.type);
        }
    });
    $('#relationshipTypeCount').text(relationshipTypes.size);


}
</script>
{% endblock %}
