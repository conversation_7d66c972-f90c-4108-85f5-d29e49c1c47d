#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
次梯度法简单验证
"""

import numpy as np

def objective_function(x):
    """目标函数 f(x) = 2x1 + 3x2 + x3"""
    return 2*x[0] + 3*x[1] + x[2]

def constraint_violations(x):
    """计算约束违反量"""
    violations = np.zeros(5)
    
    # 约束1: -x1 + 4x2 + 2x3 >= 8
    violations[0] = max(0, 8 - (-x[0] + 4*x[1] + 2*x[2]))
    
    # 约束2: 3x1 + 2x2 >= 6
    violations[1] = max(0, 6 - (3*x[0] + 2*x[1]))
    
    # 约束3-5: xi >= 0
    violations[2] = max(0, -x[0])
    violations[3] = max(0, -x[1])
    violations[4] = max(0, -x[2])
    
    return violations

def is_feasible(x, tolerance=1e-6):
    """检查点是否可行"""
    violations = constraint_violations(x)
    return np.all(violations < tolerance)

def test_basic_functionality():
    """测试基本功能"""
    print("次梯度法基本功能测试")
    print("=" * 50)
    
    # 测试点
    test_points = [
        np.array([0.0, 2.0, 0.0]),  # 理论最优解
        np.array([1.0, 1.0, 1.0]),  # 不可行点
        np.array([0.0, 3.0, 1.0]),  # 可行点
    ]
    
    for i, x in enumerate(test_points):
        print(f"\n测试点 {i+1}: x = {x}")
        
        # 计算目标函数值
        f_val = objective_function(x)
        print(f"目标函数值: f(x) = {f_val:.6f}")
        
        # 检查约束
        violations = constraint_violations(x)
        print(f"约束违反量: {violations}")
        
        # 检查可行性
        feasible = is_feasible(x)
        print(f"是否可行: {feasible}")
        
        if feasible:
            print("约束验证:")
            print(f"  约束1: -x1 + 4x2 + 2x3 = {-x[0] + 4*x[1] + 2*x[2]:.6f} >= 8")
            print(f"  约束2: 3x1 + 2x2 = {3*x[0] + 2*x[1]:.6f} >= 6")
            print(f"  约束3-5: x1={x[0]:.6f}, x2={x[1]:.6f}, x3={x[2]:.6f} >= 0")

def solve_with_scipy():
    """使用scipy求解"""
    try:
        from scipy.optimize import linprog
        
        print("\n" + "=" * 50)
        print("单纯形法求解（理论最优解）")
        print("=" * 50)
        
        # 标准形式
        c = np.array([2, 3, 1])
        A_ub = np.array([
            [1, -4, -2],   # x1 - 4x2 - 2x3 <= -8
            [-3, -2, 0]    # -3x1 - 2x2 <= -6
        ])
        b_ub = np.array([-8, -6])
        bounds = [(0, None), (0, None), (0, None)]
        
        result = linprog(c, A_ub=A_ub, b_ub=b_ub, bounds=bounds, method='highs')
        
        if result.success:
            print(f"理论最优解: x* = [{result.x[0]:.6f}, {result.x[1]:.6f}, {result.x[2]:.6f}]")
            print(f"理论最优值: f* = {result.fun:.6f}")
            
            # 验证解
            x_opt = result.x
            print(f"\n约束验证:")
            print(f"约束1: -x1 + 4x2 + 2x3 = {-x_opt[0] + 4*x_opt[1] + 2*x_opt[2]:.6f} >= 8")
            print(f"约束2: 3x1 + 2x2 = {3*x_opt[0] + 2*x_opt[1]:.6f} >= 6")
            print(f"约束3-5: x1={x_opt[0]:.6f}, x2={x_opt[1]:.6f}, x3={x_opt[2]:.6f} >= 0")
            
            return result.x, result.fun
        else:
            print("求解失败:", result.message)
            return None, None
            
    except ImportError:
        print("scipy未安装，无法获取理论最优解")
        return None, None

def simple_subgradient_iteration():
    """简单的次梯度迭代演示"""
    print("\n" + "=" * 50)
    print("次梯度法迭代演示")
    print("=" * 50)
    
    # 初始点
    x = np.array([1.0, 1.0, 1.0])
    print(f"初始点: x = {x}")
    print(f"初始目标函数值: f(x) = {objective_function(x):.6f}")
    print(f"初始可行性: {is_feasible(x)}")
    
    # 简单的启发式更新
    for k in range(5):
        violations = constraint_violations(x)
        max_violation = np.max(violations)
        
        print(f"\n迭代 {k+1}:")
        print(f"  当前点: x = [{x[0]:.4f}, {x[1]:.4f}, {x[2]:.4f}]")
        print(f"  目标函数值: {objective_function(x):.6f}")
        print(f"  最大违反量: {max_violation:.6f}")
        print(f"  是否可行: {is_feasible(x)}")
        
        if is_feasible(x):
            print("  找到可行解!")
            break
        
        # 简单的启发式调整
        if violations[0] > 0:  # 约束1违反
            x[1] += 0.5  # 增加x2
            x[2] += 0.2  # 增加x3
        if violations[1] > 0:  # 约束2违反
            x[0] += 0.3  # 增加x1
            x[1] += 0.2  # 增加x2
        
        # 确保非负
        x = np.maximum(x, 0)

def analyze_problem_structure():
    """分析问题结构"""
    print("\n" + "=" * 50)
    print("问题结构分析")
    print("=" * 50)
    
    print("线性规划问题:")
    print("  目标函数: min 2x1 + 3x2 + x3")
    print("  约束条件:")
    print("    -x1 + 4x2 + 2x3 >= 8")
    print("    3x1 + 2x2 >= 6")
    print("    xi >= 0, i = 1,2,3")
    
    print("\n问题特点:")
    print("  - 变量数: 3")
    print("  - 约束数: 5 (2个不等式 + 3个非负)")
    print("  - 问题类型: 线性规划")
    print("  - 可行域: 有界凸多面体")
    
    print("\n次梯度法适用性:")
    print("  ✓ 目标函数凸")
    print("  ✓ 约束函数凸")
    print("  ✓ 可行域非空")
    print("  ✓ 理论上保证收敛")

def main():
    """主函数"""
    # 问题结构分析
    analyze_problem_structure()
    
    # 基本功能测试
    test_basic_functionality()
    
    # 理论最优解
    x_opt, f_opt = solve_with_scipy()
    
    # 简单迭代演示
    simple_subgradient_iteration()
    
    print("\n" + "=" * 50)
    print("总结")
    print("=" * 50)
    print("1. 次梯度法是求解凸优化问题的通用方法")
    print("2. 对于线性规划，单纯形法更高效")
    print("3. 次梯度法的价值在于其通用性和理论完备性")
    print("4. 实际应用中需要仔细选择步长和收敛判据")

if __name__ == "__main__":
    main()
