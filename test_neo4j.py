#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Neo4j数据库连接
"""

import py2neo

def test_neo4j_connection():
    """测试Neo4j连接"""
    try:
        client = py2neo.Graph(
            'bolt://localhost:7687',
            user='neo4j',
            password='wangqi20040401',
            name='doctor'
        )
        
        # 测试连接
        result = client.run('MATCH (n) RETURN count(n) as total_nodes LIMIT 1')
        data = result.data()
        print(f'数据库连接成功，总节点数: {data[0]["total_nodes"]}')
        
        # 检查节点类型
        result = client.run('MATCH (n) RETURN DISTINCT labels(n) as node_types LIMIT 10')
        types = result.data()
        print('\n节点类型:')
        for t in types:
            if t["node_types"]:
                print(f'  - {t["node_types"]}')
        
        # 检查一些示例节点
        result = client.run('MATCH (n) WHERE n.名称 IS NOT NULL RETURN n.名称 as name, labels(n) as type LIMIT 5')
        nodes = result.data()
        print('\n示例节点:')
        for node in nodes:
            print(f'  - {node["name"]} ({node["type"]})')
            
        # 检查关系
        result = client.run('MATCH ()-[r]->() RETURN count(r) as total_relationships LIMIT 1')
        data = result.data()
        print(f'\n总关系数: {data[0]["total_relationships"]}')
        
        # 检查一些示例关系
        result = client.run('''
        MATCH (a)-[r]->(b) 
        WHERE a.名称 IS NOT NULL AND b.名称 IS NOT NULL
        RETURN a.名称 as source, b.名称 as target, type(r) as relationship 
        LIMIT 5
        ''')
        relationships = result.data()
        print('\n示例关系:')
        for rel in relationships:
            print(f'  - {rel["source"]} --[{rel["relationship"]}]--> {rel["target"]}')
        
        return True
        
    except Exception as e:
        print(f'数据库连接失败: {e}')
        return False

if __name__ == '__main__':
    test_neo4j_connection()
