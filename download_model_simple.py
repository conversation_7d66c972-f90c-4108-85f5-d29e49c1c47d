#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的BERT模型下载脚本
使用多种方法下载chinese-roberta-wwm-ext模型
"""

import os
import requests
import json
from urllib.parse import urljoin

def download_file(url, local_path, filename):
    """下载单个文件"""
    try:
        print(f"下载 {filename}...")
        response = requests.get(url, stream=True, timeout=30)
        response.raise_for_status()
        
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        
        file_size = os.path.getsize(local_path) / (1024 * 1024)
        print(f"✓ {filename} 下载完成 ({file_size:.1f}MB)")
        return True
        
    except Exception as e:
        print(f"✗ {filename} 下载失败: {e}")
        return False

def download_from_mirror():
    """从镜像站点下载"""
    base_url = "https://hf-mirror.com/hfl/chinese-roberta-wwm-ext/resolve/main/"
    local_dir = "model/chinese-roberta-wwm-ext"
    
    # 需要下载的文件列表
    files = [
        "config.json",
        "pytorch_model.bin", 
        "tokenizer.json",
        "tokenizer_config.json",
        "vocab.txt"
    ]
    
    print("从HuggingFace镜像站点下载模型...")
    print(f"目标目录: {local_dir}")
    
    success_count = 0
    for filename in files:
        url = urljoin(base_url, filename)
        local_path = os.path.join(local_dir, filename)
        
        if download_file(url, local_path, filename):
            success_count += 1
    
    if success_count == len(files):
        print(f"\n🎉 所有文件下载完成！({success_count}/{len(files)})")
        return True
    else:
        print(f"\n⚠️ 部分文件下载失败 ({success_count}/{len(files)})")
        return False

def download_from_gitee():
    """从Gitee镜像下载"""
    # Gitee上的镜像仓库（如果有的话）
    print("尝试从Gitee镜像下载...")
    # 这里可以添加Gitee镜像的下载逻辑
    return False

def verify_download():
    """验证下载的文件"""
    local_dir = "model/chinese-roberta-wwm-ext"
    required_files = [
        "config.json",
        "pytorch_model.bin",
        "tokenizer.json", 
        "tokenizer_config.json",
        "vocab.txt"
    ]
    
    print("\n验证下载的文件:")
    all_exist = True
    
    for filename in required_files:
        filepath = os.path.join(local_dir, filename)
        if os.path.exists(filepath):
            size = os.path.getsize(filepath) / (1024 * 1024)
            print(f"  ✓ {filename} ({size:.1f}MB)")
        else:
            print(f"  ✗ {filename} (缺失)")
            all_exist = False
    
    return all_exist

def main():
    print("BERT模型下载工具 (简化版)")
    print("=" * 50)
    
    # 检查是否已存在
    if os.path.exists('model/chinese-roberta-wwm-ext/config.json'):
        print("✓ BERT模型已存在")
        if verify_download():
            print("✓ 所有文件完整，无需重新下载")
            return
        else:
            print("⚠️ 文件不完整，重新下载...")
    
    # 尝试不同的下载方法
    methods = [
        ("HuggingFace镜像", download_from_mirror),
        ("Gitee镜像", download_from_gitee)
    ]
    
    for method_name, method_func in methods:
        print(f"\n尝试使用 {method_name}...")
        try:
            if method_func():
                if verify_download():
                    print(f"\n🎉 使用 {method_name} 下载成功！")
                    print("\n现在可以重新启动系统使用本地BERT模型了！")
                    return
        except Exception as e:
            print(f"✗ {method_name} 失败: {e}")
            continue
    
    # 所有方法都失败了
    print("\n❌ 所有自动下载方法都失败了")
    print_manual_guide()

def print_manual_guide():
    """打印手动下载指导"""
    print("\n" + "="*60)
    print("🔧 手动下载指导")
    print("="*60)
    print("\n请手动下载以下文件到 model/chinese-roberta-wwm-ext/ 目录:")
    print("\n1. 访问: https://hf-mirror.com/hfl/chinese-roberta-wwm-ext/tree/main")
    print("2. 下载以下文件:")
    
    files = [
        ("config.json", "模型配置文件"),
        ("pytorch_model.bin", "模型权重文件 (最大)"),
        ("tokenizer.json", "分词器文件"),
        ("tokenizer_config.json", "分词器配置"),
        ("vocab.txt", "词汇表文件")
    ]
    
    for filename, desc in files:
        print(f"   - {filename} ({desc})")
    
    print(f"\n3. 确保文件保存在: {os.path.abspath('model/chinese-roberta-wwm-ext/')}")
    print("4. 完成后运行: python download_model_simple.py 验证")
    print("\n或者使用Git命令:")
    print("   cd model")
    print("   git clone https://hf-mirror.com/hfl/chinese-roberta-wwm-ext")
    print("="*60)

if __name__ == "__main__":
    main()
