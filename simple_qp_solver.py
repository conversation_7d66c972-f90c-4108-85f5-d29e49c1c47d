#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
二次规划问题求解器 - 简化版
解决约束优化问题：
min f(x) = x1² - x1*x2 + 2*x2² - x1 - 10*x2
s.t. 3*x1 + 2*x2 ≤ 6
     x1 ≥ 0, x2 ≥ 0
"""

import numpy as np
from scipy.optimize import minimize

def objective_function(x):
    """目标函数: f(x) = x1² - x1*x2 + 2*x2² - x1 - 10*x2"""
    x1, x2 = x.flatten()
    return x1**2 - x1*x2 + 2*x2**2 - x1 - 10*x2

def objective_gradient(x):
    """目标函数的梯度"""
    x1, x2 = x.flatten()
    grad_x1 = 2*x1 - x2 - 1
    grad_x2 = -x1 + 4*x2 - 10
    return np.array([grad_x1, grad_x2])

def solve_quadratic_programming():
    """求解二次规划问题"""
    print("=" * 60)
    print("二次规划问题求解")
    print("=" * 60)
    
    # 问题描述
    print("问题描述:")
    print("min f(x) = x1² - x1*x2 + 2*x2² - x1 - 10*x2")
    print("s.t. 3*x1 + 2*x2 ≤ 6")
    print("     x1 ≥ 0, x2 ≥ 0")
    print()
    
    # 步骤1: 检查目标函数的凸性
    print("步骤1: 检查目标函数的凸性")
    H = np.array([[2, -1], [-1, 4]])
    print(f"Hessian矩阵:")
    print(H)
    eigenvalues = np.linalg.eigvals(H)
    print(f"特征值: {eigenvalues}")
    print("✓ Hessian矩阵正定，目标函数为严格凸函数")
    print()
    
    # 步骤2: 设置约束条件
    print("步骤2: 设置约束条件")
    ineq_constraint = {
        'type': 'ineq',
        'fun': lambda x: 6 - 3*x[0] - 2*x[1],
        'jac': lambda x: np.array([-3, -2])
    }
    bounds = [(0, None), (0, None)]
    print("不等式约束: 3*x1 + 2*x2 ≤ 6")
    print("边界约束: x1 ≥ 0, x2 ≥ 0")
    print()
    
    # 步骤3: 选择初始点
    x0 = np.array([0.5, 1.0])
    print(f"步骤3: 选择初始点 x0 = {x0}")
    print(f"初始点目标函数值: f(x0) = {objective_function(x0):.6f}")
    print()
    
    # 步骤4: 求解优化问题
    print("步骤4: 求解优化问题")
    result = minimize(
        fun=objective_function,
        x0=x0,
        method='SLSQP',
        jac=objective_gradient,
        bounds=bounds,
        constraints=[ineq_constraint],
        options={'disp': True, 'ftol': 1e-12}
    )
    
    print("\n求解结果:")
    print(f"优化成功: {result.success}")
    print(f"最优解: x* = [{result.x[0]:.6f}, {result.x[1]:.6f}]")
    print(f"最优值: f(x*) = {result.fun:.6f}")
    print(f"迭代次数: {result.nit}")
    print()
    
    # 步骤5: 验证约束条件
    print("步骤5: 验证约束条件")
    x_opt = result.x
    constraint_val = 3*x_opt[0] + 2*x_opt[1]
    print(f"约束检查: 3*x1 + 2*x2 = {constraint_val:.6f} ≤ 6")
    print("✓ 不等式约束满足" if constraint_val <= 6 + 1e-6 else "✗ 不等式约束违反")
    print(f"边界约束检查: x1 = {x_opt[0]:.6f} ≥ 0, x2 = {x_opt[1]:.6f} ≥ 0")
    print("✓ 边界约束满足" if x_opt[0] >= -1e-6 and x_opt[1] >= -1e-6 else "✗ 边界约束违反")
    print()
    
    # 步骤6: KKT条件检查
    print("步骤6: KKT条件检查")
    grad = objective_gradient(x_opt)
    print(f"梯度: ∇f(x*) = [{grad[0]:.6f}, {grad[1]:.6f}]")
    
    if abs(constraint_val - 6) < 1e-6:
        print("最优解在约束边界上")
        A = np.array([[3, 2]])
        lambda_val = np.linalg.lstsq(A.T, -grad, rcond=None)[0]
        print(f"拉格朗日乘数: λ = {lambda_val[0]:.6f}")
        print("✓ KKT条件满足" if lambda_val[0] >= -1e-6 else "✗ KKT条件违反")
    else:
        print("最优解在可行域内部")
        print("✓ 梯度为零，满足最优性条件" if np.linalg.norm(grad) < 1e-6 else "✗ 梯度非零")
    print()
    
    return result

def analytical_solution():
    """解析解法验证"""
    print("步骤7: 解析解法验证")
    print("对于二次规划问题，我们可以用拉格朗日乘数法求解析解")
    
    print("\n构建拉格朗日函数:")
    print("L(x1, x2, λ, μ1, μ2) = x1² - x1*x2 + 2*x2² - x1 - 10*x2 + λ(3*x1 + 2*x2 - 6) - μ1*x1 - μ2*x2")
    
    print("\nKKT条件:")
    print("1. ∇L = 0:")
    print("   ∂L/∂x1 = 2*x1 - x2 - 1 + 3*λ - μ1 = 0")
    print("   ∂L/∂x2 = -x1 + 4*x2 - 10 + 2*λ - μ2 = 0")
    
    print("2. 原始可行性: 3*x1 + 2*x2 ≤ 6, x1 ≥ 0, x2 ≥ 0")
    print("3. 对偶可行性: λ ≥ 0, μ1 ≥ 0, μ2 ≥ 0")
    print("4. 互补松弛条件: λ(3*x1 + 2*x2 - 6) = 0, μ1*x1 = 0, μ2*x2 = 0")
    
    print("\n分析最优解:")
    print("由于数值解显示 x1 = 0.5 > 0, x2 = 2.25 > 0")
    print("根据互补松弛条件: μ1 = 0, μ2 = 0")
    print("约束 3*x1 + 2*x2 = 6 是紧约束，所以 λ > 0")
    
    print("\n简化的KKT系统:")
    print("2*x1 - x2 - 1 + 3*λ = 0  ... (1)")
    print("-x1 + 4*x2 - 10 + 2*λ = 0  ... (2)")
    print("3*x1 + 2*x2 = 6  ... (3)")
    
    print("\n求解线性方程组:")
    print("从方程(1)和(2)消除λ得到: x1 = 2*x2 - 4")
    print("代入约束(3): 3*(2*x2 - 4) + 2*x2 = 6")
    print("化简: 6*x2 - 12 + 2*x2 = 6")
    print("解得: 8*x2 = 18, x2 = 2.25")
    print("因此: x1 = 2*2.25 - 4 = 0.5")
    print("拉格朗日乘数: λ = (2.25 + 1 - 2*0.5)/3 = 0.75")
    
    print(f"\n解析解: x1* = 0.5, x2* = 2.25, λ* = 0.75")
    f_val = 0.5**2 - 0.5*2.25 + 2*2.25**2 - 0.5 - 10*2.25
    print(f"目标函数值: f(x*) = {f_val}")

if __name__ == "__main__":
    # 求解问题
    result = solve_quadratic_programming()
    
    # 解析解验证
    analytical_solution()
    
    print("\n" + "=" * 60)
    print("求解完成")
    print("=" * 60)
    print("\n总结:")
    print(f"最优解: x1* = {result.x[0]:.6f}, x2* = {result.x[1]:.6f}")
    print(f"最优值: f(x*) = {result.fun:.6f}")
    print("该解满足所有约束条件和KKT条件，是全局最优解。")
