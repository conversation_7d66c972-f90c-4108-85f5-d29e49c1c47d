#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员面板模块 - admin_app.py
处理系统管理、监控、配置等功能
"""

from flask import Blueprint, render_template, request, jsonify, session
import os
import sys
import json
from datetime import datetime, timedelta
import py2neo

# 尝试导入psutil，如果失败则使用备用方案
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("警告: psutil未安装，系统监控功能将受限")

# 创建蓝图
admin_bp = Blueprint('admin', __name__)

def get_neo4j_client():
    """获取Neo4j客户端"""
    return py2neo.Graph(
        'bolt://localhost:7687',
        user='neo4j',
        password='wangqi20040401',
        name='doctor'
    )

def get_system_info():
    """获取系统信息"""
    if not PSUTIL_AVAILABLE:
        return {
            'cpu': {'percent': 0, 'count': 'N/A'},
            'memory': {'percent': 0, 'total_gb': 'N/A', 'used_gb': 'N/A'},
            'disk': {'percent': 0, 'total_gb': 'N/A', 'used_gb': 'N/A'},
            'note': 'psutil未安装，无法获取详细系统信息'
        }

    try:
        # CPU信息
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()

        # 内存信息
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_total = memory.total / (1024**3)  # GB
        memory_used = memory.used / (1024**3)   # GB

        # 磁盘信息
        disk = psutil.disk_usage('/')
        disk_percent = (disk.used / disk.total) * 100
        disk_total = disk.total / (1024**3)  # GB
        disk_used = disk.used / (1024**3)    # GB

        return {
            'cpu': {
                'percent': cpu_percent,
                'count': cpu_count
            },
            'memory': {
                'percent': memory_percent,
                'total_gb': round(memory_total, 2),
                'used_gb': round(memory_used, 2)
            },
            'disk': {
                'percent': round(disk_percent, 2),
                'total_gb': round(disk_total, 2),
                'used_gb': round(disk_used, 2)
            }
        }
    except Exception as e:
        print(f"获取系统信息失败: {e}")
        return {
            'cpu': {'percent': 0, 'count': 0},
            'memory': {'percent': 0, 'total_gb': 0, 'used_gb': 0},
            'disk': {'percent': 0, 'total_gb': 0, 'used_gb': 0}
        }

def get_service_status():
    """获取服务状态"""
    services = {}

    # 检查Neo4j连接
    try:
        client = get_neo4j_client()
        client.run("RETURN 1")
        services['neo4j'] = {'status': 'running', 'message': '连接正常'}
    except Exception as e:
        services['neo4j'] = {'status': 'error', 'message': f'连接失败: {str(e)}'}

    # 检查BERT模型
    model_path = 'model/best_roberta_rnn_model_ent_aug.pt'
    if os.path.exists(model_path):
        model_size = os.path.getsize(model_path) / (1024 * 1024)  # MB
        services['bert_model'] = {'status': 'running', 'message': f'模型文件存在 ({model_size:.1f}MB)'}
    else:
        services['bert_model'] = {'status': 'error', 'message': '模型文件不存在'}

    return services

def get_database_statistics():
    """获取数据库统计信息"""
    try:
        client = get_neo4j_client()

        # 节点统计
        node_stats = {}
        node_query = """
        MATCH (n)
        RETURN labels(n)[0] as type, count(n) as count
        ORDER BY count DESC
        """
        node_result = client.run(node_query).data()
        for record in node_result:
            node_stats[record['type']] = record['count']

        # 关系统计
        rel_stats = {}
        rel_query = """
        MATCH ()-[r]->()
        RETURN type(r) as type, count(r) as count
        ORDER BY count DESC
        """
        rel_result = client.run(rel_query).data()
        for record in rel_result:
            rel_stats[record['type']] = record['count']

        # 总计
        total_nodes = sum(node_stats.values())
        total_relationships = sum(rel_stats.values())

        return {
            'total_nodes': total_nodes,
            'total_relationships': total_relationships,
            'node_types': node_stats,
            'relationship_types': rel_stats
        }
    except Exception as e:
        print(f"获取数据库统计失败: {e}")
        return {
            'total_nodes': 0,
            'total_relationships': 0,
            'node_types': {},
            'relationship_types': {}
        }

def get_activity_logs(limit=50):
    """获取活动日志"""
    log_file = 'tmp_data/activity.log'
    logs = []

    try:
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for line in lines[-limit:]:
                    try:
                        log_data = json.loads(line.strip())
                        logs.append(log_data)
                    except:
                        continue
    except Exception as e:
        print(f"读取日志失败: {e}")

    # 如果没有日志文件，创建一些示例日志
    if not logs:
        logs = [
            {
                'timestamp': datetime.now().isoformat(),
                'type': 'info',
                'message': '系统启动',
                'user': 'system'
            },
            {
                'timestamp': (datetime.now() - timedelta(minutes=5)).isoformat(),
                'type': 'success',
                'message': 'BERT模型加载成功',
                'user': 'system'
            },
            {
                'timestamp': (datetime.now() - timedelta(minutes=10)).isoformat(),
                'type': 'info',
                'message': 'Neo4j数据库连接成功',
                'user': 'system'
            }
        ]

    return logs

def log_activity(activity_type, message, user=None):
    """记录活动日志"""
    log_file = 'tmp_data/activity.log'

    try:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)

        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'type': activity_type,
            'message': message,
            'user': user or session.get('user_id', 'anonymous')
        }

        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
    except Exception as e:
        print(f"记录日志失败: {e}")

def backup_database():
    """备份数据库"""
    try:
        backup_dir = 'backups'
        os.makedirs(backup_dir, exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = f"{backup_dir}/neo4j_backup_{timestamp}.cypher"

        client = get_neo4j_client()

        # 简化的数据导出（不依赖APOC插件）
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(f"// Neo4j数据库备份 - {timestamp}\n")
            f.write("// 这是一个简化的备份文件\n")

            try:
                # 导出节点统计
                node_query = "MATCH (n) RETURN labels(n)[0] as type, count(n) as count"
                node_result = client.run(node_query).data()
                f.write(f"// 节点统计:\n")
                for record in node_result:
                    f.write(f"// {record['type']}: {record['count']} 个节点\n")

                # 导出关系统计
                rel_query = "MATCH ()-[r]->() RETURN type(r) as type, count(r) as count"
                rel_result = client.run(rel_query).data()
                f.write(f"// 关系统计:\n")
                for record in rel_result:
                    f.write(f"// {record['type']}: {record['count']} 个关系\n")

            except Exception as e:
                f.write(f"// 备份过程中出现错误: {str(e)}\n")

        log_activity('success', f'数据库备份完成: {backup_file}')
        return True, f'备份完成: {backup_file}'

    except Exception as e:
        error_msg = f'备份失败: {str(e)}'
        log_activity('error', error_msg)
        return False, error_msg

def get_model_info():
    """获取模型信息"""
    models = {}

    # BERT模型信息
    bert_path = 'model/best_roberta_rnn_model_ent_aug.pt'
    if os.path.exists(bert_path):
        size = os.path.getsize(bert_path) / (1024 * 1024)  # MB
        mtime = datetime.fromtimestamp(os.path.getmtime(bert_path))
        models['bert'] = {
            'name': 'BERT NER模型',
            'path': bert_path,
            'size_mb': round(size, 2),
            'modified': mtime.isoformat(),
            'status': 'available'
        }
    else:
        models['bert'] = {
            'name': 'BERT NER模型',
            'path': bert_path,
            'status': 'missing'
        }

    # 系统信息
    models['system'] = {
        'name': '系统信息',
        'python_version': sys.version,
        'status': 'available'
    }

    return models

# 路由定义
@admin_bp.route('/admin')
def admin_page():
    """管理员页面"""
    if 'user_id' not in session or not session.get('is_admin'):
        return render_template('login.html')
    return render_template('admin.html')

@admin_bp.route('/api/admin/dashboard', methods=['GET'])
def api_get_dashboard():
    """获取管理员仪表板数据"""
    if 'user_id' not in session or not session.get('is_admin'):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    try:
        dashboard_data = {
            'system_info': get_system_info(),
            'service_status': get_service_status(),
            'database_stats': get_database_statistics(),
            'activity_logs': get_activity_logs(20),
            'model_info': get_model_info()
        }

        return jsonify({'success': True, 'dashboard': dashboard_data})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@admin_bp.route('/api/admin/backup', methods=['POST'])
def api_backup_database():
    """备份数据库API"""
    if 'user_id' not in session or not session.get('is_admin'):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    try:
        success, message = backup_database()
        return jsonify({'success': success, 'message': message})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@admin_bp.route('/api/admin/logs', methods=['GET'])
def api_get_logs():
    """获取系统日志API"""
    if 'user_id' not in session or not session.get('is_admin'):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    try:
        limit = int(request.args.get('limit', 100))
        logs = get_activity_logs(limit)
        return jsonify({'success': True, 'logs': logs})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@admin_bp.route('/api/admin/system/restart', methods=['POST'])
def api_restart_system():
    """重启系统API"""
    if 'user_id' not in session or not session.get('is_admin'):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    try:
        log_activity('warning', f'用户 {session["user_id"]} 请求重启系统')
        # 注意：实际重启需要谨慎处理
        return jsonify({'success': True, 'message': '重启请求已记录，请手动重启服务'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@admin_bp.route('/api/admin/clear-cache', methods=['POST'])
def api_clear_cache():
    """清理缓存API"""
    if 'user_id' not in session or not session.get('is_admin'):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    try:
        # 清理临时文件
        import shutil
        temp_dirs = ['tmp_data/__pycache__', '__pycache__']

        for temp_dir in temp_dirs:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)

        log_activity('info', f'用户 {session["user_id"]} 清理了系统缓存')
        return jsonify({'success': True, 'message': '缓存清理完成'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@admin_bp.route('/api/admin/config', methods=['GET', 'POST'])
def api_system_config():
    """系统配置API"""
    if 'user_id' not in session or not session.get('is_admin'):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    config_file = 'tmp_data/system_config.json'

    if request.method == 'GET':
        # 获取配置
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                config = {
                    'default_model': 'qwen:32b',
                    'max_concurrent_users': 100,
                    'session_timeout': 30,
                    'enable_debug': False
                }

            return jsonify({'success': True, 'config': config})
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)})

    else:
        # 保存配置
        try:
            config = request.get_json()

            os.makedirs(os.path.dirname(config_file), exist_ok=True)
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            log_activity('info', f'用户 {session["user_id"]} 更新了系统配置')
            return jsonify({'success': True, 'message': '配置保存成功'})
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)})
