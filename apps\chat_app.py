#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能问答模块 - chat_app.py
处理聊天相关的所有功能
"""

from flask import Blueprint, render_template, request, jsonify, session
import os
import sys
import torch
import pickle
from transformers import BertTokenizer
import py2neo

# 添加父目录到路径以导入ner_model
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
import ner_model as zwk

# 创建蓝图
chat_bp = Blueprint('chat', __name__)

# 全局模型变量
chat_models = {}

def init_chat_models():
    """初始化聊天模型"""
    global chat_models
    if not chat_models:
        try:
            cache_model = 'best_roberta_rnn_model_ent_aug'
            device = torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')

            # 检查并加载tag2idx
            tag2idx_path = 'tmp_data/tag2idx.npy'
            if not os.path.exists(tag2idx_path):
                print("创建默认标签映射...")
                create_default_tag2idx()

            with open(tag2idx_path, 'rb') as f:
                tag2idx = pickle.load(f)
            idx2tag = list(tag2idx)

            # 初始化规则和TF-IDF
            rule = zwk.rule_find()
            tfidf_r = zwk.tfidf_alignment()

            # 尝试加载BERT模型
            model_name = 'model/chinese-roberta-wwm-ext'
            online_model_name = 'hfl/chinese-roberta-wwm-ext'

            # 设置镜像站点
            os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

            if os.path.exists(model_name):
                print(f"使用本地BERT模型: {model_name}")
                bert_tokenizer = BertTokenizer.from_pretrained(model_name)
                bert_model = zwk.Bert_Model(model_name, hidden_size=128, tag_num=len(tag2idx), bi=True)
            else:
                print(f"使用在线模型: {online_model_name}")
                try:
                    bert_tokenizer = BertTokenizer.from_pretrained(online_model_name)
                    bert_model = zwk.Bert_Model(online_model_name, hidden_size=128, tag_num=len(tag2idx), bi=True)
                except Exception as e:
                    print(f"在线模型加载失败: {e}")
                    print("使用备用模式...")
                    raise e

            # 加载训练好的权重
            model_path = f'model/{cache_model}.pt'
            if os.path.exists(model_path):
                print(f"加载训练权重: {model_path}")
                bert_model.load_state_dict(torch.load(model_path, map_location=device))
                bert_model = bert_model.to(device)
                bert_model.eval()
                print("✓ BERT模型加载成功")
            else:
                print(f"警告: 权重文件不存在")
                bert_model = bert_model.to(device)
                bert_model.eval()

            chat_models = {
                'bert_tokenizer': bert_tokenizer,
                'bert_model': bert_model,
                'idx2tag': idx2tag,
                'rule': rule,
                'tfidf_r': tfidf_r,
                'device': device
            }
            print("✓ 聊天模型初始化完成")

        except Exception as e:
            print(f"❌ 聊天模型初始化失败: {e}")
            create_fallback_chat_models()

def create_default_tag2idx():
    """创建默认的标签映射"""
    tag2idx = {
        '<PAD>': 0, 'O': 1,
        'B-疾病': 2, 'I-疾病': 3,
        'B-药品': 4, 'I-药品': 5,
        'B-食物': 6, 'I-食物': 7,
        'B-检查项目': 8, 'I-检查项目': 9,
        'B-科目': 10, 'I-科目': 11,
        'B-疾病症状': 12, 'I-疾病症状': 13,
        'B-治疗方法': 14, 'I-治疗方法': 15,
        'B-药品商': 16, 'I-药品商': 17
    }

    os.makedirs('tmp_data', exist_ok=True)
    with open('tmp_data/tag2idx.npy', 'wb') as f:
        pickle.dump(tag2idx, f)

def create_fallback_chat_models():
    """创建备用聊天模型"""
    global chat_models
    print("创建备用聊天模型...")

    class FallbackRule:
        def find(self, text):
            entities = []
            disease_keywords = ['感冒', '发烧', '头痛', '咳嗽', '高血压', '糖尿病', '心脏病']
            drug_keywords = ['阿司匹林', '布洛芬', '对乙酰氨基酚']

            for keyword in disease_keywords:
                if keyword in text:
                    start = text.find(keyword)
                    entities.append((start, start + len(keyword) - 1, '疾病', keyword))

            for keyword in drug_keywords:
                if keyword in text:
                    start = text.find(keyword)
                    entities.append((start, start + len(keyword) - 1, '药品', keyword))

            return entities

    chat_models = {
        'bert_tokenizer': None,
        'bert_model': None,
        'idx2tag': ['O', 'B-疾病', 'I-疾病', 'B-药品', 'I-药品'],
        'rule': FallbackRule(),
        'tfidf_r': zwk.tfidf_alignment(),
        'device': torch.device('cpu'),
        'fallback_mode': True
    }

def get_neo4j_client():
    """获取Neo4j客户端"""
    return py2neo.Graph(
        'bolt://localhost:7687',
        user='neo4j',
        password='wangqi20040401',
        name='doctor'
    )

def get_chat_entities(query, rule):
    """获取聊天实体"""
    entities = {}
    rule_results = rule.find(query)

    for _, _, entity_type, word in rule_results:
        entities[entity_type] = word

    return entities

def intent_recognition(query):
    """基于规则的意图识别"""
    # 定义关键词映射
    intent_keywords = {
        "简介": ["简介", "介绍", "什么是", "是什么", "了解"],
        "病因": ["病因", "原因", "为什么", "怎么得的", "怎么引起", "导致"],
        "预防": ["预防", "防止", "避免", "怎么预防", "如何预防", "需要注意", "注意什么", "注意事项"],
        "症状": ["症状", "表现", "征象", "有什么症状", "什么症状"],
        "治疗": ["治疗", "怎么治", "如何治疗", "怎么办", "怎样治", "治疗方法", "怎么处理", "如何处理"],
        "药品": ["药", "药物", "吃什么药", "用什么药", "药品", "服用"],
        "检查": ["检查", "化验", "需要做什么检查", "什么检查", "检测"],
        "食物": ["吃", "食物", "饮食", "能吃什么", "不能吃什么", "忌口", "宜吃", "忌吃"]
    }

    detected_intents = []
    query_lower = query.lower()

    for intent, keywords in intent_keywords.items():
        for keyword in keywords:
            if keyword in query_lower:
                detected_intents.append(intent)
                break

    # 如果没有检测到意图，默认返回简介
    if not detected_intents:
        detected_intents = ["简介"]

    return "、".join(detected_intents)

def generate_chat_prompt(response, query, client, bert_model, bert_tokenizer, rule, tfidf_r, device, idx2tag):
    """生成聊天提示词"""
    # 检查是否为备用模式
    if chat_models.get('fallback_mode'):
        entities = get_chat_entities(query, rule)
    else:
        entities = zwk.get_ner_result(bert_model, bert_tokenizer, query, rule, tfidf_r, device, idx2tag)

    yitu = []
    prompt = "<指令>你是一个医疗问答机器人，你需要根据给定的提示回答用户的问题。请注意，你的全部回答必须完全基于给定的提示，不可自由发挥。如果根据提示无法给出答案，立刻回答'根据已知信息无法回答该问题'。</指令>"
    prompt += "<指令>请你仅针对医疗类问题提供简洁和专业的回答。如果问题不是医疗相关的，你一定要回答'我只能回答医疗相关的问题。'，以明确告知你的回答限制。</指令>"

    # 处理疾病症状推测
    if '疾病症状' in entities and '疾病' not in entities:
        try:
            sql_q = "match (a:疾病)-[r:疾病的症状]->(b:疾病症状 {名称:'%s'}) return a.名称" % (entities['疾病症状'])
            res = list(client.run(sql_q).data()[0].values())
            if len(res) > 0:
                entities['疾病'] = res[0]
                all_en = "、".join(res)
                prompt += f"<提示>用户有{entities['疾病症状']}的情况，知识库推测其可能是得了{all_en}。请注意这只是一个推测，你需要明确告知用户这一点。</提示>"
        except:
            pass

    pre_len = len(prompt)

    # 处理各种意图
    if "简介" in response and '疾病' in entities:
        prompt += add_disease_info(entities['疾病'], '疾病简介', client)
        yitu.append('查询疾病简介')

    if "病因" in response and '疾病' in entities:
        prompt += add_disease_info(entities['疾病'], '疾病病因', client)
        yitu.append('查询疾病病因')

    if "预防" in response and '疾病' in entities:
        prompt += add_disease_info(entities['疾病'], '预防措施', client)
        yitu.append('查询预防措施')

    if "症状" in response and '疾病' in entities:
        prompt += add_disease_relation(entities['疾病'], '疾病的症状', '疾病症状', client)
        yitu.append('查询疾病的症状')

    if "药品" in response and '疾病' in entities:
        prompt += add_disease_relation(entities['疾病'], '疾病使用药品', '药品', client)
        yitu.append('查询疾病使用药品')

    if pre_len == len(prompt):
        prompt += f"<提示>提示：知识库异常，没有相关信息！请你直接回答'根据已知信息无法回答该问题'！</提示>"

    prompt += f"<用户问题>{query}</用户问题>"
    prompt += f"<注意>现在你已经知道给定的'<提示></提示>'和'<用户问题></用户问题>'了,你要极其认真的判断提示里是否有用户问题所需的信息，如果没有相关信息，你必须直接回答'根据已知信息无法回答该问题'。</注意>"

    return prompt, "、".join(yitu), entities

def add_disease_info(entity, info_type, client):
    """添加疾病信息"""
    add_prompt = ""
    try:
        sql_q = "match (a:疾病{名称:'%s'}) return a.%s" % (entity, info_type)
        res = client.run(sql_q).data()[0].values()
        add_prompt += f"<提示>用户对{entity}可能有查询{info_type}需求，知识库内容如下："
        if len(res) > 0:
            join_res = "".join(res)
            add_prompt += join_res
        else:
            add_prompt += "图谱中无信息，查找失败。"
        add_prompt += f"</提示>"
    except:
        pass
    return add_prompt

def add_disease_relation(entity, relation, target, client):
    """添加疾病关系"""
    add_prompt = ""
    try:
        sql_q = "match (a:疾病{名称:'%s'})-[r:%s]->(b:%s) return b.名称" % (entity, relation, target)
        res = client.run(sql_q).data()
        res = [list(data.values())[0] for data in res]
        add_prompt += f"<提示>用户对{entity}可能有查询{relation}需求，知识库内容如下："
        if len(res) > 0:
            join_res = "、".join(res)
            add_prompt += join_res
        else:
            add_prompt += "图谱中无信息，查找失败。"
        add_prompt += f"</提示>"
    except:
        pass
    return add_prompt

# 路由定义
@chat_bp.route('/chat')
def chat_page():
    """聊天页面"""
    if 'user_id' not in session:
        return render_template('login.html')
    return render_template('chat.html')

@chat_bp.route('/api/chat', methods=['POST'])
def api_chat():
    """聊天API - 基于BERT模型和知识图谱"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    data = request.get_json()
    query = data.get('query')

    try:
        # 初始化模型
        init_chat_models()

        # 意图识别
        intent_result = intent_recognition(query)

        # 实体识别
        client = get_neo4j_client()
        if chat_models.get('fallback_mode'):
            entities = get_chat_entities(query, chat_models['rule'])
        else:
            entities = zwk.get_ner_result(
                chat_models['bert_model'],
                chat_models['bert_tokenizer'],
                query,
                chat_models['rule'],
                chat_models['tfidf_r'],
                chat_models['device'],
                chat_models['idx2tag']
            )

        # 基于知识图谱生成回答
        response = generate_knowledge_based_answer(query, intent_result, entities, client)

        return jsonify({
            'success': True,
            'response': response,
            'entities': entities,
            'intent': intent_result,
            'debug_info': {
                'entities_detected': entities if session.get('is_admin') else None
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'处理失败: {str(e)}'
        })

def generate_knowledge_based_answer(query, intent, entities, client):
    """基于知识图谱生成回答"""
    if not entities or '疾病' not in entities:
        return f"抱歉，我无法从您的问题「{query}」中识别出具体的疾病名称。请提供更明确的疾病信息。"

    disease = entities['疾病']
    answer_parts = []

    try:
        # 根据意图查询相应信息
        if "简介" in intent:
            info = get_disease_property(disease, '疾病简介', client)
            if info:
                answer_parts.append(f"关于{disease}的简介：{info}")

        if "病因" in intent:
            info = get_disease_property(disease, '疾病病因', client)
            if info:
                answer_parts.append(f"{disease}的病因：{info}")

        if "预防" in intent:
            info = get_disease_property(disease, '预防措施', client)
            if info:
                answer_parts.append(f"{disease}的预防措施：{info}")

        if "症状" in intent:
            symptoms = get_disease_relations(disease, '疾病的症状', '疾病症状', client)
            if symptoms:
                answer_parts.append(f"{disease}的主要症状包括：{', '.join(symptoms)}")

        if "治疗" in intent:
            treatments = get_disease_relations(disease, '治疗的方法', '治疗方法', client)
            if treatments:
                answer_parts.append(f"{disease}的治疗方法包括：{', '.join(treatments)}")

        if "药品" in intent:
            drugs = get_disease_relations(disease, '疾病使用药品', '药品', client)
            if drugs:
                answer_parts.append(f"治疗{disease}常用的药品有：{', '.join(drugs)}")

        if "检查" in intent:
            checks = get_disease_relations(disease, '疾病所需检查', '检查项目', client)
            if checks:
                answer_parts.append(f"{disease}需要进行的检查项目：{', '.join(checks)}")

        if "食物" in intent:
            good_foods = get_disease_relations(disease, '疾病宜吃食物', '食物', client)
            bad_foods = get_disease_relations(disease, '疾病忌吃食物', '食物', client)
            if good_foods:
                answer_parts.append(f"{disease}患者宜吃：{', '.join(good_foods)}")
            if bad_foods:
                answer_parts.append(f"{disease}患者忌吃：{', '.join(bad_foods)}")

        # 如果没有找到任何信息
        if not answer_parts:
            return f"抱歉，知识库中暂时没有关于{disease}的相关信息。"

        return "\n\n".join(answer_parts)

    except Exception as e:
        print(f"生成回答时出错: {e}")
        return f"抱歉，查询{disease}的信息时出现错误。"

def get_disease_property(disease, property_name, client):
    """获取疾病属性"""
    try:
        query = f"MATCH (d:疾病{{名称:'{disease}'}}) RETURN d.{property_name} as info"
        result = client.run(query).data()
        if result and result[0]['info']:
            return result[0]['info']
    except:
        pass
    return None

def get_disease_relations(disease, relation, target_type, client):
    """获取疾病关系"""
    try:
        query = f"MATCH (d:疾病{{名称:'{disease}'}})-[r:{relation}]->(t:{target_type}) RETURN t.名称 as name"
        result = client.run(query).data()
        return [record['name'] for record in result if record['name']]
    except:
        pass
    return []
