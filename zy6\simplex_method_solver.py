#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单纯形法求解线性规划问题
问题：min 2x1 + 3x2 + x3
约束：-x1 + 4x2 + 2x3 >= 8
     3x1 + 2x2 >= 6
     xi >= 0, i = 1,2,3
"""

import numpy as np
import pandas as pd
from fractions import Fraction
import time

class SimplexMethodSolver:
    """单纯形法求解器"""
    
    def __init__(self, tolerance=1e-10):
        """
        初始化求解器
        
        Parameters:
        -----------
        tolerance : float
            数值容差
        """
        self.tolerance = tolerance
        self.tableau_history = []
        self.iteration_count = 0
        
    def setup_standard_form(self):
        """
        将问题转换为标准形式
        
        原问题：
        min 2x1 + 3x2 + x3
        s.t. -x1 + 4x2 + 2x3 >= 8
             3x1 + 2x2 >= 6
             xi >= 0
        
        标准形式：
        min 2x1 + 3x2 + x3 + 0s1 + 0s2
        s.t. x1 - 4x2 - 2x3 + s1 = -8  (乘以-1)
             -3x1 - 2x2 + s2 = -6       (乘以-1)
             xi >= 0, si >= 0
        """
        # 目标函数系数 (c^T)
        self.c = np.array([2, 3, 1, 0, 0], dtype=float)
        
        # 约束矩阵 A 和右端向量 b
        # 注意：为了得到正的右端向量，我们将不等式乘以-1
        self.A = np.array([
            [1, -4, -2, 1, 0],   # x1 - 4x2 - 2x3 + s1 = -8 (乘以-1后)
            [-3, -2, 0, 0, 1]    # -3x1 - 2x2 + s2 = -6 (乘以-1后)
        ], dtype=float)
        
        self.b = np.array([-8, -6], dtype=float)
        
        # 变量名
        self.var_names = ['x1', 'x2', 'x3', 's1', 's2']
        
        # 基变量索引 (初始基: s1, s2)
        self.basic_vars = [3, 4]  # s1, s2
        
        print("标准形式转换:")
        print("目标函数: min 2x1 + 3x2 + x3")
        print("约束条件:")
        print("  x1 - 4x2 - 2x3 + s1 = -8")
        print("  -3x1 - 2x2 + s2 = -6")
        print("  xi >= 0, si >= 0")
        print("注意：右端向量为负，需要使用两阶段法或大M法")
        
    def create_initial_tableau(self):
        """创建初始单纯形表"""
        m, n = self.A.shape
        
        # 创建单纯形表
        # 格式: [A | b]
        #      [c | 0]
        tableau = np.zeros((m + 1, n + 1))
        
        # 约束部分
        tableau[:m, :n] = self.A
        tableau[:m, n] = self.b
        
        # 目标函数部分 (注意：单纯形表中目标函数行的系数要取负号)
        tableau[m, :n] = -self.c
        tableau[m, n] = 0  # 初始目标函数值
        
        return tableau
    
    def find_pivot_column(self, tableau):
        """
        找到主元列（最负的目标函数系数对应的列）
        
        Parameters:
        -----------
        tableau : numpy.ndarray
            当前单纯形表
            
        Returns:
        --------
        int or None
            主元列索引，如果没有则返回None
        """
        m, n = tableau.shape
        obj_row = tableau[m-1, :n-1]  # 目标函数行（不包括RHS）
        
        # 找到最负的系数
        min_val = np.min(obj_row)
        if min_val >= -self.tolerance:
            return None  # 已达到最优
        
        return np.argmin(obj_row)
    
    def find_pivot_row(self, tableau, pivot_col):
        """
        找到主元行（最小比值测试）
        
        Parameters:
        -----------
        tableau : numpy.ndarray
            当前单纯形表
        pivot_col : int
            主元列索引
            
        Returns:
        --------
        int or None
            主元行索引，如果无界则返回None
        """
        m, n = tableau.shape
        rhs = tableau[:m-1, n-1]  # 右端向量
        pivot_column = tableau[:m-1, pivot_col]  # 主元列
        
        # 计算比值（只考虑正的主元列元素）
        ratios = []
        valid_rows = []
        
        for i in range(m-1):
            if pivot_column[i] > self.tolerance:
                ratio = rhs[i] / pivot_column[i]
                ratios.append(ratio)
                valid_rows.append(i)
            else:
                ratios.append(float('inf'))
                valid_rows.append(i)
        
        if not ratios or all(r == float('inf') for r in ratios):
            return None  # 无界解
        
        # 找到最小非负比值
        min_ratio = float('inf')
        pivot_row = None
        
        for i, ratio in enumerate(ratios):
            if ratio >= -self.tolerance and ratio < min_ratio:
                min_ratio = ratio
                pivot_row = i
        
        return pivot_row
    
    def pivot_operation(self, tableau, pivot_row, pivot_col):
        """
        执行主元操作
        
        Parameters:
        -----------
        tableau : numpy.ndarray
            当前单纯形表
        pivot_row : int
            主元行
        pivot_col : int
            主元列
            
        Returns:
        --------
        numpy.ndarray
            新的单纯形表
        """
        new_tableau = tableau.copy()
        m, n = tableau.shape
        
        # 主元元素
        pivot_element = tableau[pivot_row, pivot_col]
        
        # 主元行归一化
        new_tableau[pivot_row, :] = tableau[pivot_row, :] / pivot_element
        
        # 其他行消元
        for i in range(m):
            if i != pivot_row:
                multiplier = tableau[i, pivot_col]
                new_tableau[i, :] = tableau[i, :] - multiplier * new_tableau[pivot_row, :]
        
        return new_tableau
    
    def print_tableau(self, tableau, iteration):
        """打印单纯形表"""
        m, n = tableau.shape
        
        print(f"\n第 {iteration} 次迭代的单纯形表:")
        print("-" * 80)
        
        # 表头
        header = ["基变量"] + self.var_names + ["RHS"]
        print(f"{'':>8}", end="")
        for h in header[1:]:
            print(f"{h:>10}", end="")
        print()
        
        # 约束行
        for i in range(m-1):
            basic_var = self.var_names[self.basic_vars[i]] if i < len(self.basic_vars) else f"x{i+1}"
            print(f"{basic_var:>8}", end="")
            for j in range(n):
                print(f"{tableau[i, j]:>10.4f}", end="")
            print()
        
        # 目标函数行
        print(f"{'z':>8}", end="")
        for j in range(n):
            print(f"{tableau[m-1, j]:>10.4f}", end="")
        print()
        print("-" * 80)
    
    def solve(self):
        """
        使用单纯形法求解
        
        Returns:
        --------
        dict
            求解结果
        """
        print("=" * 80)
        print("单纯形法求解线性规划问题")
        print("=" * 80)
        
        # 设置标准形式
        self.setup_standard_form()
        
        # 检查初始可行性
        if np.any(self.b < 0):
            print("\n警告：初始基解不可行（存在负的RHS）")
            print("实际应用中需要使用两阶段法或大M法")
            print("这里我们继续演示单纯形法的基本步骤")
        
        # 创建初始单纯形表
        tableau = self.create_initial_tableau()
        self.iteration_count = 0
        
        # 打印初始表
        self.print_tableau(tableau, self.iteration_count)
        
        start_time = time.time()
        
        # 主循环
        while True:
            # 找主元列
            pivot_col = self.find_pivot_column(tableau)
            if pivot_col is None:
                print("\n已达到最优解！")
                break
            
            # 找主元行
            pivot_row = self.find_pivot_row(tableau, pivot_col)
            if pivot_row is None:
                print("\n问题无界！")
                return {
                    'status': 'unbounded',
                    'optimal_point': None,
                    'optimal_value': float('-inf'),
                    'iterations': self.iteration_count
                }
            
            print(f"\n主元位置: 行 {pivot_row + 1}, 列 {pivot_col + 1} ({self.var_names[pivot_col]})")
            print(f"进入变量: {self.var_names[pivot_col]}")
            print(f"离开变量: {self.var_names[self.basic_vars[pivot_row]]}")
            
            # 更新基变量
            self.basic_vars[pivot_row] = pivot_col
            
            # 主元操作
            tableau = self.pivot_operation(tableau, pivot_row, pivot_col)
            self.iteration_count += 1
            
            # 打印新表
            self.print_tableau(tableau, self.iteration_count)
            
            # 记录历史
            self.tableau_history.append(tableau.copy())
        
        end_time = time.time()
        
        # 提取解
        solution = self.extract_solution(tableau)
        solution['iterations'] = self.iteration_count
        solution['time'] = end_time - start_time
        
        return solution
    
    def extract_solution(self, tableau):
        """
        从最终单纯形表提取解
        
        Parameters:
        -----------
        tableau : numpy.ndarray
            最终单纯形表
            
        Returns:
        --------
        dict
            解的信息
        """
        m, n = tableau.shape
        
        # 提取基变量的值
        solution_vector = np.zeros(len(self.var_names))
        
        for i, var_idx in enumerate(self.basic_vars):
            if var_idx < len(self.var_names):
                solution_vector[var_idx] = tableau[i, n-1]
        
        # 原问题的解（只取前3个变量）
        x_optimal = solution_vector[:3]
        
        # 目标函数值（注意符号）
        optimal_value = -tableau[m-1, n-1]
        
        print("\n" + "=" * 60)
        print("求解结果:")
        print("=" * 60)
        print(f"最优解: x* = [{x_optimal[0]:.6f}, {x_optimal[1]:.6f}, {x_optimal[2]:.6f}]")
        print(f"最优值: f* = {optimal_value:.6f}")
        
        # 验证约束
        print("\n约束验证:")
        print(f"约束1: -x1 + 4x2 + 2x3 = {-x_optimal[0] + 4*x_optimal[1] + 2*x_optimal[2]:.6f} >= 8")
        print(f"约束2: 3x1 + 2x2 = {3*x_optimal[0] + 2*x_optimal[1]:.6f} >= 6")
        print(f"约束3-5: x1={x_optimal[0]:.6f}, x2={x_optimal[1]:.6f}, x3={x_optimal[2]:.6f} >= 0")
        
        # 检查可行性
        constraint1 = -x_optimal[0] + 4*x_optimal[1] + 2*x_optimal[2]
        constraint2 = 3*x_optimal[0] + 2*x_optimal[1]
        
        feasible = (constraint1 >= 8 - 1e-6 and 
                   constraint2 >= 6 - 1e-6 and 
                   np.all(x_optimal >= -1e-6))
        
        print(f"解的可行性: {'可行' if feasible else '不可行'}")
        
        return {
            'status': 'optimal',
            'optimal_point': x_optimal,
            'optimal_value': optimal_value,
            'full_solution': solution_vector,
            'basic_variables': self.basic_vars,
            'feasible': feasible
        }
    
    def export_tableau_history(self, filename):
        """导出单纯形表历史"""
        if not self.tableau_history:
            print("没有单纯形表历史数据")
            return
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("单纯形法迭代历史\n")
            f.write("=" * 50 + "\n\n")
            
            for i, tableau in enumerate(self.tableau_history):
                f.write(f"第 {i+1} 次迭代:\n")
                f.write("-" * 30 + "\n")
                
                # 写入表格
                m, n = tableau.shape
                header = ["基变量"] + self.var_names + ["RHS"]
                f.write(f"{'':>8}")
                for h in header[1:]:
                    f.write(f"{h:>10}")
                f.write("\n")
                
                for j in range(m-1):
                    basic_var = self.var_names[self.basic_vars[j]] if j < len(self.basic_vars) else f"x{j+1}"
                    f.write(f"{basic_var:>8}")
                    for k in range(n):
                        f.write(f"{tableau[j, k]:>10.4f}")
                    f.write("\n")
                
                f.write(f"{'z':>8}")
                for k in range(n):
                    f.write(f"{tableau[m-1, k]:>10.4f}")
                f.write("\n\n")
        
        print(f"单纯形表历史已导出到: {filename}")

def solve_with_scipy():
    """使用scipy的linprog求解作为对比"""
    try:
        from scipy.optimize import linprog
        
        print("\n" + "=" * 60)
        print("使用scipy.optimize.linprog求解（对比）:")
        print("=" * 60)
        
        # 标准形式
        c = np.array([2, 3, 1])
        A_ub = np.array([
            [1, -4, -2],   # x1 - 4x2 - 2x3 <= -8
            [-3, -2, 0]    # -3x1 - 2x2 <= -6
        ])
        b_ub = np.array([-8, -6])
        bounds = [(0, None), (0, None), (0, None)]
        
        start_time = time.time()
        result = linprog(c, A_ub=A_ub, b_ub=b_ub, bounds=bounds, method='highs')
        end_time = time.time()
        
        if result.success:
            print(f"最优解: x* = [{result.x[0]:.6f}, {result.x[1]:.6f}, {result.x[2]:.6f}]")
            print(f"最优值: f* = {result.fun:.6f}")
            print(f"求解时间: {end_time - start_time:.6f} 秒")
            print(f"迭代次数: {result.nit if hasattr(result, 'nit') else 'N/A'}")
            print("求解成功")
            
            return {
                'optimal_point': result.x,
                'optimal_value': result.fun,
                'time': end_time - start_time,
                'iterations': result.nit if hasattr(result, 'nit') else None,
                'success': True
            }
        else:
            print("scipy求解失败:", result.message)
            return {'success': False}
            
    except ImportError:
        print("scipy未安装，无法进行对比")
        return {'success': False}

def main():
    """主函数"""
    # 创建求解器
    solver = SimplexMethodSolver(tolerance=1e-10)
    
    # 求解问题
    result = solver.solve()
    
    # 导出单纯形表历史
    solver.export_tableau_history('zy6/simplex_tableau_history.txt')
    
    # scipy对比
    scipy_result = solve_with_scipy()
    
    # 性能比较
    print("\n" + "=" * 80)
    print("性能总结:")
    print("=" * 80)
    print(f"手工实现单纯形法:")
    print(f"  - 迭代次数: {result['iterations']}")
    print(f"  - 求解时间: {result['time']:.6f} 秒")
    print(f"  - 最优值: {result['optimal_value']:.6f}")
    
    if scipy_result['success']:
        print(f"\nscipy.optimize.linprog:")
        print(f"  - 迭代次数: {scipy_result['iterations']}")
        print(f"  - 求解时间: {scipy_result['time']:.6f} 秒")
        print(f"  - 最优值: {scipy_result['optimal_value']:.6f}")

if __name__ == "__main__":
    main()
