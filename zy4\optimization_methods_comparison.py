#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三种优化算法比较：最速下降法、牛顿法、共轭梯度法
问题：f(x) = 4(x1+1)^2 + 2(x2-1)^2 + x1 + x2 + 10
初始点：x0 = (0, 0)^T
精度：ε = 10^-5
"""

import numpy as np
import time
import csv
from typing import List, Dict, Tuple

class OptimizationComparison:
    """优化算法比较类"""
    
    def __init__(self, epsilon=1e-5, max_iterations=1000):
        self.epsilon = epsilon
        self.max_iterations = max_iterations
        self.results = {}
    
    def objective_function(self, x: np.ndarray) -> float:
        """目标函数 f(x) = 4(x1+1)^2 + 2(x2-1)^2 + x1 + x2 + 10"""
        x1, x2 = x[0], x[1]
        return 4*(x1 + 1)**2 + 2*(x2 - 1)**2 + x1 + x2 + 10
    
    def gradient(self, x: np.ndarray) -> np.ndarray:
        """计算梯度"""
        x1, x2 = x[0], x[1]
        return np.array([8*x1 + 9, 4*x2 - 3])
    
    def hessian(self, x: np.ndarray) -> np.ndarray:
        """计算Hessian矩阵"""
        return np.array([[8, 0], [0, 4]])
    
    def steepest_descent(self, x0: np.ndarray) -> Dict:
        """最速下降法"""
        x = np.array(x0, dtype=float)
        history = []
        start_time = time.time()
        
        print("=" * 80)
        print("最速下降法求解")
        print("=" * 80)
        print(f"{'k':<4} {'x1':<12} {'x2':<12} {'f(x)':<12} {'||∇f||':<12} {'α':<12}")
        print("-" * 80)
        
        for k in range(self.max_iterations):
            grad = self.gradient(x)
            grad_norm = np.linalg.norm(grad)
            f_val = self.objective_function(x)
            
            history.append({
                'iteration': k,
                'x': x.copy(),
                'f_value': f_val,
                'gradient_norm': grad_norm
            })
            
            if grad_norm < self.epsilon:
                print(f"{k:<4} {x[0]:<12.6f} {x[1]:<12.6f} {f_val:<12.6f} {grad_norm:<12.6e} {'收敛':<12}")
                break
            
            # 精确线搜索
            H = self.hessian(x)
            alpha = np.dot(grad, grad) / np.dot(grad, np.dot(H, grad))
            
            print(f"{k:<4} {x[0]:<12.6f} {x[1]:<12.6f} {f_val:<12.6f} {grad_norm:<12.6e} {alpha:<12.6f}")
            
            # 更新点
            x = x - alpha * grad
        
        end_time = time.time()
        
        return {
            'method': '最速下降法',
            'optimal_point': x,
            'optimal_value': self.objective_function(x),
            'iterations': len(history),
            'time': end_time - start_time,
            'history': history,
            'converged': grad_norm < self.epsilon
        }
    
    def newton_method(self, x0: np.ndarray) -> Dict:
        """牛顿法"""
        x = np.array(x0, dtype=float)
        history = []
        start_time = time.time()
        
        print("\n" + "=" * 80)
        print("牛顿法求解")
        print("=" * 80)
        print(f"{'k':<4} {'x1':<12} {'x2':<12} {'f(x)':<12} {'||∇f||':<12} {'det(H)':<12}")
        print("-" * 80)
        
        for k in range(self.max_iterations):
            grad = self.gradient(x)
            grad_norm = np.linalg.norm(grad)
            f_val = self.objective_function(x)
            H = self.hessian(x)
            det_H = np.linalg.det(H)
            
            history.append({
                'iteration': k,
                'x': x.copy(),
                'f_value': f_val,
                'gradient_norm': grad_norm
            })
            
            if grad_norm < self.epsilon:
                print(f"{k:<4} {x[0]:<12.6f} {x[1]:<12.6f} {f_val:<12.6f} {grad_norm:<12.6e} {'收敛':<12}")
                break
            
            print(f"{k:<4} {x[0]:<12.6f} {x[1]:<12.6f} {f_val:<12.6f} {grad_norm:<12.6e} {det_H:<12.2f}")
            
            # 牛顿方向
            newton_direction = np.linalg.solve(H, -grad)
            x = x + newton_direction
        
        end_time = time.time()
        
        return {
            'method': '牛顿法',
            'optimal_point': x,
            'optimal_value': self.objective_function(x),
            'iterations': len(history),
            'time': end_time - start_time,
            'history': history,
            'converged': grad_norm < self.epsilon
        }
    
    def conjugate_gradient(self, x0: np.ndarray) -> Dict:
        """共轭梯度法"""
        x = np.array(x0, dtype=float)
        history = []
        start_time = time.time()
        
        print("\n" + "=" * 80)
        print("共轭梯度法求解")
        print("=" * 80)
        print(f"{'k':<4} {'x1':<12} {'x2':<12} {'f(x)':<12} {'||∇f||':<12} {'β':<12}")
        print("-" * 80)
        
        # 初始化
        grad = self.gradient(x)
        direction = -grad  # 初始搜索方向
        
        for k in range(self.max_iterations):
            grad_norm = np.linalg.norm(grad)
            f_val = self.objective_function(x)
            
            history.append({
                'iteration': k,
                'x': x.copy(),
                'f_value': f_val,
                'gradient_norm': grad_norm
            })
            
            if grad_norm < self.epsilon:
                print(f"{k:<4} {x[0]:<12.6f} {x[1]:<12.6f} {f_val:<12.6f} {grad_norm:<12.6e} {'收敛':<12}")
                break
            
            # 精确线搜索
            H = self.hessian(x)
            alpha = np.dot(grad, direction) / np.dot(direction, np.dot(H, direction))
            
            # 更新点
            x_new = x + alpha * direction
            grad_new = self.gradient(x_new)
            
            # Fletcher-Reeves公式计算β
            beta = np.dot(grad_new, grad_new) / np.dot(grad, grad)
            
            print(f"{k:<4} {x[0]:<12.6f} {x[1]:<12.6f} {f_val:<12.6f} {grad_norm:<12.6e} {beta:<12.6f}")
            
            # 更新搜索方向
            direction = -grad_new + beta * direction
            
            # 更新变量
            x = x_new
            grad = grad_new
        
        end_time = time.time()
        
        return {
            'method': '共轭梯度法',
            'optimal_point': x,
            'optimal_value': self.objective_function(x),
            'iterations': len(history),
            'time': end_time - start_time,
            'history': history,
            'converged': grad_norm < self.epsilon
        }
    
    def compare_methods(self, x0: np.ndarray) -> None:
        """比较三种方法"""
        print("开始三种优化算法比较分析")
        print("问题: f(x) = 4(x1+1)² + 2(x2-1)² + x1 + x2 + 10")
        print(f"初始点: x0 = {x0}")
        print(f"收敛精度: ε = {self.epsilon}")
        
        # 运行三种算法
        self.results['steepest_descent'] = self.steepest_descent(x0)
        self.results['newton'] = self.newton_method(x0)
        self.results['conjugate_gradient'] = self.conjugate_gradient(x0)
        
        # 理论最优解
        x_theory = np.array([-9/8, 3/4])
        f_theory = self.objective_function(x_theory)
        
        # 打印比较结果
        print("\n" + "=" * 100)
        print("算法性能比较")
        print("=" * 100)
        print(f"{'算法':<15} {'迭代次数':<10} {'计算时间(s)':<12} {'最优值':<12} {'误差':<12} {'收敛性':<10}")
        print("-" * 100)
        
        for method_name, result in self.results.items():
            error = np.linalg.norm(result['optimal_point'] - x_theory)
            converged = "✓" if result['converged'] else "✗"
            print(f"{result['method']:<15} {result['iterations']:<10} {result['time']:<12.6f} "
                  f"{result['optimal_value']:<12.6f} {error:<12.6e} {converged:<10}")
        
        print(f"{'理论最优解':<15} {'-':<10} {'-':<12} {f_theory:<12.6f} {0:<12.6e} {'✓':<10}")
    
    def export_results(self, filename: str) -> None:
        """导出结果到CSV文件"""
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['算法', '迭代次数', 'x1', 'x2', '最优值', '计算时间', '收敛性']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for result in self.results.values():
                writer.writerow({
                    '算法': result['method'],
                    '迭代次数': result['iterations'],
                    'x1': result['optimal_point'][0],
                    'x2': result['optimal_point'][1],
                    '最优值': result['optimal_value'],
                    '计算时间': result['time'],
                    '收敛性': '收敛' if result['converged'] else '未收敛'
                })
        
        print(f"\n结果已导出到: {filename}")

def theoretical_analysis():
    """理论分析"""
    print("\n" + "=" * 100)
    print("理论分析")
    print("=" * 100)
    
    print("目标函数: f(x) = 4(x1+1)² + 2(x2-1)² + x1 + x2 + 10")
    print("梯度: ∇f(x) = [8x1 + 9, 4x2 - 3]")
    print("Hessian矩阵: H = [[8, 0], [0, 4]]")
    print("特征值: λ1 = 8, λ2 = 4")
    print("条件数: κ = 2")
    print("理论最优解: x* = [-1.125, 0.75]")
    print("理论最优值: f* = 9.8125")
    
    print("\n算法理论收敛性:")
    print("- 最速下降法: 线性收敛，收敛率 ρ = (κ-1)/(κ+1) = 1/3")
    print("- 牛顿法: 二次收敛，对二次函数1次收敛")
    print("- 共轭梯度法: 对二次函数有限步收敛，理论上2次收敛")

def main():
    """主函数"""
    # 初始化
    x0 = np.array([0.0, 0.0])
    epsilon = 1e-5
    
    # 理论分析
    theoretical_analysis()
    
    # 创建比较器
    comparator = OptimizationComparison(epsilon=epsilon)
    
    # 比较三种方法
    comparator.compare_methods(x0)
    
    # 导出结果
    comparator.export_results('zy4/optimization_comparison_results.csv')
    
    # 详细分析
    print("\n" + "=" * 100)
    print("详细性能分析")
    print("=" * 100)
    
    for method_name, result in comparator.results.items():
        print(f"\n{result['method']}:")
        print(f"  - 最优点: [{result['optimal_point'][0]:.8f}, {result['optimal_point'][1]:.8f}]")
        print(f"  - 最优值: {result['optimal_value']:.8f}")
        print(f"  - 迭代次数: {result['iterations']}")
        print(f"  - 计算时间: {result['time']:.6f}秒")
        print(f"  - 收敛性: {'成功收敛' if result['converged'] else '未收敛'}")

if __name__ == "__main__":
    main()
