{% extends "base.html" %}

{% block title %}个人资料 - 医疗智能问答系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <!-- 左侧导航 -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>用户中心</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('auth.profile_page') }}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-user-circle me-2"></i>个人资料
                    </a>
                    <a href="{{ url_for('auth.settings_page') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-cog me-2"></i>账户设置
                    </a>
                    <a href="{{ url_for('auth.messages_page') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-bell me-2"></i>消息中心
                    </a>
                    <a href="{{ url_for('auth.help_page') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-question-circle me-2"></i>帮助中心
                    </a>
                </div>
            </div>
        </div>

        <!-- 右侧内容 -->
        <div class="col-md-9">
            <!-- 个人信息卡片 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-id-card me-2"></i>基本信息</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="avatar-container mb-3">
                                <img id="userAvatar" src="https://via.placeholder.com/120x120/007bff/ffffff?text=用户"
                                     class="rounded-circle" width="120" height="120" alt="用户头像">
                                <div class="mt-2">
                                    <button class="btn btn-sm btn-outline-primary" onclick="changeAvatar()">
                                        <i class="fas fa-camera me-1"></i>更换头像
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-9">
                            <form id="profileForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">用户名</label>
                                        <input type="text" class="form-control" id="username" readonly>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">邮箱</label>
                                        <input type="email" class="form-control" id="email" placeholder="请输入邮箱">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">手机号</label>
                                        <input type="tel" class="form-control" id="phone" placeholder="请输入手机号">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">注册时间</label>
                                        <input type="text" class="form-control" id="createdAt" readonly>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">个人简介</label>
                                        <textarea class="form-control" id="bio" rows="3" placeholder="介绍一下自己..."></textarea>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <button type="button" class="btn btn-primary" onclick="updateProfile()">
                                        <i class="fas fa-save me-1"></i>保存修改
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 使用统计卡片 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>使用统计</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="stat-item">
                                <h3 class="text-primary" id="totalQuestions">-</h3>
                                <p class="text-muted mb-0">总提问数</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <h3 class="text-success" id="totalSessions">-</h3>
                                <p class="text-muted mb-0">会话次数</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <h3 class="text-info" id="avgSessionTime">-</h3>
                                <p class="text-muted mb-0">平均会话时长</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <h3 class="text-warning" id="loginCount">-</h3>
                                <p class="text-muted mb-0">登录次数</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近活动 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>最近活动</h5>
                </div>
                <div class="card-body">
                    <div id="recentActivity">
                        <div class="text-center text-muted">
                            <i class="fas fa-spinner fa-spin"></i> 加载中...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 头像上传模态框 -->
<div class="modal fade" id="avatarModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">更换头像</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <input type="file" id="avatarInput" accept="image/*" style="display: none;">
                    <div class="mb-3">
                        <img id="avatarPreview" src="" class="rounded-circle" width="150" height="150" style="display: none;">
                    </div>
                    <button class="btn btn-outline-primary" onclick="document.getElementById('avatarInput').click()">
                        <i class="fas fa-upload me-1"></i>选择图片
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="uploadAvatar()">确认上传</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载时获取用户信息
$(document).ready(function() {
    loadUserProfile();
    loadUserStatistics();
});

// 加载用户资料
function loadUserProfile() {
    fetch('/api/user/profile')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const user = data.user;
                $('#username').val(user.username);
                $('#email').val(user.email || '');
                $('#phone').val(user.phone || '');
                $('#bio').val(user.bio || '');
                $('#createdAt').val(formatDate(user.created_at));
                $('#loginCount').text(user.login_count || 0);

                // 加载头像
                if (user.avatar) {
                    $('#userAvatar').attr('src', user.avatar);
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

// 加载用户统计
function loadUserStatistics() {
    fetch('/api/user/statistics')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const stats = data.statistics;
                $('#totalQuestions').text(stats.total_questions);
                $('#totalSessions').text(stats.total_sessions);
                $('#avgSessionTime').text(stats.avg_session_time);

                // 显示最近活动
                const activityHtml = stats.recent_activity.map(activity => `
                    <div class="d-flex align-items-center mb-2">
                        <div class="flex-shrink-0">
                            <i class="fas fa-circle text-primary" style="font-size: 8px;"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold">${activity.action}</div>
                            <small class="text-muted">${activity.date}</small>
                        </div>
                    </div>
                `).join('');
                $('#recentActivity').html(activityHtml);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            $('#recentActivity').html('<div class="text-center text-muted">加载失败</div>');
        });
}

// 更新用户资料
function updateProfile() {
    const profileData = {
        email: $('#email').val(),
        phone: $('#phone').val(),
        bio: $('#bio').val()
    };

    fetch('/api/user/update-profile', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(profileData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', '更新失败，请重试');
    });
}

// 更换头像
function changeAvatar() {
    $('#avatarModal').modal('show');
}

// 头像预览
$('#avatarInput').change(function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            $('#avatarPreview').attr('src', e.target.result).show();
        };
        reader.readAsDataURL(file);
    }
});

// 上传头像
function uploadAvatar() {
    const file = $('#avatarInput')[0].files[0];
    if (!file) {
        showAlert('warning', '请先选择图片');
        return;
    }

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
        showAlert('warning', '请选择图片文件');
        return;
    }

    // 检查文件大小（限制为2MB）
    if (file.size > 2 * 1024 * 1024) {
        showAlert('warning', '图片大小不能超过2MB');
        return;
    }

    // 转换为base64
    const reader = new FileReader();
    reader.onload = function(e) {
        const avatarData = e.target.result;

        // 发送到服务器
        fetch('/api/user/upload-avatar', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                avatar_data: avatarData
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                $('#userAvatar').attr('src', data.avatar_url);
                $('#avatarModal').modal('hide');
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', '上传失败，请重试');
        });
    };

    reader.readAsDataURL(file);
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}

// 显示提示消息
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container').prepend(alertHtml);

    // 3秒后自动消失
    setTimeout(() => {
        $('.alert').alert('close');
    }, 3000);
}
</script>
{% endblock %}
