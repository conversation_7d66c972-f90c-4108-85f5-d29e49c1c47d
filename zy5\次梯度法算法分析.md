# 次梯度法求解线性规划问题详细分析

## 1. 问题描述

### 1.1 线性规划问题
求解线性规划问题：

$$\begin{align}
\min \quad & 2x_1 + 3x_2 + x_3 \\
\text{s.t.} \quad & -x_1 + 4x_2 + 2x_3 \geq 8 \\
& 3x_1 + 2x_2 \geq 6 \\
& x_i \geq 0, \quad i = 1,2,3
\end{align}$$

### 1.2 问题特点
- **问题类型**：线性规划问题
- **变量维数**：3维
- **约束数量**：5个（2个不等式约束 + 3个非负约束）
- **目标函数**：线性函数
- **约束函数**：线性不等式约束

### 1.3 标准形式转换

将问题转换为标准的约束优化形式：

$$\begin{align}
\min \quad & f(x) = 2x_1 + 3x_2 + x_3 \\
\text{s.t.} \quad & g_1(x) = 8 - (-x_1 + 4x_2 + 2x_3) \leq 0 \\
& g_2(x) = 6 - (3x_1 + 2x_2) \leq 0 \\
& g_3(x) = -x_1 \leq 0 \\
& g_4(x) = -x_2 \leq 0 \\
& g_5(x) = -x_3 \leq 0
\end{align}$$

## 2. 次梯度法原理

### 2.1 基本概念

#### 2.1.1 次梯度定义
对于凸函数 $f: \mathbb{R}^n \to \mathbb{R}$，在点 $x$ 处的次梯度是满足以下条件的向量 $g$：

$$f(y) \geq f(x) + g^T(y - x), \quad \forall y \in \mathbb{R}^n$$

次梯度的集合称为次微分，记为 $\partial f(x)$。

#### 2.1.2 次梯度的性质
1. **存在性**：凸函数在其定义域内部的每一点都存在次梯度
2. **唯一性**：当函数在某点可微时，次梯度唯一且等于梯度
3. **凸组合**：次微分是凸集
4. **链式法则**：复合函数的次梯度满足链式法则

### 2.2 拉格朗日对偶方法

#### 2.2.1 拉格朗日函数
构造拉格朗日函数：

$$L(x, \lambda) = f(x) + \sum_{i=1}^5 \lambda_i g_i(x)$$

其中 $\lambda_i \geq 0$ 是拉格朗日乘子。

#### 2.2.2 对偶函数
对偶函数定义为：

$$q(\lambda) = \inf_{x} L(x, \lambda)$$

#### 2.2.3 对偶问题
对偶问题为：

$$\max_{\lambda \geq 0} q(\lambda)$$

### 2.3 次梯度计算

#### 2.3.1 关于 $x$ 的次梯度
拉格朗日函数关于 $x$ 的次梯度为：

$$\partial_x L(x, \lambda) = \nabla f(x) + \sum_{i=1}^5 \lambda_i \nabla g_i(x)$$

对于本问题：
- $\nabla f(x) = [2, 3, 1]^T$
- $\nabla g_1(x) = [1, -4, -2]^T$
- $\nabla g_2(x) = [-3, -2, 0]^T$
- $\nabla g_3(x) = [-1, 0, 0]^T$
- $\nabla g_4(x) = [0, -1, 0]^T$
- $\nabla g_5(x) = [0, 0, -1]^T$

#### 2.3.2 关于 $\lambda$ 的次梯度
拉格朗日函数关于 $\lambda$ 的次梯度为：

$$\partial_\lambda L(x, \lambda) = [g_1(x), g_2(x), g_3(x), g_4(x), g_5(x)]^T$$

## 3. 算法步骤

### 3.1 次梯度法迭代格式

#### 3.1.1 原变量更新
$$x^{(k+1)} = x^{(k)} - \alpha_k^x \cdot \partial_x L(x^{(k)}, \lambda^{(k)})$$

#### 3.1.2 对偶变量更新
$$\lambda^{(k+1)} = \max\{0, \lambda^{(k)} + \alpha_k^\lambda \cdot \partial_\lambda L(x^{(k)}, \lambda^{(k)})\}$$

其中 $\max\{0, \cdot\}$ 是逐元素操作，确保 $\lambda \geq 0$。

### 3.2 步长选择策略

#### 3.2.1 常数步长
$$\alpha_k = \alpha > 0 \quad \text{(常数)}$$

**优点**：简单易实现
**缺点**：可能不收敛到最优解

#### 3.2.2 递减步长
$$\alpha_k = \frac{\alpha_0}{k+1}$$

**优点**：保证收敛
**缺点**：收敛速度可能较慢

#### 3.2.3 平方可和步长
$$\alpha_k = \frac{\alpha_0}{\sqrt{k+1}}$$

满足条件：$\sum_{k=0}^\infty \alpha_k = \infty$ 且 $\sum_{k=0}^\infty \alpha_k^2 < \infty$

### 3.3 收敛性分析

#### 3.3.1 收敛条件
对于凸优化问题，次梯度法在以下条件下收敛：
1. 目标函数和约束函数都是凸函数
2. 步长满足：$\sum_{k=0}^\infty \alpha_k = \infty$ 且 $\sum_{k=0}^\infty \alpha_k^2 < \infty$
3. 次梯度有界

#### 3.3.2 收敛速度
次梯度法的收敛速度为 $O(1/\sqrt{k})$，比梯度下降法慢。

## 4. 伪代码

```
算法：次梯度法求解约束优化问题
输入：初始点 x0, 初始乘子 λ0, 最大迭代次数 max_iter, 容差 ε
输出：最优解 x*, 最优值 f*

1. 初始化
   x ← x0
   λ ← λ0
   k ← 0
   best_feasible_x ← None
   best_feasible_value ← +∞
   
2. 主循环
   while k < max_iter do
       // 计算约束违反量
       violations ← compute_violations(x)
       
       // 检查可行性
       if max(violations) < ε then
           if f(x) < best_feasible_value then
               best_feasible_x ← x
               best_feasible_value ← f(x)
           end if
       end if
       
       // 计算次梯度
       subgrad_x ← ∇f(x) + Σ λi * ∇gi(x)
       subgrad_λ ← violations
       
       // 计算步长
       αx ← step_size_rule(k)
       αλ ← step_size_rule(k)
       
       // 更新变量
       x ← x - αx * subgrad_x
       λ ← max(0, λ + αλ * subgrad_λ)
       
       // 检查收敛
       if convergence_check(x, λ) then
           break
       end if
       
       k ← k + 1
   end while
   
3. 输出结果
   if best_feasible_x ≠ None then
       return best_feasible_x, best_feasible_value
   else
       return x, f(x)  // 可能不可行
   end if
```

## 5. 数学公式详解

### 5.1 约束违反量计算

对于每个约束 $g_i(x) \leq 0$，违反量定义为：

$$\text{violation}_i = \max\{0, g_i(x)\}$$

具体计算：

#### 5.1.1 约束1
$$g_1(x) = 8 - (-x_1 + 4x_2 + 2x_3) = 8 + x_1 - 4x_2 - 2x_3$$
$$\text{violation}_1 = \max\{0, 8 + x_1 - 4x_2 - 2x_3\}$$

#### 5.1.2 约束2
$$g_2(x) = 6 - (3x_1 + 2x_2) = 6 - 3x_1 - 2x_2$$
$$\text{violation}_2 = \max\{0, 6 - 3x_1 - 2x_2\}$$

#### 5.1.3 非负约束
$$g_3(x) = -x_1, \quad \text{violation}_3 = \max\{0, -x_1\}$$
$$g_4(x) = -x_2, \quad \text{violation}_4 = \max\{0, -x_2\}$$
$$g_5(x) = -x_3, \quad \text{violation}_5 = \max\{0, -x_3\}$$

### 5.2 次梯度计算公式

#### 5.2.1 关于 $x$ 的次梯度
$$\frac{\partial L}{\partial x} = \begin{bmatrix} 2 \\ 3 \\ 1 \end{bmatrix} + \lambda_1 \begin{bmatrix} 1 \\ -4 \\ -2 \end{bmatrix} + \lambda_2 \begin{bmatrix} -3 \\ -2 \\ 0 \end{bmatrix} + \lambda_3 \begin{bmatrix} -1 \\ 0 \\ 0 \end{bmatrix} + \lambda_4 \begin{bmatrix} 0 \\ -1 \\ 0 \end{bmatrix} + \lambda_5 \begin{bmatrix} 0 \\ 0 \\ -1 \end{bmatrix}$$

展开得：
$$\frac{\partial L}{\partial x_1} = 2 + \lambda_1 - 3\lambda_2 - \lambda_3$$
$$\frac{\partial L}{\partial x_2} = 3 - 4\lambda_1 - 2\lambda_2 - \lambda_4$$
$$\frac{\partial L}{\partial x_3} = 1 - 2\lambda_1 - \lambda_5$$

#### 5.2.2 关于 $\lambda$ 的次梯度
$$\frac{\partial L}{\partial \lambda} = \begin{bmatrix} g_1(x) \\ g_2(x) \\ g_3(x) \\ g_4(x) \\ g_5(x) \end{bmatrix} = \begin{bmatrix} 8 + x_1 - 4x_2 - 2x_3 \\ 6 - 3x_1 - 2x_2 \\ -x_1 \\ -x_2 \\ -x_3 \end{bmatrix}$$

## 6. 算法特点

### 6.1 优点

1. **适用性广**：可处理非光滑凸优化问题
2. **实现简单**：不需要计算Hessian矩阵
3. **内存需求低**：只需存储当前迭代点
4. **理论保证**：对凸问题有收敛性保证

### 6.2 缺点

1. **收敛速度慢**：$O(1/\sqrt{k})$ 的收敛速度
2. **步长选择敏感**：步长选择对性能影响很大
3. **可能振荡**：在最优解附近可能出现振荡
4. **精度有限**：难以获得高精度解

### 6.3 适用场景

1. **大规模问题**：当问题规模很大时
2. **非光滑问题**：当目标函数或约束不可微时
3. **在线优化**：当数据流式到达时
4. **分布式优化**：当需要分布式求解时

## 7. 与其他方法比较

### 7.1 与单纯形法比较

| 特性 | 次梯度法 | 单纯形法 |
|------|----------|----------|
| **适用问题** | 一般凸优化 | 线性规划 |
| **收敛速度** | $O(1/\sqrt{k})$ | 有限步收敛 |
| **内存需求** | $O(n)$ | $O(mn)$ |
| **实现复杂度** | 简单 | 复杂 |
| **数值稳定性** | 好 | 可能有数值问题 |

### 7.2 与内点法比较

| 特性 | 次梯度法 | 内点法 |
|------|----------|--------|
| **收敛速度** | 慢 | 快 |
| **每次迭代成本** | 低 | 高 |
| **可扩展性** | 好 | 一般 |
| **精度** | 中等 | 高 |
| **初始点要求** | 无特殊要求 | 需要内点 |

## 8. 手工计算示例

### 8.1 第一次迭代详细计算

**初始点**：$x^{(0)} = (1, 1, 1)^T$，$\lambda^{(0)} = (1, 1, 0, 0, 0)^T$

#### 步骤1：计算约束违反量
$$g_1(x^{(0)}) = 8 + 1 - 4 \cdot 1 - 2 \cdot 1 = 8 + 1 - 4 - 2 = 3$$
$$g_2(x^{(0)}) = 6 - 3 \cdot 1 - 2 \cdot 1 = 6 - 3 - 2 = 1$$
$$g_3(x^{(0)}) = -1 < 0$$
$$g_4(x^{(0)}) = -1 < 0$$
$$g_5(x^{(0)}) = -1 < 0$$

违反量向量：$\text{violations} = [3, 1, 0, 0, 0]^T$

#### 步骤2：计算次梯度
关于 $x$ 的次梯度：
$$\frac{\partial L}{\partial x_1} = 2 + 1 \cdot 1 - 3 \cdot 1 - 0 = 2 + 1 - 3 = 0$$
$$\frac{\partial L}{\partial x_2} = 3 - 4 \cdot 1 - 2 \cdot 1 - 0 = 3 - 4 - 2 = -3$$
$$\frac{\partial L}{\partial x_3} = 1 - 2 \cdot 1 - 0 = 1 - 2 = -1$$

次梯度：$\nabla_x L = [0, -3, -1]^T$

关于 $\lambda$ 的次梯度：$\nabla_\lambda L = [3, 1, 0, 0, 0]^T$

#### 步骤3：更新变量
使用步长 $\alpha = 1/(k+1) = 1$：

$$x^{(1)} = x^{(0)} - \alpha \nabla_x L = \begin{bmatrix} 1 \\ 1 \\ 1 \end{bmatrix} - 1 \cdot \begin{bmatrix} 0 \\ -3 \\ -1 \end{bmatrix} = \begin{bmatrix} 1 \\ 4 \\ 2 \end{bmatrix}$$

$$\lambda^{(1)} = \max\{0, \lambda^{(0)} + \alpha \nabla_\lambda L\} = \max\left\{0, \begin{bmatrix} 1 \\ 1 \\ 0 \\ 0 \\ 0 \end{bmatrix} + 1 \cdot \begin{bmatrix} 3 \\ 1 \\ 0 \\ 0 \\ 0 \end{bmatrix}\right\} = \begin{bmatrix} 4 \\ 2 \\ 0 \\ 0 \\ 0 \end{bmatrix}$$

### 8.2 可行性检验

对于点 $x^{(1)} = (1, 4, 2)^T$：

$$-x_1 + 4x_2 + 2x_3 = -1 + 16 + 4 = 19 \geq 8 \quad \checkmark$$
$$3x_1 + 2x_2 = 3 + 8 = 11 \geq 6 \quad \checkmark$$
$$x_1 = 1 \geq 0, \quad x_2 = 4 \geq 0, \quad x_3 = 2 \geq 0 \quad \checkmark$$

该点是可行的，目标函数值：$f(x^{(1)}) = 2 \cdot 1 + 3 \cdot 4 + 2 = 16$

## 9. Python实现要点

### 9.1 核心算法实现

```python
def solve(self, x0, lambda0, step_size_rule='diminishing'):
    x = np.array(x0, dtype=float)
    lambda_vec = np.array(lambda0, dtype=float)

    for k in range(self.max_iterations):
        # 计算次梯度
        subgrad_x = self.subgradient_x(x, lambda_vec)
        subgrad_lambda = self.subgradient_lambda(x)

        # 计算步长
        if step_size_rule == 'diminishing':
            alpha_x = 1.0 / (k + 1)
            alpha_lambda = 1.0 / (k + 1)

        # 更新变量
        x = x - alpha_x * subgrad_x
        lambda_vec = np.maximum(0, lambda_vec + alpha_lambda * subgrad_lambda)

        # 检查收敛和可行性
        if self.is_feasible(x):
            # 更新最佳可行解
            pass

    return result
```

### 9.2 关键实现细节

1. **约束违反量计算**：使用 `max(0, g(x))` 确保非负
2. **拉格朗日乘子更新**：使用 `np.maximum(0, ...)` 保持非负性
3. **可行性检查**：所有约束违反量小于容差
4. **最佳解跟踪**：记录遇到的最佳可行解

### 9.3 数值稳定性考虑

1. **步长选择**：避免步长过大导致发散
2. **约束处理**：正确处理等式和不等式约束
3. **收敛判断**：基于变量变化和约束满足程度

## 10. 实验设计

### 10.1 测试参数

| 参数 | 值 | 说明 |
|------|----|----|
| 初始点 | $(1, 1, 1)^T$ | 不可行点 |
| 初始乘子 | $(1, 1, 0, 0, 0)^T$ | 正值初始化 |
| 最大迭代次数 | 100 | 防止无限循环 |
| 收敛容差 | $10^{-6}$ | 数值精度要求 |
| 步长规则 | 递减步长 | $\alpha_k = 1/(k+1)$ |

### 10.2 评价指标

1. **收敛性**：是否找到可行解
2. **最优性**：目标函数值
3. **约束满足**：约束违反量
4. **迭代次数**：算法效率
5. **数值稳定性**：解的质量

### 10.3 对比实验

与单纯形法（scipy.optimize.linprog）进行对比：
- 最优值比较
- 求解时间比较
- 数值精度比较

## 11. 预期结果分析

### 11.1 理论最优解

通过图解法或单纯形法可以得到理论最优解。对于本问题，最优解应该在约束的交点处。

### 11.2 次梯度法表现预期

1. **收敛性**：应该能找到可行解
2. **收敛速度**：相对较慢，需要较多迭代
3. **解的质量**：接近但可能不完全等于理论最优解
4. **数值稳定性**：总体稳定，但可能有小幅振荡

### 11.3 算法改进方向

1. **自适应步长**：根据迭代进展调整步长
2. **加速技术**：使用Nesterov加速等技术
3. **约束处理**：更精细的约束违反量处理
4. **混合方法**：结合其他优化算法

## 12. 总结

### 12.1 次梯度法特点

1. **通用性强**：适用于非光滑凸优化问题
2. **实现简单**：算法逻辑清晰，易于编程
3. **理论保证**：对凸问题有收敛性保证
4. **可扩展性好**：适合大规模和分布式问题

### 12.2 应用价值

1. **学术研究**：理解非光滑优化的基础方法
2. **工程应用**：处理实际中的约束优化问题
3. **算法开发**：作为更复杂算法的组成部分
4. **教学工具**：展示凸优化的基本思想

### 12.3 局限性

1. **收敛速度慢**：相比专门的线性规划算法
2. **精度有限**：难以获得高精度解
3. **参数敏感**：步长选择对性能影响大
4. **可能振荡**：在最优解附近可能不稳定

次梯度法虽然不是求解线性规划问题的最优选择，但它提供了一个通用的框架来理解和处理更广泛的凸优化问题，具有重要的理论和实践价值。
