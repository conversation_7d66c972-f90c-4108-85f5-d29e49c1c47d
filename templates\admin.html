{% extends "base.html" %}

{% block title %}管理员面板 - 医疗智能问答系统{% endblock %}

{% block extra_css %}
<style>
.admin-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: transform 0.3s ease;
}

.admin-card:hover {
    transform: translateY(-5px);
}

.admin-card i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.activity-log {
    max-height: 400px;
    overflow-y: auto;
}

.log-item {
    padding: 0.75rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
}

.log-item:last-child {
    border-bottom: none;
}

.log-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.2rem;
    color: white;
}

.log-icon.info { background: #17a2b8; }
.log-icon.success { background: #28a745; }
.log-icon.warning { background: #ffc107; }
.log-icon.danger { background: #dc3545; }

.system-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.status-online { background: #28a745; }
.status-offline { background: #dc3545; }
.status-warning { background: #ffc107; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fas fa-tachometer-alt me-2"></i>管理员面板</h2>
            <p class="text-muted">系统管理和监控中心</p>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number text-primary" id="totalUsers">-</div>
            <div class="stat-label">总用户数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number text-success" id="totalQuestions">-</div>
            <div class="stat-label">总问答数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number text-warning" id="totalNodes">-</div>
            <div class="stat-label">知识节点</div>
        </div>
        <div class="stat-card">
            <div class="stat-number text-info" id="totalRelations">-</div>
            <div class="stat-label">知识关系</div>
        </div>
    </div>

    <div class="row">
        <!-- 系统管理 -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>系统管理</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="admin-card" onclick="openKnowledgeGraphManager()">
                                <i class="fas fa-project-diagram"></i>
                                <h6>知识图谱管理</h6>
                                <p class="mb-0">管理医疗知识图谱</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="admin-card" onclick="openUserManager()">
                                <i class="fas fa-users"></i>
                                <h6>用户管理</h6>
                                <p class="mb-0">管理系统用户</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="admin-card" onclick="openSystemSettings()">
                                <i class="fas fa-sliders-h"></i>
                                <h6>系统设置</h6>
                                <p class="mb-0">配置系统参数</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="admin-card" onclick="openModelManager()">
                                <i class="fas fa-brain"></i>
                                <h6>模型管理</h6>
                                <p class="mb-0">管理AI模型</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="admin-card" onclick="openDataBackup()">
                                <i class="fas fa-database"></i>
                                <h6>数据备份</h6>
                                <p class="mb-0">备份系统数据</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="admin-card" onclick="openSystemLogs()">
                                <i class="fas fa-file-alt"></i>
                                <h6>系统日志</h6>
                                <p class="mb-0">查看系统日志</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统状态 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-heartbeat me-2"></i>系统状态</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>服务状态</h6>
                            <div class="mb-2">
                                <div class="system-status">
                                    <div class="status-indicator status-online"></div>
                                    <span>Web服务</span>
                                    <span class="ms-auto text-success">运行中</span>
                                </div>
                            </div>
                            <div class="mb-2">
                                <div class="system-status">
                                    <div class="status-indicator status-online"></div>
                                    <span>Neo4j数据库</span>
                                    <span class="ms-auto text-success">运行中</span>
                                </div>
                            </div>
                            <div class="mb-2">
                                <div class="system-status">
                                    <div class="status-indicator status-online"></div>
                                    <span>AI模型服务</span>
                                    <span class="ms-auto text-success">运行中</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>系统资源</h6>
                            <div class="mb-2">
                                <div class="d-flex justify-content-between">
                                    <span>CPU使用率</span>
                                    <span id="cpuUsage">-</span>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar" id="cpuProgress" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="mb-2">
                                <div class="d-flex justify-content-between">
                                    <span>内存使用率</span>
                                    <span id="memoryUsage">-</span>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar" id="memoryProgress" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="mb-2">
                                <div class="d-flex justify-content-between">
                                    <span>磁盘使用率</span>
                                    <span id="diskUsage">-</span>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar" id="diskProgress" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 活动日志 -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>最近活动</h5>
                </div>
                <div class="card-body p-0">
                    <div class="activity-log" id="activityLog">
                        <div class="log-item">
                            <div class="log-icon info">
                                <i class="fas fa-user"></i>
                            </div>
                            <div>
                                <div class="fw-bold">用户登录</div>
                                <small class="text-muted">admin 登录系统</small>
                                <div class="text-muted" style="font-size: 0.8rem;">刚刚</div>
                            </div>
                        </div>
                        <div class="log-item">
                            <div class="log-icon success">
                                <i class="fas fa-database"></i>
                            </div>
                            <div>
                                <div class="fw-bold">数据同步</div>
                                <small class="text-muted">知识图谱数据同步完成</small>
                                <div class="text-muted" style="font-size: 0.8rem;">5分钟前</div>
                            </div>
                        </div>
                        <div class="log-item">
                            <div class="log-icon info">
                                <i class="fas fa-comments"></i>
                            </div>
                            <div>
                                <div class="fw-bold">问答记录</div>
                                <small class="text-muted">用户提问关于感冒的问题</small>
                                <div class="text-muted" style="font-size: 0.8rem;">10分钟前</div>
                            </div>
                        </div>
                        <div class="log-item">
                            <div class="log-icon warning">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div>
                                <div class="fw-bold">系统警告</div>
                                <small class="text-muted">模型响应时间较长</small>
                                <div class="text-muted" style="font-size: 0.8rem;">15分钟前</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作模态框 -->
<div class="modal fade" id="quickActionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">快速操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 动态内容 -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    loadDashboardData();
    startRealTimeUpdates();
});

function loadDashboardData() {
    // 加载统计数据
    loadStatistics();

    // 加载系统状态
    loadSystemStatus();

    // 加载活动日志
    loadActivityLog();
}

function loadStatistics() {
    // 模拟数据，实际应该从API获取
    $('#totalUsers').text('156');
    $('#totalQuestions').text('2,847');
    $('#totalNodes').text('44,656');
    $('#totalRelations').text('312,159');
}

function loadSystemStatus() {
    // 模拟系统资源数据
    updateResourceUsage('cpu', 45);
    updateResourceUsage('memory', 67);
    updateResourceUsage('disk', 23);
}

function updateResourceUsage(type, percentage) {
    $(`#${type}Usage`).text(`${percentage}%`);
    $(`#${type}Progress`).css('width', `${percentage}%`);

    // 根据使用率设置颜色
    const progressBar = $(`#${type}Progress`);
    progressBar.removeClass('bg-success bg-warning bg-danger');

    if (percentage < 50) {
        progressBar.addClass('bg-success');
    } else if (percentage < 80) {
        progressBar.addClass('bg-warning');
    } else {
        progressBar.addClass('bg-danger');
    }
}

function loadActivityLog() {
    // 实际应该从API获取最新的活动日志
    console.log('加载活动日志...');
}

function startRealTimeUpdates() {
    // 每30秒更新一次系统状态
    setInterval(function() {
        loadSystemStatus();
    }, 30000);

    // 每60秒更新一次活动日志
    setInterval(function() {
        loadActivityLog();
    }, 60000);
}

// 管理功能
function openKnowledgeGraphManager() {
    showQuickAction('知识图谱管理', `
        <div class="row">
            <div class="col-12">
                <h6>知识图谱操作</h6>
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="window.open('http://localhost:7474', '_blank')">
                        <i class="fas fa-external-link-alt me-2"></i>打开Neo4j浏览器
                    </button>
                    <button class="btn btn-success" onclick="rebuildGraph()">
                        <i class="fas fa-sync me-2"></i>重建知识图谱
                    </button>
                    <button class="btn btn-info" onclick="exportGraph()">
                        <i class="fas fa-download me-2"></i>导出图谱数据
                    </button>
                    <button class="btn btn-warning" onclick="optimizeGraph()">
                        <i class="fas fa-tools me-2"></i>优化图谱性能
                    </button>
                </div>
            </div>
        </div>
    `);
}

function openUserManager() {
    showQuickAction('用户管理', `
        <div class="row">
            <div class="col-12">
                <h6>用户操作</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>用户名</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>admin</td>
                                <td><span class="badge bg-danger">管理员</span></td>
                                <td><span class="badge bg-success">活跃</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">编辑</button>
                                </td>
                            </tr>
                            <tr>
                                <td>user1</td>
                                <td><span class="badge bg-primary">用户</span></td>
                                <td><span class="badge bg-success">活跃</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">编辑</button>
                                    <button class="btn btn-sm btn-outline-danger">禁用</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `);
}

function openSystemSettings() {
    showQuickAction('系统设置', `
        <div class="row">
            <div class="col-12">
                <h6>系统配置</h6>
                <form>
                    <div class="mb-3">
                        <label class="form-label">默认AI模型</label>
                        <select class="form-select">
                            <option selected>BERT医疗问答模型</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">最大并发用户数</label>
                        <input type="number" class="form-control" value="100">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">会话超时时间（分钟）</label>
                        <input type="number" class="form-control" value="30">
                    </div>
                    <button type="submit" class="btn btn-primary">保存设置</button>
                </form>
            </div>
        </div>
    `);
}

function openModelManager() {
    showQuickAction('模型管理', `
        <div class="row">
            <div class="col-12">
                <h6>AI模型状态</h6>
                <div class="list-group">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <strong>BERT医疗问答模型</strong>
                            <br><small class="text-muted">基于您的BERT权重的医疗问答系统</small>
                        </div>
                        <span class="badge bg-success">运行中</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <strong>BERT NER模型</strong>
                            <br><small class="text-muted">医疗命名实体识别</small>
                        </div>
                        <span class="badge bg-success">运行中</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <strong>意图分类模型</strong>
                            <br><small class="text-muted">用户意图识别</small>
                        </div>
                        <span class="badge bg-success">运行中</span>
                    </div>
                </div>
            </div>
        </div>
    `);
}

function openDataBackup() {
    showQuickAction('数据备份', `
        <div class="row">
            <div class="col-12">
                <h6>备份操作</h6>
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="createBackup()">
                        <i class="fas fa-save me-2"></i>创建完整备份
                    </button>
                    <button class="btn btn-info" onclick="createIncrementalBackup()">
                        <i class="fas fa-plus me-2"></i>创建增量备份
                    </button>
                    <button class="btn btn-success" onclick="restoreBackup()">
                        <i class="fas fa-upload me-2"></i>恢复备份
                    </button>
                </div>
                <hr>
                <h6>备份历史</h6>
                <div class="list-group">
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between">
                            <span>完整备份 - 2024-01-15</span>
                            <button class="btn btn-sm btn-outline-primary">恢复</button>
                        </div>
                    </div>
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between">
                            <span>增量备份 - 2024-01-14</span>
                            <button class="btn btn-sm btn-outline-primary">恢复</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `);
}

function openSystemLogs() {
    showQuickAction('系统日志', `
        <div class="row">
            <div class="col-12">
                <h6>日志查看</h6>
                <div class="mb-3">
                    <select class="form-select">
                        <option>应用日志</option>
                        <option>错误日志</option>
                        <option>访问日志</option>
                        <option>数据库日志</option>
                    </select>
                </div>
                <div style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 1rem; border-radius: 5px; font-family: monospace; font-size: 0.8rem;">
                    2024-01-15 10:30:15 [INFO] 用户登录: admin<br>
                    2024-01-15 10:29:45 [INFO] 系统启动完成<br>
                    2024-01-15 10:29:30 [INFO] 加载AI模型: BERT NER<br>
                    2024-01-15 10:29:20 [INFO] 连接Neo4j数据库<br>
                    2024-01-15 10:29:10 [INFO] 系统初始化开始<br>
                </div>
            </div>
        </div>
    `);
}

function showQuickAction(title, content) {
    $('#modalTitle').text(title);
    $('#modalBody').html(content);
    $('#quickActionModal').modal('show');
}

// 具体操作函数
function rebuildGraph() {
    if (confirm('确定要重建知识图谱吗？这可能需要一些时间。')) {
        Utils.showMessage('开始重建知识图谱...', 'info');
        // 实际的重建逻辑
    }
}

function exportGraph() {
    Utils.showMessage('开始导出图谱数据...', 'info');
    // 实际的导出逻辑
}

function optimizeGraph() {
    if (confirm('确定要优化图谱性能吗？')) {
        Utils.showMessage('开始优化图谱性能...', 'info');
        // 实际的优化逻辑
    }
}

function createBackup() {
    Utils.showMessage('开始创建完整备份...', 'info');
    // 实际的备份逻辑
}

function createIncrementalBackup() {
    Utils.showMessage('开始创建增量备份...', 'info');
    // 实际的增量备份逻辑
}

function restoreBackup() {
    if (confirm('确定要恢复备份吗？这将覆盖当前数据。')) {
        Utils.showMessage('开始恢复备份...', 'warning');
        // 实际的恢复逻辑
    }
}
</script>
{% endblock %}
