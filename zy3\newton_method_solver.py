#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
牛顿法求解优化问题
问题：f(x) = 4(x1+1)^2 + 2(x2-1)^2 + x1 + x2 + 10
初始点：x0 = (0, 0)^T
精度：ε = 10^-5
"""

import numpy as np
import math

class NewtonMethodSolver:
    """牛顿法求解器"""
    
    def __init__(self, epsilon=1e-5, max_iterations=1000):
        """
        初始化求解器
        
        Parameters:
        -----------
        epsilon : float
            收敛精度
        max_iterations : int
            最大迭代次数
        """
        self.epsilon = epsilon
        self.max_iterations = max_iterations
        self.history = []  # 存储迭代历史
        
    def objective_function(self, x):
        """
        目标函数 f(x) = 4(x1+1)^2 + 2(x2-1)^2 + x1 + x2 + 10
        
        Parameters:
        -----------
        x : numpy.ndarray
            输入向量 [x1, x2]
            
        Returns:
        --------
        float
            函数值
        """
        x1, x2 = x[0], x[1]
        return 4*(x1 + 1)**2 + 2*(x2 - 1)**2 + x1 + x2 + 10
    
    def gradient(self, x):
        """
        计算梯度 ∇f(x)
        
        ∂f/∂x1 = 8(x1+1) + 1 = 8x1 + 9
        ∂f/∂x2 = 4(x2-1) + 1 = 4x2 - 3
        
        Parameters:
        -----------
        x : numpy.ndarray
            输入向量 [x1, x2]
            
        Returns:
        --------
        numpy.ndarray
            梯度向量
        """
        x1, x2 = x[0], x[1]
        grad_x1 = 8*x1 + 9
        grad_x2 = 4*x2 - 3
        return np.array([grad_x1, grad_x2])
    
    def hessian(self, x):
        """
        计算Hessian矩阵 ∇²f(x)
        
        ∂²f/∂x1² = 8
        ∂²f/∂x2² = 4
        ∂²f/∂x1∂x2 = 0
        
        Parameters:
        -----------
        x : numpy.ndarray
            输入向量 [x1, x2]
            
        Returns:
        --------
        numpy.ndarray
            Hessian矩阵
        """
        # 对于二次函数，Hessian矩阵是常数矩阵
        return np.array([[8, 0], [0, 4]])
    
    def solve(self, x0):
        """
        使用牛顿法求解优化问题
        
        Parameters:
        -----------
        x0 : numpy.ndarray
            初始点
            
        Returns:
        --------
        dict
            求解结果
        """
        x = np.array(x0, dtype=float)
        self.history = []
        
        print("=" * 80)
        print("牛顿法求解优化问题")
        print("=" * 80)
        print(f"目标函数: f(x) = 4(x1+1)² + 2(x2-1)² + x1 + x2 + 10")
        print(f"初始点: x0 = {x0}")
        print(f"收敛精度: ε = {self.epsilon}")
        print("-" * 80)
        print(f"{'迭代k':<6} {'x1':<12} {'x2':<12} {'f(x)':<12} {'||∇f||':<12} {'det(H)':<12}")
        print("-" * 80)
        
        for k in range(self.max_iterations):
            # 计算梯度
            grad = self.gradient(x)
            grad_norm = np.linalg.norm(grad)
            f_val = self.objective_function(x)
            
            # 计算Hessian矩阵
            H = self.hessian(x)
            det_H = np.linalg.det(H)
            
            # 记录迭代历史
            iteration_info = {
                'iteration': k,
                'x': x.copy(),
                'f_value': f_val,
                'gradient': grad.copy(),
                'gradient_norm': grad_norm,
                'hessian': H.copy(),
                'hessian_det': det_H
            }
            
            # 检查收敛条件
            if grad_norm < self.epsilon:
                iteration_info['converged'] = True
                self.history.append(iteration_info)
                print(f"{k:<6} {x[0]:<12.6f} {x[1]:<12.6f} {f_val:<12.6f} {grad_norm:<12.6e} {'收敛':<12}")
                break
            
            iteration_info['converged'] = False
            self.history.append(iteration_info)
            
            print(f"{k:<6} {x[0]:<12.6f} {x[1]:<12.6f} {f_val:<12.6f} {grad_norm:<12.6e} {det_H:<12.2f}")
            
            # 检查Hessian矩阵是否正定
            eigenvalues = np.linalg.eigvals(H)
            if np.any(eigenvalues <= 0):
                print(f"警告: Hessian矩阵不是正定的，特征值: {eigenvalues}")
                break
            
            # 牛顿方向：解线性方程组 H * d = -∇f
            try:
                newton_direction = -np.linalg.solve(H, grad)
            except np.linalg.LinAlgError:
                print("错误: Hessian矩阵奇异，无法求解")
                break
            
            # 更新点：x^(k+1) = x^(k) + d^(k)
            x = x + newton_direction
        
        else:
            print(f"达到最大迭代次数 {self.max_iterations}")
        
        print("-" * 80)
        print(f"最终结果:")
        print(f"最优点: x* = [{x[0]:.8f}, {x[1]:.8f}]")
        print(f"最优值: f(x*) = {self.objective_function(x):.8f}")
        print(f"梯度范数: ||∇f(x*)|| = {np.linalg.norm(self.gradient(x)):.8e}")
        print(f"迭代次数: {len(self.history)}")
        
        return {
            'optimal_point': x,
            'optimal_value': self.objective_function(x),
            'gradient_norm': np.linalg.norm(self.gradient(x)),
            'iterations': len(self.history),
            'converged': grad_norm < self.epsilon,
            'history': self.history
        }
    
    def analyze_quadratic_convergence(self):
        """分析二次收敛性"""
        if len(self.history) < 3:
            print("迭代次数不足，无法分析收敛性")
            return
        
        print("\n" + "=" * 80)
        print("二次收敛性分析:")
        print("=" * 80)
        
        # 计算理论最优解
        x_theory = np.array([-9/8, 3/4])
        
        print(f"{'k':<4} {'||x^k - x*||':<15} {'||x^k+1 - x*||':<15} {'收敛比率':<15}")
        print("-" * 60)
        
        for i in range(len(self.history) - 1):
            x_k = self.history[i]['x']
            x_k1 = self.history[i+1]['x']
            
            error_k = np.linalg.norm(x_k - x_theory)
            error_k1 = np.linalg.norm(x_k1 - x_theory)
            
            if error_k > 1e-12:
                ratio = error_k1 / (error_k**2)
                print(f"{i:<4} {error_k:<15.6e} {error_k1:<15.6e} {ratio:<15.6e}")
            else:
                print(f"{i:<4} {error_k:<15.6e} {error_k1:<15.6e} {'机器精度':<15}")
    
    def export_results_to_csv(self, filename):
        """导出结果到CSV文件"""
        if not self.history:
            print("没有迭代历史数据")
            return
        
        import csv
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['迭代次数', 'x1', 'x2', '目标函数值', '梯度x1', '梯度x2', '梯度范数', 'Hessian行列式']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for h in self.history:
                writer.writerow({
                    '迭代次数': h['iteration'],
                    'x1': h['x'][0],
                    'x2': h['x'][1],
                    '目标函数值': h['f_value'],
                    '梯度x1': h['gradient'][0],
                    '梯度x2': h['gradient'][1],
                    '梯度范数': h['gradient_norm'],
                    'Hessian行列式': h['hessian_det']
                })
        
        print(f"结果已导出到: {filename}")

def verify_theoretical_solution():
    """验证理论解析解"""
    print("\n" + "=" * 80)
    print("理论解析解验证:")
    print("=" * 80)
    print("对于二次函数，最优解可通过 ∇f(x*) = 0 求得:")
    print("∂f/∂x1 = 8x1 + 9 = 0  =>  x1* = -9/8 = -1.125")
    print("∂f/∂x2 = 4x2 - 3 = 0  =>  x2* = 3/4 = 0.75")
    
    solver = NewtonMethodSolver()
    x_theory = np.array([-9/8, 3/4])
    f_theory = solver.objective_function(x_theory)
    grad_theory = solver.gradient(x_theory)
    H_theory = solver.hessian(x_theory)
    
    print(f"理论最优点: x* = [{x_theory[0]:.8f}, {x_theory[1]:.8f}]")
    print(f"理论最优值: f(x*) = {f_theory:.8f}")
    print(f"理论梯度: ∇f(x*) = [{grad_theory[0]:.8e}, {grad_theory[1]:.8e}]")
    print(f"Hessian矩阵:")
    print(f"H = [[{H_theory[0,0]:.1f}, {H_theory[0,1]:.1f}],")
    print(f"     [{H_theory[1,0]:.1f}, {H_theory[1,1]:.1f}]]")
    
    # 分析Hessian矩阵性质
    eigenvalues = np.linalg.eigvals(H_theory)
    condition_number = np.max(eigenvalues) / np.min(eigenvalues)
    
    print(f"Hessian特征值: λ1 = {eigenvalues[0]:.1f}, λ2 = {eigenvalues[1]:.1f}")
    print(f"条件数: κ = {condition_number:.1f}")
    print(f"正定性: {'是' if np.all(eigenvalues > 0) else '否'}")
    
    return x_theory, f_theory

def compare_with_steepest_descent():
    """与最速下降法比较"""
    print("\n" + "=" * 80)
    print("牛顿法 vs 最速下降法:")
    print("=" * 80)
    
    comparison_table = [
        ["特性", "牛顿法", "最速下降法"],
        ["收敛速度", "二次收敛", "线性收敛"],
        ["每次迭代计算量", "O(n³)", "O(n)"],
        ["内存需求", "O(n²)", "O(n)"],
        ["对于二次函数", "1次迭代收敛", "多次迭代"],
        ["Hessian信息", "需要", "不需要"],
        ["适用范围", "光滑强凸函数", "一般凸函数"]
    ]
    
    for row in comparison_table:
        print(f"{row[0]:<15} {row[1]:<15} {row[2]:<15}")

def main():
    """主函数"""
    # 问题参数
    x0 = np.array([0.0, 0.0])  # 初始点
    epsilon = 1e-5  # 收敛精度
    
    # 理论分析
    x_theory, f_theory = verify_theoretical_solution()
    
    # 比较分析
    compare_with_steepest_descent()
    
    # 创建求解器
    solver = NewtonMethodSolver(epsilon=epsilon)
    
    # 求解问题
    result = solver.solve(x0)
    
    # 分析收敛性
    solver.analyze_quadratic_convergence()
    
    # 导出结果
    solver.export_results_to_csv('zy3/newton_method_results.csv')
    
    # 计算误差
    error = np.linalg.norm(result['optimal_point'] - x_theory)
    print(f"\n数值解与理论解误差: ||x_num - x_theory|| = {error:.8e}")
    
    # 算法性能总结
    print("\n" + "=" * 80)
    print("算法性能总结:")
    print("=" * 80)
    print(f"收敛性: {'✓ 成功收敛' if result['converged'] else '✗ 未收敛'}")
    print(f"迭代次数: {result['iterations']}")
    print(f"数值精度: {error:.2e}")
    print(f"收敛类型: 二次收敛")
    print(f"计算效率: 对于二次函数理论上1次迭代即可收敛")

if __name__ == "__main__":
    main()
