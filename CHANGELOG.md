# 更新日志

本文档记录了DoctorRAG项目的所有重要更改。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 计划添加
- 多语言支持（英文、日文）
- 语音问答功能
- 移动端响应式优化
- RESTful API完整文档
- 机器学习模型性能优化
- 实时聊天功能
- 用户反馈系统
- 数据导出功能

### 计划改进
- 知识图谱查询性能优化
- NER模型准确率提升
- 前端界面用户体验改进
- 系统安全性增强

## [1.0.0] - 2024-01-01

### 新增功能
- ✨ 智能医疗问答系统
  - BERT-based命名实体识别
  - 三层NER融合策略（深度学习+规则+相似度）
  - 8种医疗意图识别
  - 知识图谱检索增强生成

- 📊 知识图谱可视化
  - D3.js交互式图谱展示
  - 8种医疗实体类型可视化
  - 实体关系探索功能
  - 搜索和过滤功能
  - 实时统计信息展示

- 👥 用户管理系统
  - 用户注册和登录
  - 个人资料管理
  - 头像上传功能
  - 聊天记录保存
  - 用户活动统计

- 🔧 系统管理功能
  - 管理员控制面板
  - 系统健康监控
  - 用户管理界面
  - 数据备份功能
  - 日志查看系统

- 🎨 用户界面
  - Bootstrap 5响应式设计
  - 现代化UI风格
  - 深色/浅色主题切换
  - 移动端适配
  - 无障碍访问支持

### 技术特性
- 🏗️ 模块化架构设计
  - Flask蓝图模式
  - 代码分层清晰
  - 易于维护和扩展

- 🤖 AI模型集成
  - 中文RoBERTa预训练模型
  - 自定义RNN层
  - TF-IDF相似度计算
  - 规则匹配系统

- 📈 性能优化
  - 查询缓存机制
  - 数据库连接池
  - 异步处理支持
  - 内存使用优化

- 🔒 安全特性
  - 用户认证和授权
  - 密码加密存储
  - CSRF保护
  - XSS防护
  - SQL注入防护

### 数据资源
- 📚 医疗知识库
  - 8,808种疾病详细信息
  - 50,000+医疗实体
  - 640万行NER训练数据
  - 100,000+知识关系

- 🏷️ 实体类别
  - 疾病实体
  - 症状实体
  - 药品实体
  - 食物实体
  - 检查项目
  - 治疗方法
  - 科目分类
  - 药品商信息

### 系统要求
- Python 3.8+
- Neo4j 4.0+
- 8GB+ RAM
- 10GB+ 磁盘空间

### 支持的平台
- Windows 10/11
- macOS 10.15+
- Ubuntu 18.04+
- CentOS 7+

### API接口
- 智能问答API
- 知识图谱查询API
- 用户管理API
- 系统监控API

### 测试覆盖
- 单元测试
- 集成测试
- 系统测试
- 性能测试

### 文档
- 完整的README文档
- API接口文档
- 部署指南
- 开发者指南
- 故障排除指南

---

## 版本说明

### 版本号格式
本项目使用语义化版本号：`主版本号.次版本号.修订号`

- **主版本号**：不兼容的API修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

### 更新类型
- `新增` - 新功能
- `改进` - 对现有功能的改进
- `修复` - 问题修复
- `移除` - 移除的功能
- `安全` - 安全相关的修复
- `废弃` - 即将移除的功能

### 发布周期
- **主版本**：每年1-2次
- **次版本**：每季度1次
- **修订版本**：根据需要随时发布

---

## 贡献指南

如果您想为本项目贡献代码，请：

1. Fork本项目
2. 创建功能分支
3. 提交您的更改
4. 创建Pull Request
5. 等待代码审查

详细信息请参考 [CONTRIBUTING.md](CONTRIBUTING.md)。

---

## 支持

如果您遇到问题或有建议，请：

- 查看 [FAQ](docs/FAQ.md)
- 搜索 [Issues](https://github.com/your-username/doctorRAG/issues)
- 创建新的 [Issue](https://github.com/your-username/doctorRAG/issues/new)
- 发送邮件至：<EMAIL>
