#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户认证模块 - auth_app.py
处理用户登录、注册、权限管理等功能
"""

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
import hashlib
import json
import os
import base64
import uuid
from datetime import datetime
from werkzeug.utils import secure_filename

# 创建蓝图
auth_bp = Blueprint('auth', __name__)

# 用户数据存储
USER_DATA_FILE = 'tmp_data/users.json'
USER_ACTIVITY_FILE = 'tmp_data/user_activities.json'
USER_MESSAGES_FILE = 'tmp_data/user_messages.json'

class User:
    def __init__(self, username, password, is_admin=False, created_at=None):
        self.username = username
        self.password = self.hash_password(password)
        self.is_admin = is_admin
        self.created_at = created_at or datetime.now().isoformat()
        self.last_login = None
        self.login_count = 0
        self.email = None
        self.phone = None
        self.avatar = None
        self.bio = None
        self.preferences = {
            'theme': 'light',
            'language': 'zh-CN',
            'notifications': True,
            'email_notifications': False
        }

    @staticmethod
    def hash_password(password):
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()

    def check_password(self, password):
        """验证密码"""
        return self.password == self.hash_password(password)

    def to_dict(self):
        """转换为字典"""
        return {
            'username': self.username,
            'password': self.password,
            'is_admin': self.is_admin,
            'created_at': self.created_at,
            'last_login': self.last_login,
            'login_count': self.login_count,
            'email': self.email,
            'phone': self.phone,
            'avatar': self.avatar,
            'bio': self.bio,
            'preferences': self.preferences
        }

    @classmethod
    def from_dict(cls, data):
        """从字典创建用户"""
        user = cls.__new__(cls)
        user.username = data['username']
        user.password = data['password']
        user.is_admin = data.get('is_admin', False)
        user.created_at = data.get('created_at')
        user.last_login = data.get('last_login')
        user.login_count = data.get('login_count', 0)
        user.email = data.get('email')
        user.phone = data.get('phone')
        user.avatar = data.get('avatar')
        user.bio = data.get('bio')
        user.preferences = data.get('preferences', {
            'theme': 'light',
            'language': 'zh-CN',
            'notifications': True,
            'email_notifications': False
        })
        return user

def load_users():
    """加载用户数据"""
    if not os.path.exists(USER_DATA_FILE):
        # 创建默认管理员用户
        default_users = {
            'admin': User('admin', 'admin123', is_admin=True).to_dict()
        }
        save_users(default_users)
        return default_users

    try:
        with open(USER_DATA_FILE, 'r', encoding='utf-8') as f:
            users_data = json.load(f)

        # 数据迁移：为现有用户添加新字段
        updated = False
        for username, user_data in users_data.items():
            if 'email' not in user_data:
                user_data['email'] = None
                updated = True
            if 'phone' not in user_data:
                user_data['phone'] = None
                updated = True
            if 'avatar' not in user_data:
                user_data['avatar'] = None
                updated = True
            if 'bio' not in user_data:
                user_data['bio'] = None
                updated = True
            if 'preferences' not in user_data:
                user_data['preferences'] = {
                    'theme': 'light',
                    'language': 'zh-CN',
                    'notifications': True,
                    'email_notifications': False
                }
                updated = True

        # 如果有更新，保存数据
        if updated:
            save_users(users_data)
            print("用户数据已迁移到新格式")

        return users_data
    except Exception as e:
        print(f"加载用户数据失败: {e}")
        return {}

def save_users(users_data):
    """保存用户数据"""
    try:
        os.makedirs(os.path.dirname(USER_DATA_FILE), exist_ok=True)
        with open(USER_DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(users_data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存用户数据失败: {e}")

def get_user(username):
    """获取用户"""
    users_data = load_users()
    if username in users_data:
        return User.from_dict(users_data[username])
    return None

def create_user(username, password, is_admin=False):
    """创建用户"""
    users_data = load_users()

    if username in users_data:
        return False, "用户名已存在"

    # 验证用户名和密码
    if len(username) < 3 or len(username) > 20:
        return False, "用户名长度应为3-20个字符"

    if len(password) < 6:
        return False, "密码长度应至少6个字符"

    # 创建新用户
    new_user = User(username, password, is_admin)
    users_data[username] = new_user.to_dict()
    save_users(users_data)

    # 创建欢迎消息
    create_user_message(
        username,
        'system',
        '欢迎使用医疗智能问答系统！',
        '感谢您注册我们的系统。您可以开始提问医疗相关问题，查看知识图谱，享受智能医疗服务。',
        'gift'
    )

    # 记录注册活动
    record_user_activity(username, 'register', '用户注册账户')

    return True, "用户创建成功"

def authenticate_user(username, password):
    """用户认证"""
    user = get_user(username)
    if user and user.check_password(password):
        # 更新登录信息
        users_data = load_users()
        users_data[username]['last_login'] = datetime.now().isoformat()
        users_data[username]['login_count'] += 1
        save_users(users_data)
        return user
    return None

def get_user_statistics():
    """获取用户统计信息"""
    users_data = load_users()
    total_users = len(users_data)
    admin_users = sum(1 for user in users_data.values() if user.get('is_admin', False))

    # 最近登录用户
    recent_logins = []
    for username, user_data in users_data.items():
        if user_data.get('last_login'):
            recent_logins.append({
                'username': username,
                'last_login': user_data['last_login'],
                'login_count': user_data.get('login_count', 0)
            })

    recent_logins.sort(key=lambda x: x['last_login'], reverse=True)

    return {
        'total_users': total_users,
        'admin_users': admin_users,
        'regular_users': total_users - admin_users,
        'recent_logins': recent_logins[:10]
    }

def load_user_activities():
    """加载用户活动数据"""
    if not os.path.exists(USER_ACTIVITY_FILE):
        return {}

    try:
        with open(USER_ACTIVITY_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载用户活动数据失败: {e}")
        return {}

def save_user_activities(activities_data):
    """保存用户活动数据"""
    try:
        os.makedirs(os.path.dirname(USER_ACTIVITY_FILE), exist_ok=True)
        with open(USER_ACTIVITY_FILE, 'w', encoding='utf-8') as f:
            json.dump(activities_data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存用户活动数据失败: {e}")

def record_user_activity(username, activity_type, description, extra_data=None):
    """记录用户活动"""
    activities = load_user_activities()

    if username not in activities:
        activities[username] = []

    activity = {
        'timestamp': datetime.now().isoformat(),
        'type': activity_type,
        'description': description,
        'extra_data': extra_data or {}
    }

    activities[username].append(activity)

    # 只保留最近100条记录
    activities[username] = activities[username][-100:]

    save_user_activities(activities)

def get_user_real_statistics(username):
    """获取用户真实统计数据"""
    activities = load_user_activities()
    user_activities = activities.get(username, [])

    # 统计各类活动
    question_count = len([a for a in user_activities if a['type'] == 'question'])
    login_count = len([a for a in user_activities if a['type'] == 'login'])
    graph_view_count = len([a for a in user_activities if a['type'] == 'graph_view'])

    # 计算会话数（按天分组登录）
    login_dates = set()
    for activity in user_activities:
        if activity['type'] == 'login':
            date = activity['timestamp'][:10]  # 取日期部分
            login_dates.add(date)

    session_count = len(login_dates)

    # 最近活动（最新10条）
    recent_activities = []
    for activity in reversed(user_activities[-10:]):
        recent_activities.append({
            'date': activity['timestamp'][:10],
            'action': activity['description']
        })

    # 常用话题（从问题活动中提取）
    topics = []
    for activity in user_activities:
        if activity['type'] == 'question' and 'topic' in activity.get('extra_data', {}):
            topics.append(activity['extra_data']['topic'])

    # 统计话题频率
    topic_counts = {}
    for topic in topics:
        topic_counts[topic] = topic_counts.get(topic, 0) + 1

    favorite_topics = sorted(topic_counts.keys(), key=lambda x: topic_counts[x], reverse=True)[:3]

    return {
        'total_questions': question_count,
        'total_sessions': session_count,
        'avg_session_time': '8分钟',  # 这个需要更复杂的计算
        'favorite_topics': favorite_topics or ['感冒', '发烧', '头痛'],
        'recent_activity': recent_activities
    }

def load_user_messages():
    """加载用户消息数据"""
    if not os.path.exists(USER_MESSAGES_FILE):
        return {}

    try:
        with open(USER_MESSAGES_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载用户消息数据失败: {e}")
        return {}

def save_user_messages(messages_data):
    """保存用户消息数据"""
    try:
        os.makedirs(os.path.dirname(USER_MESSAGES_FILE), exist_ok=True)
        with open(USER_MESSAGES_FILE, 'w', encoding='utf-8') as f:
            json.dump(messages_data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存用户消息数据失败: {e}")

def create_user_message(username, message_type, title, content, icon='info'):
    """创建用户消息"""
    messages = load_user_messages()

    if username not in messages:
        messages[username] = []

    message = {
        'id': str(uuid.uuid4()),
        'type': message_type,
        'title': title,
        'content': content,
        'icon': icon,
        'timestamp': datetime.now().isoformat(),
        'read': False
    }

    messages[username].append(message)
    save_user_messages(messages)
    return message

def get_user_messages(username, message_type=None, read_status=None):
    """获取用户消息"""
    messages = load_user_messages()
    user_messages = messages.get(username, [])

    # 过滤消息
    filtered_messages = []
    for msg in user_messages:
        if message_type and msg['type'] != message_type:
            continue
        if read_status is not None and msg['read'] != read_status:
            continue
        filtered_messages.append(msg)

    # 按时间倒序排列
    filtered_messages.sort(key=lambda x: x['timestamp'], reverse=True)
    return filtered_messages

def mark_message_as_read(username, message_id):
    """标记消息为已读"""
    messages = load_user_messages()
    user_messages = messages.get(username, [])

    for msg in user_messages:
        if msg['id'] == message_id:
            msg['read'] = True
            break

    messages[username] = user_messages
    save_user_messages(messages)

def get_unread_message_count(username):
    """获取未读消息数量"""
    messages = get_user_messages(username, read_status=False)
    return len(messages)

def require_login(f):
    """登录装饰器"""
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('auth.login_page'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

def require_admin(f):
    """管理员权限装饰器"""
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('auth.login_page'))
        if not session.get('is_admin', False):
            return jsonify({'success': False, 'message': '需要管理员权限'})
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# 路由定义
@auth_bp.route('/login')
def login_page():
    """登录页面"""
    return render_template('login.html')

@auth_bp.route('/register')
def register_page():
    """注册页面"""
    return render_template('register.html')

@auth_bp.route('/profile')
def profile_page():
    """个人资料页面"""
    if 'user_id' not in session:
        return redirect(url_for('auth.login_page'))
    return render_template('profile.html')

@auth_bp.route('/settings')
def settings_page():
    """设置页面"""
    if 'user_id' not in session:
        return redirect(url_for('auth.login_page'))
    return render_template('settings.html')

@auth_bp.route('/messages')
def messages_page():
    """消息中心页面"""
    if 'user_id' not in session:
        return redirect(url_for('auth.login_page'))
    return render_template('messages.html')

@auth_bp.route('/help')
def help_page():
    """帮助中心页面"""
    return render_template('help.html')

@auth_bp.route('/api/login', methods=['POST'])
def api_login():
    """登录API"""
    data = request.get_json()
    username = data.get('username', '').strip()
    password = data.get('password', '')

    if not username or not password:
        return jsonify({
            'success': False,
            'message': '用户名和密码不能为空'
        })

    user = authenticate_user(username, password)
    if user:
        session['user_id'] = username
        session['is_admin'] = user.is_admin
        session['login_time'] = datetime.now().isoformat()

        # 记录登录活动
        record_user_activity(username, 'login', '用户登录系统')

        return jsonify({
            'success': True,
            'message': '登录成功',
            'user': {
                'username': user.username,
                'is_admin': user.is_admin
            }
        })
    else:
        return jsonify({
            'success': False,
            'message': '用户名或密码错误'
        })

@auth_bp.route('/api/register', methods=['POST'])
def api_register():
    """注册API"""
    data = request.get_json()
    username = data.get('username', '').strip()
    password = data.get('password', '')

    if not username or not password:
        return jsonify({
            'success': False,
            'message': '用户名和密码不能为空'
        })

    # 前端已经验证了密码匹配，这里不需要重复验证
    success, message = create_user(username, password)
    return jsonify({
        'success': success,
        'message': message
    })

@auth_bp.route('/api/logout', methods=['POST'])
def api_logout():
    """登出API"""
    session.clear()
    return jsonify({'success': True, 'message': '已退出登录'})

@auth_bp.route('/api/user/profile', methods=['GET'])
def api_get_profile():
    """获取用户资料API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    user = get_user(session['user_id'])
    if user:
        return jsonify({
            'success': True,
            'user': {
                'username': user.username,
                'is_admin': user.is_admin,
                'created_at': user.created_at,
                'last_login': user.last_login,
                'login_count': user.login_count
            }
        })
    else:
        return jsonify({'success': False, 'message': '用户不存在'})

@auth_bp.route('/api/user/change-password', methods=['POST'])
def api_change_password():
    """修改密码API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    data = request.get_json()
    old_password = data.get('old_password', '')
    new_password = data.get('new_password', '')

    user = get_user(session['user_id'])
    if not user:
        return jsonify({'success': False, 'message': '用户不存在'})

    if not user.check_password(old_password):
        return jsonify({'success': False, 'message': '原密码错误'})

    if len(new_password) < 6:
        return jsonify({'success': False, 'message': '新密码长度应至少6个字符'})

    # 更新密码
    users_data = load_users()
    users_data[session['user_id']]['password'] = User.hash_password(new_password)
    save_users(users_data)

    return jsonify({'success': True, 'message': '密码修改成功'})

@auth_bp.route('/api/user/update-profile', methods=['POST'])
def api_update_profile():
    """更新用户资料API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    data = request.get_json()
    email = data.get('email', '').strip()
    phone = data.get('phone', '').strip()
    bio = data.get('bio', '').strip()

    # 更新用户资料
    users_data = load_users()
    if session['user_id'] in users_data:
        users_data[session['user_id']]['email'] = email
        users_data[session['user_id']]['phone'] = phone
        users_data[session['user_id']]['bio'] = bio
        save_users(users_data)
        return jsonify({'success': True, 'message': '资料更新成功'})
    else:
        return jsonify({'success': False, 'message': '用户不存在'})

@auth_bp.route('/api/user/update-preferences', methods=['POST'])
def api_update_preferences():
    """更新用户偏好设置API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    data = request.get_json()
    preferences = data.get('preferences', {})

    # 更新用户偏好
    users_data = load_users()
    if session['user_id'] in users_data:
        if 'preferences' not in users_data[session['user_id']]:
            users_data[session['user_id']]['preferences'] = {}
        users_data[session['user_id']]['preferences'].update(preferences)
        save_users(users_data)
        return jsonify({'success': True, 'message': '设置更新成功'})
    else:
        return jsonify({'success': False, 'message': '用户不存在'})

@auth_bp.route('/api/user/statistics', methods=['GET'])
def api_get_user_statistics():
    """获取用户统计信息API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    user = get_user(session['user_id'])
    if user:
        # 获取真实统计数据
        stats = get_user_real_statistics(session['user_id'])
        return jsonify({'success': True, 'statistics': stats})
    else:
        return jsonify({'success': False, 'message': '用户不存在'})

@auth_bp.route('/api/user/upload-avatar', methods=['POST'])
def api_upload_avatar():
    """上传头像API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        # 获取base64图片数据
        data = request.get_json()
        avatar_data = data.get('avatar_data', '')

        if not avatar_data:
            return jsonify({'success': False, 'message': '请选择头像图片'})

        # 解析base64数据
        if ',' in avatar_data:
            header, encoded = avatar_data.split(',', 1)
        else:
            encoded = avatar_data

        # 生成唯一文件名
        avatar_filename = f"avatar_{session['user_id']}_{uuid.uuid4().hex[:8]}.jpg"
        avatar_dir = 'static/uploads/avatars'

        # 确保目录存在
        os.makedirs(avatar_dir, exist_ok=True)

        # 保存头像文件
        avatar_path = os.path.join(avatar_dir, avatar_filename)
        with open(avatar_path, 'wb') as f:
            f.write(base64.b64decode(encoded))

        # 更新用户数据
        users_data = load_users()
        if session['user_id'] in users_data:
            # 删除旧头像文件
            old_avatar = users_data[session['user_id']].get('avatar')
            if old_avatar and os.path.exists(old_avatar):
                try:
                    os.remove(old_avatar)
                except:
                    pass

            # 保存新头像路径
            users_data[session['user_id']]['avatar'] = avatar_path
            save_users(users_data)

            return jsonify({
                'success': True,
                'message': '头像上传成功',
                'avatar_url': f'/{avatar_path}'
            })
        else:
            return jsonify({'success': False, 'message': '用户不存在'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'上传失败: {str(e)}'})

@auth_bp.route('/api/user/messages', methods=['GET'])
def api_get_user_messages():
    """获取用户消息API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        message_type = request.args.get('type')
        read_status = request.args.get('read')

        if read_status is not None:
            read_status = read_status.lower() == 'true'

        messages = get_user_messages(session['user_id'], message_type, read_status)
        unread_count = get_unread_message_count(session['user_id'])

        return jsonify({
            'success': True,
            'messages': messages,
            'unread_count': unread_count
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@auth_bp.route('/api/user/messages/<message_id>/read', methods=['POST'])
def api_mark_message_read(message_id):
    """标记消息为已读API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        mark_message_as_read(session['user_id'], message_id)
        unread_count = get_unread_message_count(session['user_id'])

        return jsonify({
            'success': True,
            'message': '消息已标记为已读',
            'unread_count': unread_count
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@auth_bp.route('/api/user/messages/mark-all-read', methods=['POST'])
def api_mark_all_messages_read():
    """标记所有消息为已读API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        messages = load_user_messages()
        user_messages = messages.get(session['user_id'], [])

        for msg in user_messages:
            msg['read'] = True

        messages[session['user_id']] = user_messages
        save_user_messages(messages)

        return jsonify({
            'success': True,
            'message': '所有消息已标记为已读',
            'unread_count': 0
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@auth_bp.route('/api/admin/users', methods=['GET'])
def api_get_users():
    """获取用户列表API（管理员）"""
    if 'user_id' not in session or not session.get('is_admin'):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    users_data = load_users()
    users_list = []

    for username, user_data in users_data.items():
        users_list.append({
            'username': username,
            'is_admin': user_data.get('is_admin', False),
            'created_at': user_data.get('created_at'),
            'last_login': user_data.get('last_login'),
            'login_count': user_data.get('login_count', 0)
        })

    return jsonify({'success': True, 'users': users_list})

@auth_bp.route('/api/admin/users/<username>/toggle-admin', methods=['POST'])
def api_toggle_admin(username):
    """切换用户管理员权限API"""
    if 'user_id' not in session or not session.get('is_admin'):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    if username == session['user_id']:
        return jsonify({'success': False, 'message': '不能修改自己的权限'})

    users_data = load_users()
    if username not in users_data:
        return jsonify({'success': False, 'message': '用户不存在'})

    users_data[username]['is_admin'] = not users_data[username].get('is_admin', False)
    save_users(users_data)

    action = "授予" if users_data[username]['is_admin'] else "撤销"
    return jsonify({'success': True, 'message': f'已{action}用户 {username} 的管理员权限'})

@auth_bp.route('/api/admin/statistics', methods=['GET'])
def api_get_admin_statistics():
    """获取管理员统计信息API"""
    if 'user_id' not in session or not session.get('is_admin'):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    stats = get_user_statistics()
    return jsonify({'success': True, 'statistics': stats})
