#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗智能问答系统 - 系统状态检查脚本
快速检查系统各组件状态
"""

import os
import sys

def check_files():
    """检查关键文件"""
    print("🔍 检查关键文件...")
    
    files = {
        "主应用": "app.py",
        "认证模块": "apps/auth_app.py", 
        "聊天模块": "apps/chat_app.py",
        "知识图谱模块": "apps/knowledge_graph_app.py",
        "管理模块": "apps/admin_app.py",
        "BERT模型": "model/best_roberta_rnn_model_ent_aug.pt",
        "标签映射": "tmp_data/tag2idx.npy",
        "主页模板": "templates/index.html",
        "登录模板": "templates/login.html",
        "聊天模板": "templates/chat.html"
    }
    
    missing = []
    for name, path in files.items():
        if os.path.exists(path):
            if path.endswith('.pt'):
                size = os.path.getsize(path) / (1024*1024)
                print(f"  ✅ {name}: {path} ({size:.1f}MB)")
            else:
                print(f"  ✅ {name}: {path}")
        else:
            print(f"  ❌ {name}: {path} (缺失)")
            missing.append(name)
    
    return len(missing) == 0, missing

def check_imports():
    """检查关键导入"""
    print("\n📦 检查关键导入...")
    
    imports = {
        "Flask": "flask",
        "PyTorch": "torch", 
        "Transformers": "transformers",
        "Neo4j": "py2neo",
        "Scikit-learn": "sklearn"
    }
    
    missing = []
    for name, module in imports.items():
        try:
            __import__(module)
            print(f"  ✅ {name}")
        except ImportError:
            print(f"  ❌ {name} (未安装)")
            missing.append(name)
    
    return len(missing) == 0, missing

def check_app_structure():
    """检查应用结构"""
    print("\n🏗️ 检查应用结构...")
    
    try:
        from app import app
        blueprints = list(app.blueprints.keys())
        print(f"  ✅ 主应用加载成功")
        print(f"  ✅ 注册蓝图: {', '.join(blueprints)}")
        
        routes = [rule.rule for rule in app.url_map.iter_rules() if rule.endpoint != 'static']
        print(f"  ✅ 可用路由: {len(routes)} 个")
        
        return True
    except Exception as e:
        print(f"  ❌ 应用结构检查失败: {e}")
        return False

def check_neo4j():
    """检查Neo4j连接"""
    print("\n🗄️ 检查Neo4j连接...")
    
    try:
        import py2neo
        client = py2neo.Graph(
            'bolt://localhost:7687',
            user='neo4j', 
            password='wangqi20040401',
            name='doctor'
        )
        
        result = client.run("RETURN 1").data()
        if result:
            count_result = client.run("MATCH (n) RETURN count(n) as total").data()
            total = count_result[0]['total'] if count_result else 0
            print(f"  ✅ Neo4j连接成功 ({total} 个节点)")
            return True
        else:
            print("  ❌ Neo4j连接失败")
            return False
    except Exception as e:
        print(f"  ❌ Neo4j连接错误: {e}")
        return False

def main():
    """主检查函数"""
    print("=" * 50)
    print("🏥 医疗智能问答系统 - 状态检查")
    print("=" * 50)
    
    checks = [
        ("文件检查", check_files),
        ("导入检查", check_imports), 
        ("应用结构", check_app_structure),
        ("数据库连接", check_neo4j)
    ]
    
    results = []
    for name, check_func in checks:
        try:
            result = check_func()
            if isinstance(result, tuple):
                success, details = result
                results.append((name, success, details))
            else:
                results.append((name, result, None))
        except Exception as e:
            print(f"  ❌ {name}检查异常: {e}")
            results.append((name, False, str(e)))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 检查结果总结")
    print("=" * 50)
    
    passed = 0
    for name, success, details in results:
        if success:
            print(f"✅ {name}: 正常")
            passed += 1
        else:
            print(f"❌ {name}: 异常")
            if details:
                if isinstance(details, list):
                    print(f"   缺失: {', '.join(details)}")
                else:
                    print(f"   详情: {details}")
    
    total = len(results)
    print(f"\n📈 总体状态: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("\n🎉 系统状态良好，可以正常启动！")
        print("💡 启动命令: python app.py")
    elif passed >= total - 1:
        print("\n⚠️ 系统基本正常，可以尝试启动")
        print("💡 启动命令: python app.py")
    else:
        print("\n🚨 系统存在问题，建议修复后再启动")
        print("💡 修复建议:")
        print("   1. 安装缺失的依赖: pip install -r requirements_minimal.txt")
        print("   2. 检查Neo4j服务是否运行")
        print("   3. 确保模型文件存在")
    
    return passed >= total - 1

if __name__ == '__main__':
    main()
