{% extends "base.html" %}

{% block title %}首页 - 医疗智能问答系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Hero Section -->
    <section class="hero-section py-5 mb-5" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">医疗智能问答系统</h1>
                    <p class="lead mb-4">基于RAG与大模型技术，为您提供专业、准确的医疗咨询服务</p>
                    <div class="d-flex gap-3">
                        <a href="{{ url_for('chat.chat_page') }}" class="btn btn-light btn-lg">
                            <i class="fas fa-comments me-2"></i>开始问答
                        </a>
                        <a href="{{ url_for('knowledge_graph.knowledge_graph_page') }}" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-project-diagram me-2"></i>知识图谱
                        </a>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <i class="fas fa-heartbeat" style="font-size: 15rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section py-5">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="display-5 fw-bold mb-3">系统特色</h2>
                    <p class="lead text-muted">先进的AI技术，专业的医疗知识</p>
                </div>
            </div>

            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-brain fa-3x text-primary"></i>
                            </div>
                            <h5 class="card-title">智能问答</h5>
                            <p class="card-text">基于大语言模型的智能问答，理解复杂医疗问题，提供准确回答</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-project-diagram fa-3x text-success"></i>
                            </div>
                            <h5 class="card-title">知识图谱</h5>
                            <p class="card-text">构建完整的医疗知识图谱，包含疾病、药品、症状等多维度信息</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-search fa-3x text-warning"></i>
                            </div>
                            <h5 class="card-title">精准检索</h5>
                            <p class="card-text">RAG技术结合实体识别，精准检索相关医疗知识</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="stats-section py-5 bg-light">
        <div class="container">
            <div class="row text-center">
                <div class="col-md-3">
                    <div class="stat-item">
                        <h3 class="display-4 fw-bold text-primary">4.4万+</h3>
                        <p class="text-muted">医疗实体</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <h3 class="display-4 fw-bold text-success">31万+</h3>
                        <p class="text-muted">知识关系</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <h3 class="display-4 fw-bold text-warning">8类</h3>
                        <p class="text-muted">实体类型</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <h3 class="display-4 fw-bold text-info">97.4%</h3>
                        <p class="text-muted">识别准确率</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- How it works Section -->
    <section class="how-it-works py-5">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="display-5 fw-bold mb-3">工作原理</h2>
                    <p class="lead text-muted">了解系统如何为您提供专业医疗咨询</p>
                </div>
            </div>

            <div class="row g-4">
                <div class="col-md-3 text-center">
                    <div class="step-item">
                        <div class="step-number mb-3">
                            <span class="badge bg-primary rounded-circle p-3 fs-4">1</span>
                        </div>
                        <h5>提出问题</h5>
                        <p class="text-muted">用户输入医疗相关问题</p>
                    </div>
                </div>

                <div class="col-md-3 text-center">
                    <div class="step-item">
                        <div class="step-number mb-3">
                            <span class="badge bg-success rounded-circle p-3 fs-4">2</span>
                        </div>
                        <h5>实体识别</h5>
                        <p class="text-muted">AI识别问题中的医疗实体</p>
                    </div>
                </div>

                <div class="col-md-3 text-center">
                    <div class="step-item">
                        <div class="step-number mb-3">
                            <span class="badge bg-warning rounded-circle p-3 fs-4">3</span>
                        </div>
                        <h5>知识检索</h5>
                        <p class="text-muted">从知识图谱中检索相关信息</p>
                    </div>
                </div>

                <div class="col-md-3 text-center">
                    <div class="step-item">
                        <div class="step-number mb-3">
                            <span class="badge bg-info rounded-circle p-3 fs-4">4</span>
                        </div>
                        <h5>生成回答</h5>
                        <p class="text-muted">大模型生成专业回答</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Start Section -->
    <section class="quick-start py-5 bg-primary text-white">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h3 class="mb-3">准备开始使用？</h3>
                    <p class="mb-0">立即体验我们的医疗智能问答系统，获得专业的医疗咨询服务</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <a href="{{ url_for('chat.chat_page') }}" class="btn btn-light btn-lg">
                        <i class="fas fa-rocket me-2"></i>立即开始
                    </a>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.hero-section {
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.feature-icon {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-item h3 {
    margin-bottom: 0.5rem;
}

.step-item {
    padding: 1rem;
}

.step-number .badge {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card {
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}
</style>
{% endblock %}
