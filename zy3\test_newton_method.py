#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试牛顿法算法
"""

import numpy as np

def test_newton_method():
    """测试牛顿法"""
    
    # 目标函数
    def f(x):
        x1, x2 = x[0], x[1]
        return 4*(x1 + 1)**2 + 2*(x2 - 1)**2 + x1 + x2 + 10
    
    # 梯度函数
    def grad(x):
        x1, x2 = x[0], x[1]
        return np.array([8*x1 + 9, 4*x2 - 3])
    
    # Hessian矩阵
    def hessian(x):
        return np.array([[8, 0], [0, 4]])
    
    # 初始化
    x = np.array([0.0, 0.0])
    epsilon = 1e-5
    
    print("牛顿法求解测试:")
    print("=" * 60)
    print(f"{'k':<3} {'x1':<10} {'x2':<10} {'f(x)':<12} {'||grad||':<12}")
    print("-" * 60)
    
    for k in range(10):  # 最多10次迭代
        g = grad(x)
        gn = np.linalg.norm(g)
        fval = f(x)
        
        print(f"{k:<3} {x[0]:<10.6f} {x[1]:<10.6f} {fval:<12.6f} {gn:<12.6e}")
        
        if gn < epsilon:
            print("收敛!")
            break
        
        # 牛顿方向
        H = hessian(x)
        d = np.linalg.solve(H, -g)
        
        # 更新
        x = x + d
    
    print("-" * 60)
    print(f"最优解: x* = ({x[0]:.8f}, {x[1]:.8f})")
    print(f"最优值: f* = {f(x):.8f}")
    print(f"理论解: x* = (-1.12500000, 0.75000000)")
    
    # 验证理论最优值
    x_theory = np.array([-1.125, 0.75])
    f_theory = f(x_theory)
    print(f"理论值: f* = {f_theory:.8f}")
    
    # 计算误差
    error = np.linalg.norm(x - x_theory)
    print(f"误差: {error:.8e}")

def demonstrate_quadratic_convergence():
    """演示二次收敛性"""
    print("\n" + "=" * 60)
    print("二次收敛性演示:")
    print("=" * 60)
    
    # 理论最优解
    x_star = np.array([-1.125, 0.75])
    
    # 不同初始点测试
    initial_points = [
        [0.0, 0.0],
        [1.0, 1.0],
        [-2.0, 2.0],
        [0.5, -0.5]
    ]
    
    def f(x):
        x1, x2 = x[0], x[1]
        return 4*(x1 + 1)**2 + 2*(x2 - 1)**2 + x1 + x2 + 10
    
    def grad(x):
        x1, x2 = x[0], x[1]
        return np.array([8*x1 + 9, 4*x2 - 3])
    
    def hessian(x):
        return np.array([[8, 0], [0, 4]])
    
    for i, x0 in enumerate(initial_points):
        print(f"\n初始点 {i+1}: x0 = {x0}")
        x = np.array(x0, dtype=float)
        
        for k in range(3):
            error = np.linalg.norm(x - x_star)
            print(f"  迭代 {k}: ||x - x*|| = {error:.6e}")
            
            if error < 1e-12:
                print("  已达到机器精度")
                break
            
            g = grad(x)
            if np.linalg.norm(g) < 1e-12:
                print("  梯度为零，已收敛")
                break
            
            H = hessian(x)
            d = np.linalg.solve(H, -g)
            x = x + d

def compare_methods():
    """比较不同方法"""
    print("\n" + "=" * 60)
    print("方法比较:")
    print("=" * 60)
    
    print("对于二次函数 f(x) = 4(x1+1)² + 2(x2-1)² + x1 + x2 + 10:")
    print()
    print("牛顿法:")
    print("  - 迭代次数: 1次")
    print("  - 收敛速度: 二次收敛")
    print("  - 计算量: O(n³) 每次迭代")
    print("  - 内存: O(n²)")
    print("  - 优点: 快速收敛，精确解")
    print("  - 缺点: 需要计算Hessian矩阵")
    print()
    print("最速下降法:")
    print("  - 迭代次数: 5-6次")
    print("  - 收敛速度: 线性收敛")
    print("  - 计算量: O(n) 每次迭代")
    print("  - 内存: O(n)")
    print("  - 优点: 简单，内存少")
    print("  - 缺点: 收敛较慢")

if __name__ == "__main__":
    test_newton_method()
    demonstrate_quadratic_convergence()
    compare_methods()
