#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外罚函数法求解约束优化问题
问题：min f(x) = x1^2 + 4x2^2 - 2x1 - x2
约束：x1 + x2 <= 1
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
from scipy.optimize import minimize
import pandas as pd

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei']
rcParams['axes.unicode_minus'] = False

class ExteriorPenaltyMethodSolver:
    """外罚函数法求解器"""
    
    def __init__(self, tolerance=1e-6, max_outer_iterations=20):
        """
        初始化求解器
        
        Parameters:
        -----------
        tolerance : float
            收敛容差
        max_outer_iterations : int
            最大外层迭代次数
        """
        self.tolerance = tolerance
        self.max_outer_iterations = max_outer_iterations
        self.history = []
        
    def objective_function(self, x):
        """
        原目标函数 f(x) = x1^2 + 4x2^2 - 2x1 - x2
        
        Parameters:
        -----------
        x : numpy.ndarray
            输入向量 [x1, x2]
            
        Returns:
        --------
        float
            函数值
        """
        return x[0]**2 + 4*x[1]**2 - 2*x[0] - x[1]
    
    def constraint_function(self, x):
        """
        约束函数 g(x) = x1 + x2 - 1
        
        Parameters:
        -----------
        x : numpy.ndarray
            输入向量 [x1, x2]
            
        Returns:
        --------
        float
            约束函数值（> 0 表示违反约束）
        """
        return x[0] + x[1] - 1
    
    def penalty_function(self, x, r):
        """
        外罚函数 P(x, r) = f(x) + r * max(0, g(x))^2
        
        Parameters:
        -----------
        x : numpy.ndarray
            输入向量
        r : float
            罚参数
            
        Returns:
        --------
        float
            罚函数值
        """
        f_val = self.objective_function(x)
        g_val = self.constraint_function(x)
        penalty_term = r * max(0, g_val)**2
        return f_val + penalty_term
    
    def penalty_gradient(self, x, r):
        """
        罚函数的梯度
        
        Parameters:
        -----------
        x : numpy.ndarray
            输入向量
        r : float
            罚参数
            
        Returns:
        --------
        numpy.ndarray
            梯度向量
        """
        # 原函数梯度: ∇f = [2x1 - 2, 8x2 - 1]
        grad_f = np.array([2*x[0] - 2, 8*x[1] - 1])
        
        # 约束函数梯度: ∇g = [1, 1]
        grad_g = np.array([1, 1])
        
        # 约束函数值
        g_val = self.constraint_function(x)
        
        # 罚函数梯度
        if g_val > 0:
            grad_penalty = grad_f + 2 * r * g_val * grad_g
        else:
            grad_penalty = grad_f
            
        return grad_penalty
    
    def solve_unconstrained_subproblem(self, x0, r):
        """
        求解无约束子问题
        
        Parameters:
        -----------
        x0 : numpy.ndarray
            初始点
        r : float
            罚参数
            
        Returns:
        --------
        dict
            求解结果
        """
        # 定义目标函数
        def objective(x):
            return self.penalty_function(x, r)
        
        def gradient(x):
            return self.penalty_gradient(x, r)
        
        # 使用scipy的优化器求解
        result = minimize(objective, x0, method='BFGS', jac=gradient, 
                         options={'gtol': self.tolerance/10})
        
        return {
            'x': result.x,
            'f_val': self.objective_function(result.x),
            'penalty_val': result.fun,
            'constraint_val': self.constraint_function(result.x),
            'success': result.success,
            'nit': result.nit
        }
    
    def is_feasible(self, x):
        """检查点是否可行"""
        return self.constraint_function(x) <= self.tolerance
    
    def solve(self, x0, r0=1.0, r_factor=10.0):
        """
        外罚函数法主算法
        
        Parameters:
        -----------
        x0 : numpy.ndarray
            初始点
        r0 : float
            初始罚参数
        r_factor : float
            罚参数增长因子
            
        Returns:
        --------
        dict
            求解结果
        """
        print("=" * 80)
        print("外罚函数法求解约束优化问题")
        print("=" * 80)
        print("目标函数: f(x) = x1² + 4x2² - 2x1 - x2")
        print("约束条件: x1 + x2 ≤ 1")
        print(f"初始点: x0 = {x0}")
        print(f"初始罚参数: r0 = {r0}")
        print(f"罚参数增长因子: {r_factor}")
        print("-" * 80)
        print(f"{'k':<3} {'r':<10} {'x1':<10} {'x2':<10} {'f(x)':<12} {'g(x)':<12} {'P(x,r)':<12} {'可行性':<8}")
        print("-" * 80)
        
        x = np.array(x0, dtype=float)
        r = r0
        self.history = []
        
        for k in range(self.max_outer_iterations):
            # 求解无约束子问题
            result = self.solve_unconstrained_subproblem(x, r)
            
            if not result['success']:
                print(f"第{k}次迭代：子问题求解失败")
                break
            
            x_new = result['x']
            f_val = result['f_val']
            g_val = result['constraint_val']
            p_val = result['penalty_val']
            is_feas = self.is_feasible(x_new)
            
            # 记录历史
            self.history.append({
                'iteration': k,
                'r': r,
                'x': x_new.copy(),
                'f_value': f_val,
                'constraint_value': g_val,
                'penalty_value': p_val,
                'feasible': is_feas,
                'inner_iterations': result['nit']
            })
            
            print(f"{k:<3} {r:<10.2e} {x_new[0]:<10.6f} {x_new[1]:<10.6f} "
                  f"{f_val:<12.6f} {g_val:<12.6e} {p_val:<12.6f} {'是' if is_feas else '否':<8}")
            
            # 检查收敛
            if abs(g_val) < self.tolerance or (is_feas and abs(g_val) < self.tolerance):
                print(f"算法在第{k}次迭代收敛")
                break
            
            # 更新罚参数和迭代点
            x = x_new
            r *= r_factor
        
        print("-" * 80)
        print("求解结果:")
        final_x = self.history[-1]['x'] if self.history else x
        final_f = self.objective_function(final_x)
        final_g = self.constraint_function(final_x)
        
        print(f"最优解: x* = [{final_x[0]:.6f}, {final_x[1]:.6f}]")
        print(f"最优值: f* = {final_f:.6f}")
        print(f"约束值: g(x*) = {final_g:.6e}")
        print(f"可行性: {'是' if self.is_feasible(final_x) else '否'}")
        print(f"总迭代次数: {len(self.history)}")
        
        return {
            'optimal_point': final_x,
            'optimal_value': final_f,
            'constraint_value': final_g,
            'feasible': self.is_feasible(final_x),
            'iterations': len(self.history),
            'history': self.history
        }
    
    def solve_analytical(self):
        """解析求解（用于对比）"""
        print("\n" + "=" * 60)
        print("解析解（对比）")
        print("=" * 60)
        
        print("无约束最优解:")
        print("∇f = [2x1 - 2, 8x2 - 1] = 0")
        print("解得: x1 = 1, x2 = 1/8 = 0.125")
        x_unconstrained = np.array([1.0, 0.125])
        f_unconstrained = self.objective_function(x_unconstrained)
        g_unconstrained = self.constraint_function(x_unconstrained)
        
        print(f"无约束最优解: x = [{x_unconstrained[0]:.6f}, {x_unconstrained[1]:.6f}]")
        print(f"目标函数值: f = {f_unconstrained:.6f}")
        print(f"约束函数值: g = {g_unconstrained:.6f}")
        print(f"约束满足: {'是' if g_unconstrained <= 0 else '否'}")
        
        if g_unconstrained <= 0:
            print("无约束最优解满足约束，即为约束问题的最优解")
            return x_unconstrained, f_unconstrained
        else:
            print("\n约束起作用，使用拉格朗日乘数法:")
            print("L = x1² + 4x2² - 2x1 - x2 + λ(x1 + x2 - 1)")
            print("KKT条件:")
            print("∇L = [2x1 - 2 + λ, 8x2 - 1 + λ] = 0")
            print("x1 + x2 = 1 (约束起作用)")
            print("λ ≥ 0")
            
            print("\n求解:")
            print("2x1 - 2 + λ = 0  =>  x1 = 1 - λ/2")
            print("8x2 - 1 + λ = 0  =>  x2 = (1 - λ)/8")
            print("x1 + x2 = 1  =>  (1 - λ/2) + (1 - λ)/8 = 1")
            print("解得: λ = 8/5 = 1.6")
            
            lambda_val = 8/5
            x1_constrained = 1 - lambda_val/2
            x2_constrained = (1 - lambda_val)/8
            x_constrained = np.array([x1_constrained, x2_constrained])
            f_constrained = self.objective_function(x_constrained)
            
            print(f"约束最优解: x* = [{x1_constrained:.6f}, {x2_constrained:.6f}]")
            print(f"最优值: f* = {f_constrained:.6f}")
            print(f"拉格朗日乘数: λ* = {lambda_val:.6f}")
            
            return x_constrained, f_constrained
    
    def plot_convergence(self, save_path=None):
        """绘制收敛过程"""
        if not self.history:
            print("没有迭代历史数据")
            return
        
        iterations = [h['iteration'] for h in self.history]
        f_values = [h['f_value'] for h in self.history]
        constraint_values = [abs(h['constraint_value']) for h in self.history]
        penalty_values = [h['penalty_value'] for h in self.history]
        r_values = [h['r'] for h in self.history]
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
        
        # 目标函数值
        ax1.plot(iterations, f_values, 'b-o', markersize=4)
        ax1.set_xlabel('迭代次数')
        ax1.set_ylabel('目标函数值 f(x)')
        ax1.set_title('目标函数值变化')
        ax1.grid(True, alpha=0.3)
        
        # 约束违反量
        ax2.semilogy(iterations, constraint_values, 'r-s', markersize=4)
        ax2.set_xlabel('迭代次数')
        ax2.set_ylabel('约束违反量 |g(x)|')
        ax2.set_title('约束违反量变化')
        ax2.grid(True, alpha=0.3)
        
        # 罚函数值
        ax3.plot(iterations, penalty_values, 'g-^', markersize=4)
        ax3.set_xlabel('迭代次数')
        ax3.set_ylabel('罚函数值 P(x,r)')
        ax3.set_title('罚函数值变化')
        ax3.grid(True, alpha=0.3)
        
        # 罚参数
        ax4.semilogy(iterations, r_values, 'm-d', markersize=4)
        ax4.set_xlabel('迭代次数')
        ax4.set_ylabel('罚参数 r')
        ax4.set_title('罚参数变化')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"收敛图已保存到: {save_path}")
        
        plt.show()
    
    def plot_contour(self, save_path=None):
        """绘制等高线图和迭代路径"""
        if not self.history:
            print("没有迭代历史数据")
            return
        
        # 创建网格
        x1_range = np.linspace(-0.5, 2.5, 100)
        x2_range = np.linspace(-0.5, 1.5, 100)
        X1, X2 = np.meshgrid(x1_range, x2_range)
        
        # 计算目标函数值
        Z = X1**2 + 4*X2**2 - 2*X1 - X2
        
        # 绘制等高线
        plt.figure(figsize=(10, 8))
        contour = plt.contour(X1, X2, Z, levels=20, alpha=0.6)
        plt.clabel(contour, inline=True, fontsize=8)
        
        # 绘制约束边界
        x1_constraint = np.linspace(-0.5, 2.5, 100)
        x2_constraint = 1 - x1_constraint
        plt.plot(x1_constraint, x2_constraint, 'r-', linewidth=2, label='约束边界: x1 + x2 = 1')
        
        # 填充可行域
        plt.fill_between(x1_constraint, x2_constraint, -0.5, alpha=0.2, color='green', label='可行域')
        
        # 绘制迭代路径
        x_path = [h['x'] for h in self.history]
        x1_path = [x[0] for x in x_path]
        x2_path = [x[1] for x in x_path]
        
        plt.plot(x1_path, x2_path, 'bo-', markersize=6, linewidth=2, label='迭代路径')
        plt.plot(x1_path[0], x2_path[0], 'go', markersize=8, label='起始点')
        plt.plot(x1_path[-1], x2_path[-1], 'ro', markersize=8, label='最终点')
        
        # 标记解析解
        x_analytical, _ = self.solve_analytical()
        plt.plot(x_analytical[0], x_analytical[1], 'ks', markersize=10, label='解析解')
        
        plt.xlabel('x1')
        plt.ylabel('x2')
        plt.title('外罚函数法迭代过程')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.axis('equal')
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"等高线图已保存到: {save_path}")
        
        plt.show()
    
    def export_results_to_csv(self, filename):
        """导出结果到CSV文件"""
        if not self.history:
            print("没有迭代历史数据")
            return
        
        data = []
        for h in self.history:
            data.append({
                '迭代次数': h['iteration'],
                '罚参数': h['r'],
                'x1': h['x'][0],
                'x2': h['x'][1],
                '目标函数值': h['f_value'],
                '约束函数值': h['constraint_value'],
                '罚函数值': h['penalty_value'],
                '可行性': h['feasible'],
                '内层迭代次数': h['inner_iterations']
            })
        
        df = pd.DataFrame(data)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"结果已导出到: {filename}")

def main():
    """主函数"""
    # 初始化参数
    x0 = np.array([0.0, 0.0])  # 初始点
    
    # 创建求解器
    solver = ExteriorPenaltyMethodSolver(tolerance=1e-6, max_outer_iterations=10)
    
    # 求解问题
    result = solver.solve(x0, r0=1.0, r_factor=10.0)
    
    # 解析解对比
    solver.solve_analytical()
    
    # 绘制收敛过程
    solver.plot_convergence('zy7/penalty_convergence.png')
    
    # 绘制等高线图
    solver.plot_contour('zy7/penalty_contour.png')
    
    # 导出结果
    solver.export_results_to_csv('zy7/penalty_results.csv')

if __name__ == "__main__":
    main()
