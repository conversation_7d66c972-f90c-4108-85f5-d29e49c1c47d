#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单纯形法测试脚本
"""

import numpy as np
import time

def objective_function(x):
    """目标函数 f(x) = 2x1 + 3x2 + x3"""
    return 2*x[0] + 3*x[1] + x[2]

def check_feasibility(x, tolerance=1e-6):
    """检查解的可行性"""
    # 约束1: -x1 + 4x2 + 2x3 >= 8
    constraint1 = -x[0] + 4*x[1] + 2*x[2] >= 8 - tolerance
    
    # 约束2: 3x1 + 2x2 >= 6
    constraint2 = 3*x[0] + 2*x[1] >= 6 - tolerance
    
    # 非负约束
    non_negative = np.all(x >= -tolerance)
    
    return constraint1 and constraint2 and non_negative

def solve_with_scipy():
    """使用scipy求解线性规划"""
    try:
        from scipy.optimize import linprog
        
        print("使用scipy.optimize.linprog求解:")
        print("=" * 50)
        
        # 标准形式
        c = np.array([2, 3, 1])
        A_ub = np.array([
            [1, -4, -2],   # x1 - 4x2 - 2x3 <= -8
            [-3, -2, 0]    # -3x1 - 2x2 <= -6
        ])
        b_ub = np.array([-8, -6])
        bounds = [(0, None), (0, None), (0, None)]
        
        start_time = time.time()
        result = linprog(c, A_ub=A_ub, b_ub=b_ub, bounds=bounds, method='highs')
        end_time = time.time()
        
        if result.success:
            print(f"最优解: x* = [{result.x[0]:.6f}, {result.x[1]:.6f}, {result.x[2]:.6f}]")
            print(f"最优值: f* = {result.fun:.6f}")
            print(f"求解时间: {end_time - start_time:.6f} 秒")
            print(f"迭代次数: {getattr(result, 'nit', 'N/A')}")
            print("求解成功")
            
            # 验证约束
            x_opt = result.x
            print(f"\n约束验证:")
            print(f"约束1: -x1 + 4x2 + 2x3 = {-x_opt[0] + 4*x_opt[1] + 2*x_opt[2]:.6f} >= 8")
            print(f"约束2: 3x1 + 2x2 = {3*x_opt[0] + 2*x_opt[1]:.6f} >= 6")
            print(f"约束3-5: x1={x_opt[0]:.6f}, x2={x_opt[1]:.6f}, x3={x_opt[2]:.6f} >= 0")
            print(f"可行性: {'是' if check_feasibility(x_opt) else '否'}")
            
            return {
                'optimal_point': result.x,
                'optimal_value': result.fun,
                'time': end_time - start_time,
                'iterations': getattr(result, 'nit', None),
                'success': True
            }
        else:
            print("scipy求解失败:", result.message)
            return {'success': False}
            
    except ImportError:
        print("scipy未安装，无法进行单纯形法求解")
        return {'success': False}

def manual_simplex_demonstration():
    """手工单纯形法演示"""
    print("\n" + "=" * 60)
    print("手工单纯形法演示")
    print("=" * 60)
    
    print("原问题:")
    print("  min 2x1 + 3x2 + x3")
    print("  s.t. -x1 + 4x2 + 2x3 >= 8")
    print("       3x1 + 2x2 >= 6")
    print("       xi >= 0, i = 1,2,3")
    
    print("\n标准形式转换:")
    print("  min 2x1 + 3x2 + x3")
    print("  s.t. x1 - 4x2 - 2x3 + s1 = -8  (约束1乘以-1)")
    print("       -3x1 - 2x2 + s2 = -6      (约束2乘以-1)")
    print("       xi >= 0, si >= 0")
    
    print("\n注意: 右端向量为负，需要两阶段法或大M法")
    
    print("\n初始单纯形表:")
    print("  基变量 |  x1   x2   x3   s1   s2 | RHS")
    print("  -------|-------------------------|----")
    print("    s1   |   1   -4   -2    1    0 |  -8")
    print("    s2   |  -3   -2    0    0    1 |  -6")
    print("    z    |  -2   -3   -1    0    0 |   0")
    
    print("\n问题分析:")
    print("  - 初始基解不可行（RHS有负值）")
    print("  - 需要使用两阶段法找到初始可行解")
    print("  - 第一阶段：引入人工变量，最小化人工变量之和")
    print("  - 第二阶段：在可行域内优化原目标函数")

def simple_subgradient_method(max_iterations=30):
    """简化的次梯度法实现"""
    print("\n" + "=" * 60)
    print("次梯度法求解（对比）")
    print("=" * 60)
    
    x = np.array([1.0, 1.0, 1.0])  # 初始点
    lambda_vec = np.array([1.0, 1.0, 0.0, 0.0, 0.0])  # 初始乘子
    
    print(f"初始点: x0 = {x}")
    print(f"初始乘子: λ0 = {lambda_vec}")
    print("-" * 60)
    print(f"{'k':<3} {'x1':<8} {'x2':<8} {'x3':<8} {'f(x)':<10} {'可行性':<8}")
    print("-" * 60)
    
    best_feasible_x = None
    best_feasible_value = float('inf')
    start_time = time.time()
    
    for k in range(max_iterations):
        # 计算约束违反量
        violations = np.zeros(5)
        violations[0] = max(0, 8 - (-x[0] + 4*x[1] + 2*x[2]))
        violations[1] = max(0, 6 - (3*x[0] + 2*x[1]))
        violations[2] = max(0, -x[0])
        violations[3] = max(0, -x[1])
        violations[4] = max(0, -x[2])
        
        # 检查可行性
        is_feasible = check_feasibility(x)
        f_val = objective_function(x)
        
        if is_feasible and f_val < best_feasible_value:
            best_feasible_x = x.copy()
            best_feasible_value = f_val
        
        print(f"{k:<3} {x[0]:<8.4f} {x[1]:<8.4f} {x[2]:<8.4f} {f_val:<10.4f} "
              f"{'是' if is_feasible else '否':<8}")
        
        # 计算次梯度
        grad_f = np.array([2.0, 3.0, 1.0])
        grad_g = np.zeros((5, 3))
        
        if violations[0] > 0:
            grad_g[0] = np.array([1.0, -4.0, -2.0])
        if violations[1] > 0:
            grad_g[1] = np.array([-3.0, -2.0, 0.0])
        if violations[2] > 0:
            grad_g[2] = np.array([-1.0, 0.0, 0.0])
        if violations[3] > 0:
            grad_g[3] = np.array([0.0, -1.0, 0.0])
        if violations[4] > 0:
            grad_g[4] = np.array([0.0, 0.0, -1.0])
        
        subgrad_x = grad_f + np.dot(lambda_vec, grad_g)
        subgrad_lambda = violations
        
        # 更新变量
        alpha = 1.0 / (k + 1)  # 递减步长
        x = x - alpha * subgrad_x
        lambda_vec = np.maximum(0, lambda_vec + alpha * subgrad_lambda)
        
        # 简单收敛检查
        if is_feasible and np.max(violations) < 1e-6:
            print(f"次梯度法在第{k}次迭代找到可行解")
            break
    
    end_time = time.time()
    
    print("-" * 60)
    if best_feasible_x is not None:
        print(f"次梯度法最优解: x* = [{best_feasible_x[0]:.6f}, {best_feasible_x[1]:.6f}, {best_feasible_x[2]:.6f}]")
        print(f"次梯度法最优值: f* = {best_feasible_value:.6f}")
        print(f"迭代次数: {k+1}")
        print(f"计算时间: {end_time - start_time:.6f} 秒")
    else:
        print("次梯度法未找到可行解")
    
    return {
        'optimal_point': best_feasible_x if best_feasible_x is not None else x,
        'optimal_value': best_feasible_value if best_feasible_x is not None else f_val,
        'time': end_time - start_time,
        'iterations': k + 1,
        'success': best_feasible_x is not None
    }

def compare_methods():
    """比较两种方法的效率"""
    print("\n" + "=" * 80)
    print("单纯形法与次梯度法效率比较")
    print("=" * 80)
    
    # 获取理论最优解（单纯形法）
    simplex_result = solve_with_scipy()
    
    # 次梯度法求解
    subgrad_result = simple_subgradient_method()
    
    # 比较结果
    if simplex_result['success'] and subgrad_result['success']:
        print("\n" + "=" * 80)
        print("性能比较结果")
        print("=" * 80)
        print(f"{'指标':<20} {'单纯形法':<15} {'次梯度法':<15} {'优势方':<15}")
        print("-" * 80)
        
        # 迭代次数
        simplex_iter = simplex_result.get('iterations', 'N/A')
        subgrad_iter = subgrad_result['iterations']
        if isinstance(simplex_iter, int):
            iter_winner = "单纯形法" if simplex_iter < subgrad_iter else "次梯度法"
        else:
            iter_winner = "单纯形法"
        print(f"{'迭代次数':<20} {simplex_iter:<15} {subgrad_iter:<15} {iter_winner:<15}")
        
        # 计算时间
        simplex_time = simplex_result['time']
        subgrad_time = subgrad_result['time']
        time_winner = "单纯形法" if simplex_time < subgrad_time else "次梯度法"
        print(f"{'计算时间(秒)':<20} {simplex_time:<15.6f} {subgrad_time:<15.6f} {time_winner:<15}")
        
        # 解的精度
        simplex_value = simplex_result['optimal_value']
        subgrad_value = subgrad_result['optimal_value']
        error = abs(subgrad_value - simplex_value)
        print(f"{'最优值':<20} {simplex_value:<15.6f} {subgrad_value:<15.6f} {'单纯形法':<15}")
        print(f"{'解的误差':<20} {0:<15.6f} {error:<15.6e} {'单纯形法':<15}")
        
        # 其他特性
        print(f"{'解的精度':<20} {'精确':<15} {'近似':<15} {'单纯形法':<15}")
        print(f"{'实现复杂度':<20} {'复杂':<15} {'简单':<15} {'次梯度法':<15}")
        print(f"{'内存需求':<20} {'中等':<15} {'低':<15} {'次梯度法':<15}")
        print(f"{'适用范围':<20} {'线性规划':<15} {'凸优化':<15} {'次梯度法':<15}")

def algorithm_analysis():
    """算法原理分析"""
    print("\n" + "=" * 80)
    print("算法原理分析")
    print("=" * 80)
    
    print("单纯形法:")
    print("  原理: 在可行域的顶点间移动寻找最优解")
    print("  优点: 精确解、有限步收敛、理论完备")
    print("  缺点: 最坏情况指数复杂度、实现复杂")
    print("  适用: 线性规划问题")
    
    print("\n次梯度法:")
    print("  原理: 使用次梯度信息进行迭代优化")
    print("  优点: 通用性强、实现简单、内存需求低")
    print("  缺点: 收敛慢、解的精度有限")
    print("  适用: 一般凸优化问题")
    
    print("\n对于本问题的建议:")
    print("  推荐使用单纯形法，因为:")
    print("  - 问题是标准线性规划")
    print("  - 规模较小，单纯形法高效")
    print("  - 需要精确解")

def main():
    """主函数"""
    print("线性规划问题求解：单纯形法 vs 次梯度法")
    print("问题: min 2x1 + 3x2 + x3")
    print("约束: -x1 + 4x2 + 2x3 >= 8")
    print("     3x1 + 2x2 >= 6")
    print("     xi >= 0, i = 1,2,3")
    
    # 手工单纯形法演示
    manual_simplex_demonstration()
    
    # 方法比较
    compare_methods()
    
    # 算法分析
    algorithm_analysis()

if __name__ == "__main__":
    main()
