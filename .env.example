# Flask配置
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=your-secret-key-here-change-this-in-production

# 服务器配置
HOST=0.0.0.0
PORT=5000

# Neo4j数据库配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your-neo4j-password

# 模型配置
MODEL_PATH=./model/
BERT_MODEL_PATH=./model/chinese-roberta-wwm-ext/
NER_MODEL_PATH=./model/best_roberta_rnn_model_ent_aug.pt
TAG2IDX_PATH=./tmp_data/tag2idx.npy

# 数据路径配置
DATA_PATH=./data/
MEDICAL_DATA_PATH=./data/medical.json
ENTITY_DATA_PATH=./data/ent_aug/
NER_DATA_PATH=./data/ner_data_aug.txt

# 用户数据配置
USER_DATA_PATH=./tmp_data/
UPLOAD_FOLDER=./static/uploads/
MAX_CONTENT_LENGTH=16777216  # 16MB

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log
ERROR_LOG_FILE=./logs/error.log

# 缓存配置
CACHE_TYPE=simple
CACHE_DEFAULT_TIMEOUT=300

# 安全配置
SESSION_COOKIE_SECURE=False
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax

# API配置
API_RATE_LIMIT=100  # 每分钟请求次数限制
API_TIMEOUT=30      # API超时时间（秒）

# 知识图谱配置
GRAPH_MAX_NODES=1000
GRAPH_MAX_RELATIONSHIPS=2000
GRAPH_SEARCH_LIMIT=50

# 模型推理配置
MAX_SEQUENCE_LENGTH=512
BATCH_SIZE=32
DEVICE=auto  # auto, cpu, cuda

# 备份配置
BACKUP_ENABLED=True
BACKUP_INTERVAL=24  # 小时
BACKUP_PATH=./backups/
