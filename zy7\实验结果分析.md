# 外罚函数法实验结果分析

## 1. 实验概述

### 1.1 问题设置
- **目标函数**: $f(x) = x_1^2 + 4x_2^2 - 2x_1 - x_2$
- **约束条件**: $g(x) = x_1 + x_2 - 1 \leq 0$

### 1.2 算法参数
- **初始点**: $x_0 = (0, 0)^T$
- **初始罚参数**: $r_0 = 1.0$
- **增长因子**: $\beta = 10.0$
- **收敛容差**: $\varepsilon = 10^{-6}$

## 2. 实验结果

### 2.1 外罚函数法求解过程

| 迭代 | 罚参数 | $x_1$ | $x_2$ | $f(x)$ | $g(x)$ | $P(x,r)$ | 可行性 |
|------|--------|-------|-------|--------|--------|----------|--------|
| 0 | $1.00 \times 10^0$ | 0.944444 | 0.111111 | -1.058642 | $5.56 \times 10^{-2}$ | -1.055556 | 否 |
| 1 | $1.00 \times 10^1$ | 0.907407 | 0.101852 | -1.051783 | $9.26 \times 10^{-3}$ | -1.050926 | 否 |
| 2 | $1.00 \times 10^2$ | 0.900794 | 0.100198 | -1.050198 | $9.92 \times 10^{-4}$ | -1.050099 | 否 |
| 3 | $1.00 \times 10^3$ | 0.900080 | 0.100020 | -1.050020 | $9.99 \times 10^{-5}$ | -1.050010 | 否 |
| 4 | $1.00 \times 10^4$ | 0.900008 | 0.100002 | -1.050002 | $9.99 \times 10^{-6}$ | -1.050001 | 否 |
| 5 | $1.00 \times 10^5$ | 0.900001 | 0.100000 | -1.050000 | $9.99 \times 10^{-7}$ | -1.050000 | 是 |

### 2.2 最终结果
- **最优解**: $x^* = (0.900001, 0.100000)^T$
- **最优值**: $f^* = -1.050000$
- **约束值**: $g(x^*) = 9.99 \times 10^{-7}$
- **迭代次数**: 6次
- **收敛性**: 成功收敛

### 2.3 scipy SLSQP对比结果
- **最优解**: $x^* = (0.900000, 0.100000)^T$
- **最优值**: $f^* = -1.050000$
- **约束值**: $g(x^*) = -6.66 \times 10^{-16}$
- **迭代次数**: 3次

## 3. 结果分析

### 3.1 收敛性分析

#### 3.1.1 收敛行为
外罚函数法表现出典型的收敛模式：
1. **快速接近约束边界**：前几次迭代快速减少约束违反量
2. **沿边界优化**：后续迭代在约束边界附近寻找最优解
3. **渐近收敛**：约束违反量呈指数级减少

#### 3.1.2 收敛速度
- **约束违反量减少**：每次迭代约减少一个数量级
- **目标函数收敛**：快速收敛到最优值附近
- **总体评价**：收敛速度符合理论预期

### 3.2 解的质量分析

#### 3.2.1 与scipy对比
| 指标 | 外罚函数法 | scipy SLSQP | 差异 |
|------|------------|-------------|------|
| $x_1$ | 0.900001 | 0.900000 | $1 \times 10^{-6}$ |
| $x_2$ | 0.100000 | 0.100000 | $< 10^{-6}$ |
| $f(x)$ | -1.050000 | -1.050000 | $< 10^{-6}$ |
| $g(x)$ | $9.99 \times 10^{-7}$ | $-6.66 \times 10^{-16}$ | 约束精度差异 |

#### 3.2.2 解的精度
- **变量精度**：达到 $10^{-6}$ 级别
- **目标函数精度**：达到 $10^{-6}$ 级别
- **约束满足精度**：达到 $10^{-7}$ 级别
- **总体评价**：精度满足工程要求

### 3.3 算法效率分析

#### 3.3.1 迭代效率
- **外罚函数法**：6次外层迭代
- **scipy SLSQP**：3次迭代
- **效率比较**：外罚函数法需要更多迭代，但每次迭代相对简单

#### 3.3.2 计算复杂度
- **子问题求解**：每次需要求解无约束优化问题
- **梯度计算**：需要计算罚函数梯度
- **参数更新**：简单的参数递增操作

## 4. 理论验证

### 4.1 无约束最优解分析

#### 4.1.1 无约束最优解
令 $\nabla f(x) = 0$：
$$\begin{cases}
2x_1 - 2 = 0 \\
8x_2 - 1 = 0
\end{cases}$$

解得：$x_{unc}^* = (1, 0.125)^T$

#### 4.1.2 约束检验
$g(x_{unc}^*) = 1 + 0.125 - 1 = 0.125 > 0$

无约束最优解违反约束，因此约束起作用。

### 4.2 约束最优解分析

#### 4.2.1 KKT条件
由于约束起作用，最优解在约束边界上：$x_1 + x_2 = 1$

在约束边界上，问题变为：
$$\min_{x_1} f(x_1, 1-x_1) = x_1^2 + 4(1-x_1)^2 - 2x_1 - (1-x_1)$$

#### 4.2.2 一维优化
$$\frac{d}{dx_1}[x_1^2 + 4(1-x_1)^2 - 2x_1 - (1-x_1)] = 0$$

展开并求导：
$$\frac{d}{dx_1}[x_1^2 + 4(1-2x_1+x_1^2) - 2x_1 - 1 + x_1] = 0$$
$$\frac{d}{dx_1}[5x_1^2 - 9x_1 + 3] = 0$$
$$10x_1 - 9 = 0$$

解得：$x_1^* = 0.9$，$x_2^* = 0.1$

#### 4.2.3 理论最优解
- **最优点**：$(0.9, 0.1)^T$
- **最优值**：$f^* = 0.81 + 0.04 - 1.8 - 0.1 = -1.05$

### 4.3 实验结果验证

实验结果 $(0.900001, 0.100000)^T$ 与理论解析解 $(0.9, 0.1)^T$ 高度一致，验证了算法的正确性。

## 5. 不同初始点测试

### 5.1 测试结果汇总

| 初始点 | 约束值 | 可行性 | 最终解 | 收敛性 |
|--------|--------|--------|--------|--------|
| $(0, 0)$ | -1.0 | 是 | $(0.9008, 0.1002)$ | 收敛 |
| $(2, 2)$ | 3.0 | 否 | $(0.9008, 0.1002)$ | 收敛 |
| $(-1, 0.5)$ | -1.5 | 是 | $(0.9008, 0.1002)$ | 收敛 |
| $(1.5, 1.5)$ | 2.0 | 否 | $(0.9008, 0.1002)$ | 收敛 |

### 5.2 初始点影响分析

#### 5.2.1 收敛性
- **全局收敛**：所有测试的初始点都收敛到相同的解
- **鲁棒性**：算法对初始点选择不敏感
- **可行性无关**：无论初始点是否可行，都能收敛

#### 5.2.2 收敛路径
- **可行初始点**：直接在可行域内优化
- **不可行初始点**：先移向可行域，再优化

## 6. 算法性能评估

### 6.1 优点验证

#### 6.1.1 概念简单
- **实现容易**：算法逻辑清晰，编程实现简单
- **理解直观**：罚函数概念易于理解

#### 6.1.2 通用性强
- **约束类型**：适用于不等式约束
- **初始点**：不需要可行初始点
- **问题规模**：适用于中小规模问题

#### 6.1.3 理论保证
- **收敛性**：实验验证了理论收敛性
- **最优性**：收敛到理论最优解

### 6.2 缺点验证

#### 6.2.1 收敛速度
- **线性收敛**：收敛速度相对较慢
- **迭代次数**：需要6次迭代，比专门算法多

#### 6.2.2 数值问题
- **大罚参数**：当 $r = 10^5$ 时，可能出现数值问题
- **病态性**：罚参数过大导致Hessian矩阵病态

#### 6.2.3 精度限制
- **约束精度**：约束满足精度受罚参数限制
- **解的精度**：难以获得极高精度解

## 7. 参数敏感性分析

### 7.1 罚参数增长因子影响

#### 7.1.1 理论分析
- **增长因子过小**：收敛慢，需要更多迭代
- **增长因子过大**：可能导致数值不稳定
- **推荐值**：$\beta \in [2, 10]$

#### 7.1.2 实验验证
本实验使用 $\beta = 10$，表现良好：
- 收敛速度适中
- 数值稳定性好
- 解的质量高

### 7.2 初始罚参数影响

#### 7.2.1 选择原则
- **不宜过小**：收敛慢
- **不宜过大**：数值问题
- **本实验**：$r_0 = 1$ 表现良好

### 7.3 收敛容差影响

#### 7.3.1 精度权衡
- **容差过大**：解的精度不足
- **容差过小**：可能无法收敛
- **本实验**：$\varepsilon = 10^{-6}$ 平衡了精度和收敛性

## 8. 与其他方法比较

### 8.1 与scipy SLSQP比较

| 特性 | 外罚函数法 | scipy SLSQP | 评价 |
|------|------------|-------------|------|
| **迭代次数** | 6次 | 3次 | SLSQP更高效 |
| **解的精度** | $10^{-6}$ | $10^{-16}$ | SLSQP精度更高 |
| **实现复杂度** | 简单 | 复杂 | 外罚函数法更简单 |
| **理论基础** | 罚函数理论 | SQP理论 | 都有严格理论基础 |
| **适用性** | 通用 | 光滑问题 | 外罚函数法更通用 |

### 8.2 算法选择建议

#### 8.2.1 选择外罚函数法当：
- 问题规模较小
- 实现简单性重要
- 教学和研究目的
- 原型开发阶段

#### 8.2.2 选择专门算法当：
- 需要高精度解
- 计算效率要求高
- 大规模工程应用
- 实时优化需求

## 9. 总结

### 9.1 实验成功验证了理论分析

1. **收敛性**：算法成功收敛到理论最优解
2. **精度**：解的精度达到 $10^{-6}$ 级别
3. **鲁棒性**：对不同初始点都能收敛
4. **效率**：6次迭代收敛，效率可接受

### 9.2 算法特点得到验证

1. **优点**：概念简单、通用性强、无需可行初始点
2. **缺点**：收敛速度慢、精度有限、参数敏感
3. **适用性**：适合教学、研究和中小规模问题

### 9.3 实际应用价值

1. **教学工具**：帮助理解约束优化基本概念
2. **研究基础**：为开发更高级算法提供基础
3. **工程应用**：在精度要求不高的场合可以使用
4. **原型开发**：快速实现约束优化功能

通过本次实验，我们深入理解了外罚函数法的工作原理、性能特点和适用场景，为实际应用和进一步研究奠定了基础。
