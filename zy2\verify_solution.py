#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证最速下降法解的正确性
"""

def main():
    """验证解的正确性"""
    
    print("最速下降法求解验证")
    print("=" * 50)
    
    # 目标函数
    def f(x1, x2):
        return 4*(x1 + 1)**2 + 2*(x2 - 1)**2 + x1 + x2 + 10
    
    # 理论最优解
    x1_theory = -9/8  # -1.125
    x2_theory = 3/4   # 0.75
    f_theory = f(x1_theory, x2_theory)
    
    print(f"理论最优解:")
    print(f"x1* = {x1_theory:.6f}")
    print(f"x2* = {x2_theory:.6f}")
    print(f"f(x*) = {f_theory:.6f}")
    
    # 验证梯度为零
    grad1 = 8*x1_theory + 9
    grad2 = 4*x2_theory - 3
    
    print(f"\n梯度验证:")
    print(f"∂f/∂x1 = {grad1:.8f} (应该为0)")
    print(f"∂f/∂x2 = {grad2:.8f} (应该为0)")
    
    # 手工计算第一步
    print(f"\n手工计算第一步:")
    x1, x2 = 0.0, 0.0
    print(f"初始点: ({x1}, {x2})")
    
    g1 = 8*x1 + 9  # 9
    g2 = 4*x2 - 3  # -3
    print(f"梯度: ({g1}, {g2})")
    
    grad_norm = (g1**2 + g2**2)**0.5
    print(f"梯度范数: {grad_norm:.6f}")
    
    # 计算步长
    alpha = (g1**2 + g2**2) / (8*g1**2 + 4*g2**2)
    print(f"最优步长: {alpha:.6f}")
    
    # 更新点
    x1_new = x1 - alpha * g1
    x2_new = x2 - alpha * g2
    print(f"新点: ({x1_new:.6f}, {x2_new:.6f})")
    print(f"新函数值: {f(x1_new, x2_new):.6f}")
    
    print(f"\n算法特性:")
    print(f"条件数: κ = 8/4 = 2")
    print(f"收敛率: ρ = (2-1)/(2+1) = 1/3 ≈ 0.333")
    print(f"预期迭代次数: 约5-10次")

if __name__ == "__main__":
    main()
