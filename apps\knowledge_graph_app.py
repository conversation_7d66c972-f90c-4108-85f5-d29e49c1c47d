#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识图谱模块 - knowledge_graph_app.py
处理知识图谱可视化相关的所有功能
"""

from flask import Blueprint, render_template, request, jsonify, session
import py2neo

# 创建蓝图
kg_bp = Blueprint('knowledge_graph', __name__)

def get_neo4j_client():
    """获取Neo4j客户端"""
    return py2neo.Graph(
        'bolt://localhost:7687',
        user='neo4j',
        password='wangqi20040401',
        name='doctor'
    )

def get_graph_statistics():
    """获取图谱统计信息"""
    try:
        client = get_neo4j_client()

        # 获取节点总数
        node_count_query = "MATCH (n) RETURN count(n) as count"
        node_count = client.run(node_count_query).data()[0]['count']

        # 获取关系总数
        rel_count_query = "MATCH ()-[r]->() RETURN count(r) as count"
        rel_count = client.run(rel_count_query).data()[0]['count']

        # 获取各类型节点数量
        type_count_query = """
        MATCH (n)
        RETURN labels(n)[0] as type, count(n) as count
        ORDER BY count DESC
        """
        type_counts = client.run(type_count_query).data()

        return {
            'total_nodes': node_count,
            'total_relationships': rel_count,
            'node_types': type_counts
        }
    except Exception as e:
        print(f"获取图谱统计失败: {e}")
        return {
            'total_nodes': 0,
            'total_relationships': 0,
            'node_types': []
        }

def search_nodes_by_type(node_type, limit=50):
    """按类型搜索节点"""
    try:
        client = get_neo4j_client()

        if node_type:
            query = f"MATCH (n:{node_type}) RETURN n.名称 as name, labels(n) as type LIMIT {limit}"
        else:
            query = f"MATCH (n) RETURN n.名称 as name, labels(n) as type LIMIT {limit}"

        result = client.run(query)
        nodes = []

        for record in result:
            if record['name']:
                nodes.append({
                    'id': record['name'],
                    'name': record['name'],
                    'type': record['type'][0] if record['type'] else 'Unknown'
                })

        return nodes
    except Exception as e:
        print(f"搜索节点失败: {e}")
        return []

def get_connected_graph_data(limit=50):
    """获取连接的图谱数据（节点和关系一起获取）"""
    try:
        client = get_neo4j_client()

        # 获取有连接的节点和关系
        query = f"""
        MATCH (a)-[r]->(b)
        WHERE a.名称 IS NOT NULL AND b.名称 IS NOT NULL
        WITH a, b, r
        LIMIT {limit}
        RETURN a.名称 as source_name, labels(a) as source_type,
               b.名称 as target_name, labels(b) as target_type,
               type(r) as relationship
        """

        result = client.run(query)

        nodes_dict = {}
        relationships = []

        for record in result:
            source_name = record['source_name']
            target_name = record['target_name']
            source_type = record['source_type'][0] if record['source_type'] else 'Unknown'
            target_type = record['target_type'][0] if record['target_type'] else 'Unknown'

            # 添加源节点
            if source_name not in nodes_dict:
                nodes_dict[source_name] = {
                    'id': source_name,
                    'name': source_name,
                    'type': source_type
                }

            # 添加目标节点
            if target_name not in nodes_dict:
                nodes_dict[target_name] = {
                    'id': target_name,
                    'name': target_name,
                    'type': target_type
                }

            # 添加关系
            relationships.append({
                'source': source_name,
                'target': target_name,
                'relationship': record['relationship']
            })

        nodes = list(nodes_dict.values())

        return {
            'nodes': nodes,
            'relationships': relationships
        }

    except Exception as e:
        print(f"获取连接图谱数据失败: {e}")
        return {'nodes': [], 'relationships': []}

def search_graph_by_keyword(keyword, limit=50):
    """按关键词搜索图谱"""
    try:
        client = get_neo4j_client()

        if not keyword:
            return {'nodes': [], 'relationships': []}

        # 搜索包含关键词的节点及其关系
        query = f"""
        MATCH (a)-[r]->(b)
        WHERE a.名称 CONTAINS '{keyword}' OR b.名称 CONTAINS '{keyword}'
        RETURN a.名称 as source, b.名称 as target, type(r) as relationship,
               labels(a) as source_type, labels(b) as target_type
        LIMIT {limit}
        """

        result = client.run(query)
        nodes = set()
        relationships = []

        for record in result:
            source = record['source']
            target = record['target']

            nodes.add((source, record['source_type'][0] if record['source_type'] else 'Unknown'))
            nodes.add((target, record['target_type'][0] if record['target_type'] else 'Unknown'))

            relationships.append({
                'source': source,
                'target': target,
                'relationship': record['relationship']
            })

        nodes_list = [{'id': name, 'name': name, 'type': node_type} for name, node_type in nodes]

        return {
            'nodes': nodes_list,
            'relationships': relationships
        }
    except Exception as e:
        print(f"搜索图谱失败: {e}")
        return {'nodes': [], 'relationships': []}

def get_node_details(node_name):
    """获取节点详细信息"""
    try:
        client = get_neo4j_client()

        # 获取节点属性
        node_query = f"MATCH (n {{名称: '{node_name}'}}) RETURN n, labels(n) as type"
        node_result = client.run(node_query).data()

        if not node_result:
            return None

        node_data = node_result[0]['n']
        node_type = node_result[0]['type'][0] if node_result[0]['type'] else 'Unknown'

        # 获取相关关系
        rel_query = f"""
        MATCH (n {{名称: '{node_name}'}})-[r]-(m)
        RETURN type(r) as relationship, m.名称 as related_node,
               labels(m) as related_type, startNode(r).名称 as start_node
        LIMIT 20
        """
        rel_result = client.run(rel_query).data()

        relationships = []
        for rel in rel_result:
            direction = "outgoing" if rel['start_node'] == node_name else "incoming"
            relationships.append({
                'relationship': rel['relationship'],
                'related_node': rel['related_node'],
                'related_type': rel['related_type'][0] if rel['related_type'] else 'Unknown',
                'direction': direction
            })

        return {
            'name': node_name,
            'type': node_type,
            'properties': dict(node_data),
            'relationships': relationships
        }
    except Exception as e:
        print(f"获取节点详情失败: {e}")
        return None

# 路由定义
@kg_bp.route('/knowledge-graph')
def knowledge_graph_page():
    """知识图谱页面"""
    if 'user_id' not in session:
        return render_template('login.html')
    return render_template('knowledge_graph.html')

@kg_bp.route('/api/knowledge-graph/statistics', methods=['GET'])
def api_get_statistics():
    """获取图谱统计信息API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        stats = get_graph_statistics()
        return jsonify({'success': True, 'statistics': stats})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@kg_bp.route('/api/knowledge-graph/nodes', methods=['GET'])
def api_get_nodes():
    """获取知识图谱节点API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        node_type = request.args.get('type', '')
        limit = int(request.args.get('limit', 50))

        nodes = search_nodes_by_type(node_type, limit)
        return jsonify({'success': True, 'nodes': nodes})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@kg_bp.route('/api/knowledge-graph/connected-data', methods=['GET'])
def api_get_connected_data():
    """获取连接的知识图谱数据API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        limit = int(request.args.get('limit', 50))
        data = get_connected_graph_data(limit)
        return jsonify({
            'success': True,
            'nodes': data['nodes'],
            'relationships': data['relationships']
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@kg_bp.route('/api/knowledge-graph/relationships', methods=['GET'])
def api_get_relationships():
    """获取知识图谱关系API（保持向后兼容）"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        limit = int(request.args.get('limit', 100))
        data = get_connected_graph_data(limit)
        return jsonify({'success': True, 'relationships': data['relationships']})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@kg_bp.route('/api/knowledge-graph/search', methods=['GET'])
def api_search_graph():
    """搜索知识图谱API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        keyword = request.args.get('keyword', '')
        limit = int(request.args.get('limit', 50))

        if not keyword:
            return jsonify({'success': False, 'message': '请提供搜索关键词'})

        result = search_graph_by_keyword(keyword, limit)
        return jsonify({
            'success': True,
            'nodes': result['nodes'],
            'relationships': result['relationships']
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@kg_bp.route('/api/knowledge-graph/node/<node_name>', methods=['GET'])
def api_get_node_details(node_name):
    """获取节点详情API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        details = get_node_details(node_name)
        if details:
            return jsonify({'success': True, 'node': details})
        else:
            return jsonify({'success': False, 'message': '节点不存在'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@kg_bp.route('/api/knowledge-graph/expand/<node_name>', methods=['GET'])
def api_expand_node(node_name):
    """展开节点关系API"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        client = get_neo4j_client()

        # 获取该节点的直接邻居
        query = f"""
        MATCH (center {{名称: '{node_name}'}})-[r]-(neighbor)
        RETURN center.名称 as center_name, labels(center) as center_type,
               neighbor.名称 as neighbor_name, labels(neighbor) as neighbor_type,
               type(r) as relationship,
               startNode(r).名称 = '{node_name}' as is_outgoing
        LIMIT 20
        """

        result = client.run(query).data()

        nodes = set()
        relationships = []

        # 添加中心节点
        if result:
            center_type = result[0]['center_type'][0] if result[0]['center_type'] else 'Unknown'
            nodes.add((node_name, center_type))

        for record in result:
            neighbor_name = record['neighbor_name']
            neighbor_type = record['neighbor_type'][0] if record['neighbor_type'] else 'Unknown'

            nodes.add((neighbor_name, neighbor_type))

            if record['is_outgoing']:
                relationships.append({
                    'source': node_name,
                    'target': neighbor_name,
                    'relationship': record['relationship']
                })
            else:
                relationships.append({
                    'source': neighbor_name,
                    'target': node_name,
                    'relationship': record['relationship']
                })

        nodes_list = [{'id': name, 'name': name, 'type': node_type} for name, node_type in nodes]

        return jsonify({
            'success': True,
            'nodes': nodes_list,
            'relationships': relationships
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})
