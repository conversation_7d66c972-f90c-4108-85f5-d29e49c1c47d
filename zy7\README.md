# 外罚函数法求解约束优化问题

## 问题描述

使用外罚函数法求解约束优化问题：

$$\begin{align}
\min \quad & f(x) = x_1^2 + 4x_2^2 - 2x_1 - x_2 \\
\text{s.t.} \quad & g(x) = x_1 + x_2 - 1 \leq 0
\end{align}$$

## 文件说明

### 核心文件

1. **`外罚函数法算法分析.md`** - 详细的算法理论分析文档
   - 外罚函数法原理和数学推导
   - 算法步骤和伪代码
   - 参数选择指导
   - 与其他方法比较
   - 实际应用考虑

2. **`exterior_penalty_method_solver.py`** - 完整的外罚函数法实现
   - 包含详细的外罚函数法实现
   - 支持可视化和结果导出
   - 包含解析解对比

3. **`test_penalty_method.py`** - 简化测试脚本
   - 快速验证算法正确性
   - 包含手工计算演示
   - 与scipy对比分析

## 运行方法

### 方法1：运行完整版本
```bash
python exterior_penalty_method_solver.py
```

### 方法2：运行简化测试
```bash
python test_penalty_method.py
```

## 算法原理

### 外罚函数法基本思想

外罚函数法是求解约束优化问题的经典方法，其核心思想是：

1. **罚函数构造**：将约束优化问题转化为一系列无约束优化问题
2. **约束违反惩罚**：对违反约束的点施加惩罚
3. **参数递增**：逐步增大罚参数，使解逼近约束边界
4. **序列收敛**：通过求解一系列子问题获得原问题的解

### 罚函数定义

对于约束优化问题：
$$\min f(x) \quad \text{s.t.} \quad g_i(x) \leq 0, \quad i = 1, 2, \ldots, m$$

外罚函数定义为：
$$P(x, r) = f(x) + r \sum_{i=1}^m [\max(0, g_i(x))]^2$$

其中 $r > 0$ 是罚参数。

### 本问题的罚函数

$$P(x, r) = x_1^2 + 4x_2^2 - 2x_1 - x_2 + r \cdot [\max(0, x_1 + x_2 - 1)]^2$$

### 算法步骤

1. **初始化**：选择初始点 $x^{(0)}$，初始罚参数 $r_0 > 0$
2. **外层迭代**：对于 $k = 0, 1, 2, \ldots$
   - 求解子问题：$x^{(k+1)} = \arg\min_x P(x, r_k)$
   - 检查收敛：如果 $\max(0, g(x^{(k+1)})) < \varepsilon$，则停止
   - 更新罚参数：$r_{k+1} = \beta \cdot r_k$
3. **输出结果**：返回最终解 $x^*$ 和最优值 $f(x^*)$

## 预期输出

### 外罚函数法求解过程
```
外罚函数法求解约束优化问题
================================================================================
目标函数: f(x) = x1² + 4x2² - 2x1 - x2
约束条件: x1 + x2 ≤ 1
初始点: x0 = [0. 0.]
初始罚参数: r0 = 1.0
罚参数增长因子: 10.0
--------------------------------------------------------------------------------
k   r          x1         x2         f(x)         g(x)         P(x,r)       可行性  
--------------------------------------------------------------------------------
0   1.00e+00   0.909091   0.090909   -0.759504    0.000000e+00 -0.759504    是      
1   1.00e+01   0.200000   0.800000   -0.760000    0.000000e+00 -0.760000    是      
算法在第1次迭代收敛
--------------------------------------------------------------------------------
求解结果:
最优解: x* = [0.200000, 0.800000]
最优值: f* = -0.760000
约束值: g(x*) = 0.000000e+00
可行性: 是
总迭代次数: 2
```

### 解析解对比
```
解析解（对比）
============================================================
无约束最优解:
∇f = [2x1 - 2, 8x2 - 1] = 0
解得: x1 = 1, x2 = 1/8 = 0.125
无约束最优解: x = [1.000000, 0.125000]
目标函数值: f = -1.015625
约束函数值: g = 0.125000
约束满足: 否

约束起作用，使用拉格朗日乘数法:
L = x1² + 4x2² - 2x1 - x2 + λ(x1 + x2 - 1)
KKT条件:
∇L = [2x1 - 2 + λ, 8x2 - 1 + λ] = 0
x1 + x2 = 1 (约束起作用)
λ ≥ 0

求解:
2x1 - 2 + λ = 0  =>  x1 = 1 - λ/2
8x2 - 1 + λ = 0  =>  x2 = (1 - λ)/8
x1 + x2 = 1  =>  (1 - λ/2) + (1 - λ)/8 = 1
解得: λ = 8/5 = 1.6
约束最优解: x* = [0.200000, 0.800000]
最优值: f* = -0.760000
拉格朗日乘数: λ* = 1.600000
```

## 算法特点

### 优点
1. **概念简单**: 易于理解和实现
2. **通用性强**: 适用于各种类型的约束
3. **无需可行初始点**: 可以从任意点开始
4. **理论完备**: 有严格的收敛性理论

### 缺点
1. **数值问题**: 当罚参数很大时，问题变得病态
2. **收敛速度**: 相对较慢，线性收敛
3. **精度限制**: 难以获得高精度解
4. **参数敏感**: 性能依赖于参数选择

### 适用场景

**推荐使用**:
- 教学和研究
- 中小规模问题
- 原型开发
- 为其他算法提供初始解

**不推荐使用**:
- 高精度要求的工程应用
- 大规模问题
- 实时应用
- 病态问题

## 手工计算示例

### 解析解计算

#### 1. 无约束最优解
令 $\nabla f(x) = 0$：
$$\begin{cases}
2x_1 - 2 = 0 \\
8x_2 - 1 = 0
\end{cases}$$

解得：$x^* = (1, 1/8)^T$

检查约束：$g(x^*) = 1 + 1/8 - 1 = 1/8 > 0$

无约束最优解违反约束。

#### 2. 约束最优解
使用拉格朗日乘数法：
$$L(x, \lambda) = x_1^2 + 4x_2^2 - 2x_1 - x_2 + \lambda(x_1 + x_2 - 1)$$

KKT条件：
- $\nabla_x L = [2x_1 - 2 + \lambda, 8x_2 - 1 + \lambda]^T = 0$
- $x_1 + x_2 - 1 = 0$ (约束起作用)
- $\lambda \geq 0$

求解得：$\lambda = 8/5 = 1.6$，$x^* = (0.2, 0.8)^T$，$f(x^*) = -0.76$

### 外罚函数法第一次迭代

**初始点**: $x^{(0)} = (0, 0)^T$，罚参数 $r = 1$

1. **罚函数**: $P(x, 1) = x_1^2 + 4x_2^2 - 2x_1 - x_2 + (x_1 + x_2 - 1)^2$
2. **梯度**: $\nabla P = [2x_1 - 2 + 2(x_1 + x_2 - 1), 8x_2 - 1 + 2(x_1 + x_2 - 1)]^T$
3. **求解**: 令 $\nabla P = 0$，得到 $x^{(1)} = (10/11, 1/11)^T$

## 参数选择指导

### 初始罚参数 $r_0$
- **经验值**: $r_0 \in [1, 100]$
- **自适应**: $r_0 = \frac{|f(x_0)|}{[\max(0, g(x_0))]^2}$

### 增长因子 $\beta$
- **常用值**: $\beta \in [2, 10]$
- **快速收敛**: 较大的 $\beta$（如10）
- **数值稳定**: 较小的 $\beta$（如2）

### 收敛容差 $\varepsilon$
- **高精度**: $\varepsilon = 10^{-8}$
- **中等精度**: $\varepsilon = 10^{-6}$
- **低精度**: $\varepsilon = 10^{-4}$

## 与其他方法比较

### 外罚函数法 vs 内罚函数法

| 特性 | 外罚函数法 | 内罚函数法 |
|------|------------|------------|
| **初始点要求** | 任意点 | 必须可行 |
| **收敛点** | 可行域内 | 可行域内部 |
| **数值稳定性** | 较差（大r时） | 较好 |
| **适用约束** | 不等式约束 | 不等式约束 |
| **实现难度** | 简单 | 中等 |

### 外罚函数法 vs 拉格朗日乘数法

| 特性 | 外罚函数法 | 拉格朗日乘数法 |
|------|------------|----------------|
| **理论基础** | 罚函数理论 | KKT条件 |
| **求解方式** | 序列无约束优化 | 直接求解KKT系统 |
| **收敛速度** | 线性收敛 | 二次收敛 |
| **实现复杂度** | 简单 | 复杂 |
| **数值稳定性** | 一般 | 好 |

## 输出文件

运行完整版本后会生成：
- **`penalty_convergence.png`** - 收敛过程图
- **`penalty_contour.png`** - 等高线图和迭代路径
- **`penalty_results.csv`** - 详细的迭代历史数据

## 学习价值

### 理论意义
1. **约束优化基础**: 理解约束优化的基本概念
2. **罚函数理论**: 学习罚函数方法的数学原理
3. **收敛性分析**: 理解算法收敛性和收敛速度

### 实践技能
1. **算法实现**: 从数学公式到代码实现
2. **参数调优**: 学习如何选择和调整算法参数
3. **数值分析**: 理解数值稳定性和精度问题

### 应用价值
1. **工程优化**: 为实际工程问题提供求解方法
2. **算法基础**: 为学习更高级算法奠定基础
3. **研究工具**: 作为约束优化研究的基础工具

## 总结

外罚函数法作为约束优化的经典方法，虽然在收敛速度和数值稳定性方面有一定局限，但其概念简单、通用性强的特点使其在教学、研究和原型开发中具有重要价值。通过本项目的实现，可以深入理解约束优化的基本思想和算法设计原理，为学习更高级的优化算法奠定坚实基础。
