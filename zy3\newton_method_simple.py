#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
牛顿法求解优化问题 - 简化版本
问题：f(x) = 4(x1+1)^2 + 2(x2-1)^2 + x1 + x2 + 10
初始点：x0 = (0, 0)^T
精度：ε = 10^-5
"""

import numpy as np

def objective_function(x):
    """目标函数 f(x) = 4(x1+1)^2 + 2(x2-1)^2 + x1 + x2 + 10"""
    x1, x2 = x[0], x[1]
    return 4*(x1 + 1)**2 + 2*(x2 - 1)**2 + x1 + x2 + 10

def gradient(x):
    """计算梯度"""
    x1, x2 = x[0], x[1]
    grad_x1 = 8*x1 + 9
    grad_x2 = 4*x2 - 3
    return np.array([grad_x1, grad_x2])

def hessian(x):
    """计算Hessian矩阵（对于二次函数是常数矩阵）"""
    return np.array([[8, 0], [0, 4]])

def newton_method(x0, epsilon=1e-5, max_iterations=1000):
    """牛顿法主算法"""
    x = np.array(x0, dtype=float)
    
    print("=" * 80)
    print("牛顿法求解优化问题")
    print("=" * 80)
    print(f"目标函数: f(x) = 4(x1+1)² + 2(x2-1)² + x1 + x2 + 10")
    print(f"初始点: x0 = {x0}")
    print(f"收敛精度: ε = {epsilon}")
    print("-" * 80)
    print(f"{'k':<4} {'x1':<12} {'x2':<12} {'f(x)':<12} {'||∇f||':<12} {'状态':<12}")
    print("-" * 80)
    
    for k in range(max_iterations):
        # 计算梯度
        grad = gradient(x)
        grad_norm = np.linalg.norm(grad)
        f_val = objective_function(x)
        
        # 检查收敛条件
        if grad_norm < epsilon:
            print(f"{k:<4} {x[0]:<12.6f} {x[1]:<12.6f} {f_val:<12.6f} {grad_norm:<12.6e} {'收敛':<12}")
            break
        
        print(f"{k:<4} {x[0]:<12.6f} {x[1]:<12.6f} {f_val:<12.6f} {grad_norm:<12.6e} {'继续':<12}")
        
        # 计算Hessian矩阵
        H = hessian(x)
        
        # 检查Hessian矩阵是否正定
        eigenvalues = np.linalg.eigvals(H)
        if np.any(eigenvalues <= 0):
            print(f"错误: Hessian矩阵不正定，特征值: {eigenvalues}")
            break
        
        # 求解牛顿方向：H * d = -∇f
        try:
            newton_direction = np.linalg.solve(H, -grad)
        except np.linalg.LinAlgError:
            print("错误: Hessian矩阵奇异，无法求解")
            break
        
        # 更新点
        x = x + newton_direction
    
    else:
        print(f"达到最大迭代次数 {max_iterations}")
    
    print("-" * 80)
    print("最终结果:")
    print(f"最优点: x* = [{x[0]:.8f}, {x[1]:.8f}]")
    print(f"最优值: f(x*) = {objective_function(x):.8f}")
    print(f"梯度范数: ||∇f(x*)|| = {np.linalg.norm(gradient(x)):.8e}")
    print(f"迭代次数: {k+1}")
    
    return x

def verify_theoretical_solution():
    """验证理论解析解"""
    print("\n" + "=" * 80)
    print("理论解析解验证:")
    print("=" * 80)
    print("对于二次函数，最优解可通过 ∇f(x*) = 0 求得:")
    print("∂f/∂x1 = 8x1 + 9 = 0  =>  x1* = -9/8 = -1.125")
    print("∂f/∂x2 = 4x2 - 3 = 0  =>  x2* = 3/4 = 0.75")
    
    x_theory = np.array([-9/8, 3/4])
    f_theory = objective_function(x_theory)
    grad_theory = gradient(x_theory)
    H_theory = hessian(x_theory)
    
    print(f"理论最优点: x* = [{x_theory[0]:.8f}, {x_theory[1]:.8f}]")
    print(f"理论最优值: f(x*) = {f_theory:.8f}")
    print(f"理论梯度: ∇f(x*) = [{grad_theory[0]:.8e}, {grad_theory[1]:.8e}]")
    
    print(f"Hessian矩阵:")
    print(f"H = [[{H_theory[0,0]:.1f}, {H_theory[0,1]:.1f}],")
    print(f"     [{H_theory[1,0]:.1f}, {H_theory[1,1]:.1f}]]")
    
    # 分析Hessian矩阵性质
    eigenvalues = np.linalg.eigvals(H_theory)
    condition_number = np.max(eigenvalues) / np.min(eigenvalues)
    
    print(f"特征值: λ1 = {eigenvalues[0]:.1f}, λ2 = {eigenvalues[1]:.1f}")
    print(f"条件数: κ = {condition_number:.1f}")
    print(f"正定性: {'是' if np.all(eigenvalues > 0) else '否'}")
    
    return x_theory, f_theory

def manual_calculation_demo():
    """手工计算演示"""
    print("\n" + "=" * 80)
    print("手工计算第一次迭代演示:")
    print("=" * 80)
    
    # 初始点
    x0 = np.array([0.0, 0.0])
    print(f"初始点: x⁽⁰⁾ = ({x0[0]}, {x0[1]})")
    
    # 计算梯度
    grad0 = gradient(x0)
    print(f"梯度: ∇f(x⁽⁰⁾) = ({grad0[0]}, {grad0[1]})")
    
    # 梯度范数
    grad_norm0 = np.linalg.norm(grad0)
    print(f"梯度范数: ||∇f(x⁽⁰⁾)|| = {grad_norm0:.6f}")
    
    # Hessian矩阵
    H = hessian(x0)
    print(f"Hessian矩阵:")
    print(f"H = [[{H[0,0]}, {H[0,1]}],")
    print(f"     [{H[1,0]}, {H[1,1]}]]")
    
    # 求解牛顿方向
    newton_dir = np.linalg.solve(H, -grad0)
    print(f"牛顿方向: d⁽⁰⁾ = ({newton_dir[0]:.6f}, {newton_dir[1]:.6f})")
    
    # 更新点
    x1 = x0 + newton_dir
    print(f"更新点: x⁽¹⁾ = ({x1[0]:.6f}, {x1[1]:.6f})")
    
    # 验证收敛
    grad1 = gradient(x1)
    print(f"新点梯度: ∇f(x⁽¹⁾) = ({grad1[0]:.8f}, {grad1[1]:.8f})")
    print(f"新点函数值: f(x⁽¹⁾) = {objective_function(x1):.8f}")

def compare_with_steepest_descent():
    """与最速下降法比较"""
    print("\n" + "=" * 80)
    print("牛顿法 vs 最速下降法比较:")
    print("=" * 80)
    
    print(f"{'特性':<20} {'牛顿法':<20} {'最速下降法':<20}")
    print("-" * 60)
    print(f"{'收敛速度':<20} {'二次收敛':<20} {'线性收敛':<20}")
    print(f"{'对二次函数':<20} {'1次迭代':<20} {'多次迭代':<20}")
    print(f"{'每次计算量':<20} {'O(n³)':<20} {'O(n)':<20}")
    print(f"{'内存需求':<20} {'O(n²)':<20} {'O(n)':<20}")
    print(f"{'Hessian信息':<20} {'需要':<20} {'不需要':<20}")
    print(f"{'线搜索':<20} {'通常不需要':<20} {'需要':<20}")
    print(f"{'适用范围':<20} {'强凸函数':<20} {'一般凸函数':<20}")

def analyze_convergence_properties():
    """分析收敛性质"""
    print("\n" + "=" * 80)
    print("牛顿法收敛性质分析:")
    print("=" * 80)
    
    # Hessian矩阵分析
    H = hessian(np.array([0, 0]))  # 常数矩阵
    eigenvalues = np.linalg.eigvals(H)
    condition_number = np.max(eigenvalues) / np.min(eigenvalues)
    
    print(f"Hessian矩阵特征值: λ1 = {eigenvalues[0]:.1f}, λ2 = {eigenvalues[1]:.1f}")
    print(f"条件数: κ = {condition_number:.1f}")
    print(f"矩阵性质: 正定对角矩阵")
    
    print(f"\n收敛性质:")
    print(f"- 对于二次函数: 理论上1次迭代收敛")
    print(f"- 收敛类型: 二次收敛")
    print(f"- 收敛域: 全局（对于凸函数）")
    print(f"- 数值稳定性: 优秀（条件数较小）")

def main():
    """主函数"""
    # 理论分析
    x_theory, f_theory = verify_theoretical_solution()
    
    # 手工计算演示
    manual_calculation_demo()
    
    # 收敛性质分析
    analyze_convergence_properties()
    
    # 比较分析
    compare_with_steepest_descent()
    
    # 求解问题
    x0 = [0.0, 0.0]
    x_optimal = newton_method(x0, epsilon=1e-5)
    
    # 计算误差
    error = np.linalg.norm(x_optimal - x_theory)
    print(f"\n数值解与理论解误差: ||x_num - x_theory|| = {error:.8e}")
    
    # 算法性能总结
    print("\n" + "=" * 80)
    print("算法性能总结:")
    print("=" * 80)
    print(f"收敛性: {'✓ 成功收敛' if error < 1e-10 else '✗ 未收敛'}")
    print(f"迭代次数: 1次（理论预期）")
    print(f"数值精度: {error:.2e}")
    print(f"收敛类型: 二次收敛")
    print(f"算法效率: 对于二次函数最优")

if __name__ == "__main__":
    main()
