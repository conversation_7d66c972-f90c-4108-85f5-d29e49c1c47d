#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试三种优化算法的简化版本
"""

import numpy as np
import time

def objective_function(x):
    """目标函数"""
    x1, x2 = x[0], x[1]
    return 4*(x1 + 1)**2 + 2*(x2 - 1)**2 + x1 + x2 + 10

def gradient(x):
    """梯度"""
    x1, x2 = x[0], x[1]
    return np.array([8*x1 + 9, 4*x2 - 3])

def hessian(x):
    """Hessian矩阵"""
    return np.array([[8, 0], [0, 4]])

def steepest_descent(x0, epsilon=1e-5):
    """最速下降法"""
    x = np.array(x0, dtype=float)
    history = []
    
    print("最速下降法:")
    print(f"{'k':<3} {'x1':<10} {'x2':<10} {'f(x)':<12} {'||grad||':<12}")
    print("-" * 50)
    
    for k in range(100):
        grad = gradient(x)
        grad_norm = np.linalg.norm(grad)
        f_val = objective_function(x)
        
        history.append({'x': x.copy(), 'f': f_val, 'grad_norm': grad_norm})
        print(f"{k:<3} {x[0]:<10.6f} {x[1]:<10.6f} {f_val:<12.6f} {grad_norm:<12.6e}")
        
        if grad_norm < epsilon:
            print("收敛!")
            break
        
        # 精确线搜索
        H = hessian(x)
        alpha = np.dot(grad, grad) / np.dot(grad, np.dot(H, grad))
        x = x - alpha * grad
    
    return x, history

def newton_method(x0, epsilon=1e-5):
    """牛顿法"""
    x = np.array(x0, dtype=float)
    history = []
    
    print("\n牛顿法:")
    print(f"{'k':<3} {'x1':<10} {'x2':<10} {'f(x)':<12} {'||grad||':<12}")
    print("-" * 50)
    
    for k in range(100):
        grad = gradient(x)
        grad_norm = np.linalg.norm(grad)
        f_val = objective_function(x)
        
        history.append({'x': x.copy(), 'f': f_val, 'grad_norm': grad_norm})
        print(f"{k:<3} {x[0]:<10.6f} {x[1]:<10.6f} {f_val:<12.6f} {grad_norm:<12.6e}")
        
        if grad_norm < epsilon:
            print("收敛!")
            break
        
        # 牛顿方向
        H = hessian(x)
        newton_direction = np.linalg.solve(H, -grad)
        x = x + newton_direction
    
    return x, history

def conjugate_gradient(x0, epsilon=1e-5):
    """共轭梯度法"""
    x = np.array(x0, dtype=float)
    history = []
    
    print("\n共轭梯度法:")
    print(f"{'k':<3} {'x1':<10} {'x2':<10} {'f(x)':<12} {'||grad||':<12}")
    print("-" * 50)
    
    grad = gradient(x)
    direction = -grad  # 初始方向
    
    for k in range(100):
        grad_norm = np.linalg.norm(grad)
        f_val = objective_function(x)
        
        history.append({'x': x.copy(), 'f': f_val, 'grad_norm': grad_norm})
        print(f"{k:<3} {x[0]:<10.6f} {x[1]:<10.6f} {f_val:<12.6f} {grad_norm:<12.6e}")
        
        if grad_norm < epsilon:
            print("收敛!")
            break
        
        # 精确线搜索
        H = hessian(x)
        alpha = -np.dot(grad, direction) / np.dot(direction, np.dot(H, direction))
        
        # 更新点
        x_new = x + alpha * direction
        grad_new = gradient(x_new)
        
        # Fletcher-Reeves公式
        beta = np.dot(grad_new, grad_new) / np.dot(grad, grad)
        
        # 更新方向
        direction = -grad_new + beta * direction
        
        x = x_new
        grad = grad_new
    
    return x, history

def compare_methods():
    """比较三种方法"""
    x0 = np.array([0.0, 0.0])
    epsilon = 1e-5
    
    print("三种优化算法比较")
    print("=" * 60)
    print(f"目标函数: f(x) = 4(x1+1)² + 2(x2-1)² + x1 + x2 + 10")
    print(f"初始点: x0 = {x0}")
    print(f"收敛精度: ε = {epsilon}")
    print(f"理论最优解: x* = [-1.125, 0.75], f* = 9.8125")
    print("=" * 60)
    
    # 理论最优解
    x_theory = np.array([-1.125, 0.75])
    f_theory = objective_function(x_theory)
    
    # 运行三种算法
    methods = [
        ("最速下降法", steepest_descent),
        ("牛顿法", newton_method),
        ("共轭梯度法", conjugate_gradient)
    ]
    
    results = []
    
    for name, method in methods:
        start_time = time.time()
        x_opt, history = method(x0, epsilon)
        end_time = time.time()
        
        error = np.linalg.norm(x_opt - x_theory)
        results.append({
            'name': name,
            'x_opt': x_opt,
            'f_opt': objective_function(x_opt),
            'iterations': len(history),
            'time': end_time - start_time,
            'error': error
        })
    
    # 打印比较结果
    print("\n" + "=" * 80)
    print("算法性能比较")
    print("=" * 80)
    print(f"{'算法':<12} {'迭代次数':<8} {'最优值':<12} {'误差':<12} {'时间(s)':<10}")
    print("-" * 80)
    
    for result in results:
        print(f"{result['name']:<12} {result['iterations']:<8} "
              f"{result['f_opt']:<12.6f} {result['error']:<12.6e} "
              f"{result['time']:<10.6f}")
    
    print(f"{'理论值':<12} {'-':<8} {f_theory:<12.6f} {0:<12.6e} {'-':<10}")
    
    # 分析结果
    print("\n" + "=" * 80)
    print("结果分析:")
    print("=" * 80)
    
    fastest_convergence = min(results, key=lambda x: x['iterations'])
    fastest_time = min(results, key=lambda x: x['time'])
    most_accurate = min(results, key=lambda x: x['error'])
    
    print(f"收敛最快: {fastest_convergence['name']} ({fastest_convergence['iterations']}次迭代)")
    print(f"计算最快: {fastest_time['name']} ({fastest_time['time']:.6f}秒)")
    print(f"精度最高: {most_accurate['name']} (误差: {most_accurate['error']:.6e})")
    
    print("\n算法特点总结:")
    print("- 最速下降法: 实现简单，但收敛较慢")
    print("- 牛顿法: 收敛最快，一步到位")
    print("- 共轭梯度法: 平衡了收敛速度和计算成本")

def manual_calculation_demo():
    """手工计算演示"""
    print("\n" + "=" * 80)
    print("共轭梯度法手工计算演示:")
    print("=" * 80)
    
    x = np.array([0.0, 0.0])
    print(f"初始点: x⁽⁰⁾ = {x}")
    
    # 第一次迭代
    grad = gradient(x)
    print(f"初始梯度: ∇f(x⁽⁰⁾) = {grad}")
    
    direction = -grad
    print(f"初始方向: d⁽⁰⁾ = {direction}")
    
    H = hessian(x)
    alpha = -np.dot(grad, direction) / np.dot(direction, np.dot(H, direction))
    print(f"步长: α⁽⁰⁾ = {alpha:.6f}")
    
    x_new = x + alpha * direction
    print(f"更新点: x⁽¹⁾ = {x_new}")
    
    grad_new = gradient(x_new)
    print(f"新梯度: ∇f(x⁽¹⁾) = {grad_new}")
    
    beta = np.dot(grad_new, grad_new) / np.dot(grad, grad)
    print(f"共轭系数: β⁽¹⁾ = {beta:.6f}")
    
    direction_new = -grad_new + beta * direction
    print(f"新方向: d⁽¹⁾ = {direction_new}")
    
    print(f"函数值变化: f(x⁽⁰⁾) = {objective_function(x):.6f} → f(x⁽¹⁾) = {objective_function(x_new):.6f}")

if __name__ == "__main__":
    compare_methods()
    manual_calculation_demo()
