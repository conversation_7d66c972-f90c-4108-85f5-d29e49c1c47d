!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗智能问答系统 - 完善的启动脚本
检查依赖、初始化环境并启动系统
"""

import os
import sys
import subprocess
import importlib

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要Python 3.7或更高版本")
        return False
    print(f"✓ Python版本: {sys.version}")
    return True

def check_and_install_dependencies():
    """检查并安装依赖"""
    print("\n检查依赖包...")
    
    # 核心依赖包
    core_dependencies = [
        'flask',
        'torch',
        'transformers',
        'py2neo',
        'scikit-learn'
    ]
    
    # 可选依赖包
    optional_dependencies = [
        'flask-cors',
        'pyahocorasick',
        'seqeval',
        'tqdm',
        'pandas',
        'psutil'
    ]
    
    missing_core = []
    missing_optional = []
    
    # 检查核心依赖
    for package in core_dependencies:
        try:
            importlib.import_module(package.replace('-', '_'))
            print(f"  ✓ {package}")
        except ImportError:
            print(f"  ✗ {package} (核心依赖)")
            missing_core.append(package)
    
    # 检查可选依赖
    for package in optional_dependencies:
        try:
            importlib.import_module(package.replace('-', '_'))
            print(f"  ✓ {package}")
        except ImportError:
            print(f"  ⚠ {package} (可选依赖)")
            missing_optional.append(package)
    
    # 如果有缺失的核心依赖，尝试安装
    if missing_core:
        print(f"\n❌ 缺失核心依赖: {', '.join(missing_core)}")
        print("请手动安装: pip install " + " ".join(missing_core))
        return False
    
    # 提示可选依赖
    if missing_optional:
        print(f"\n⚠️ 缺失可选依赖: {', '.join(missing_optional)}")
        print("建议安装以获得完整功能: pip install " + " ".join(missing_optional))
        print("系统仍可正常运行，但部分功能可能受限")
    
    return True

def check_model_files():
    """检查模型文件"""
    print("\n检查模型文件...")
    
    required_files = [
        'model/best_roberta_rnn_model_ent_aug.pt',
        'tmp_data/tag2idx.npy'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path) / (1024 * 1024)  # MB
            print(f"  ✓ {file_path} ({size:.1f}MB)")
        else:
            print(f"  ✗ {file_path} (缺失)")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ 缺失模型文件: {missing_files}")
        print("系统将使用备用模式运行")
    
    return len(missing_files) == 0

def check_neo4j_connection():
    """检查Neo4j连接"""
    print("\n检查Neo4j连接...")
    
    try:
        import py2neo
        client = py2neo.Graph(
            'bolt://localhost:7687',
            user='neo4j',
            password='wangqi20040401',
            name='doctor'
        )
        
        # 测试连接
        result = client.run("RETURN 1 as test").data()
        if result:
            print("  ✓ Neo4j连接成功")
            
            # 获取数据统计
            count_query = "MATCH (n) RETURN count(n) as total"
            count_result = client.run(count_query).data()
            if count_result:
                total_nodes = count_result[0]['total']
                print(f"  ✓ 数据库包含 {total_nodes} 个节点")
            
            return True
        else:
            print("  ✗ Neo4j连接失败")
            return False
            
    except Exception as e:
        print(f"  ✗ Neo4j连接错误: {e}")
        print("  提示: 请确保Neo4j服务正在运行，并检查连接配置")
        return False

def check_templates():
    """检查模板文件"""
    print("\n检查模板文件...")
    
    required_templates = [
        'templates/base.html',
        'templates/index.html',
        'templates/login.html',
        'templates/register.html',
        'templates/chat.html',
        'templates/knowledge_graph.html',
        'templates/admin.html',
        'templates/404.html',
        'templates/500.html'
    ]
    
    missing_templates = []
    
    for template in required_templates:
        if os.path.exists(template):
            print(f"  ✓ {template}")
        else:
            print(f"  ✗ {template} (缺失)")
            missing_templates.append(template)
    
    if missing_templates:
        print(f"\n⚠️ 缺失模板文件: {missing_templates}")
        print("部分页面可能无法正常显示")
    
    return len(missing_templates) == 0

def create_directories():
    """创建必要的目录"""
    print("\n创建必要目录...")
    
    directories = [
        'tmp_data',
        'backups',
        'logs'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"  ✓ 创建目录: {directory}")
        else:
            print(f"  ✓ 目录已存在: {directory}")

def test_app_import():
    """测试应用导入"""
    print("\n测试应用导入...")
    
    try:
        from app import app
        print("  ✓ 主应用导入成功")
        
        # 检查蓝图注册
        blueprints = list(app.blueprints.keys())
        print(f"  ✓ 已注册蓝图: {blueprints}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ 应用导入失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("医疗智能问答系统 - 启动检查")
    print("=" * 60)
    
    # 执行各项检查
    checks = [
        ("Python版本", check_python_version),
        ("依赖包", check_and_install_dependencies),
        ("模型文件", check_model_files),
        ("Neo4j连接", check_neo4j_connection),
        ("模板文件", check_templates),
        ("应用导入", test_app_import)
    ]
    
    # 创建必要目录
    create_directories()
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"  ✗ {check_name}检查异常: {e}")
            results.append((check_name, False))
    
    # 总结检查结果
    print("\n" + "=" * 60)
    print("检查结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{check_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项检查通过")
    
    # 决定是否启动
    if passed >= total - 1:  # 允许一项检查失败
        print("\n🚀 系统检查基本通过，正在启动...")
        print("=" * 60)
        print("🏠 主页: http://localhost:5000")
        print("💬 智能问答: http://localhost:5000/chat")
        print("📊 知识图谱: http://localhost:5000/knowledge-graph")
        print("🔧 管理面板: http://localhost:5000/admin")
        print("=" * 60)
        print("按 Ctrl+C 停止服务")
        print("-" * 60)
        
        try:
            from app import app
            app.run(debug=True, host='0.0.0.0', port=5000)
        except KeyboardInterrupt:
            print("\n\n👋 服务已停止")
        except Exception as e:
            print(f"\n❌ 启动失败: {e}")
            return False
    else:
        print(f"\n❌ 检查失败项过多 ({total - passed} 项)，请修复问题后重试")
        return False
    
    return True

if __name__ == '__main__':
    main()
